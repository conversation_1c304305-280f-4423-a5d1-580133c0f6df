:root {
  /*--vdl-yellow: rgb(253, 212, 0);*/
  --vdl-yellow: hsla(50, 100%, 50%, 0.1);
  --vdl-light-blue:hsla(207, 97%, 29%, 0.1);
}

.orderStatus{
  display: flex;
  margin-bottom: 1em;
}
.orderStatus div{
  padding: 10px 5px;
}
.orderStatus div:first-child {
  width: 66px;
  line-height: 2.5em;
}

.orderStatus div:nth-child(2){
  width: 23%;
}

.orderStatus textarea {
  width: 100%;
  height: 2.5em;
}

.selectOrderStatus{
  padding: 0 5px;
}
.producttype{
  width: 100%;
}

.select2.select2-container{
  vertical-align: bottom;
  margin-right: 10px;
}

.product_no .description{
  margin-right: 10px;
}


.user-logo {
  float: left;
  padding: 8px 0 0 10px
}

.user-logo img {
  max-width: 170px;
  max-height: 61px;
}

#productloader img {
  max-width: 200px;
}

a.refreshbasket {
  text-decoration: none;
  font-size: 15px;
  padding: 3px;
}
