<?php

  use domain\machine\event\ProductChanged;

  AppModel::loadModelClass('ProductModel');

  class Product extends ProductModel {

    const STOCK_DEFAULT = 0;
    const ON_STOCK = 1;
    const OUT_OF_STOCK = 2;

    const STOCK_COLOR_RED = "rgb(255, 102, 0)";
    const STOCK_COLOR_GREEN = "#42c814";
    const STOCK_COLOR_DEFAULT = "#FFF";
    /**
     * possible status for product (0 = standaard, geen tekst tonen in frontend)
     * @var array|string[]
     */
    public static array $stocks = [
      self::STOCK_DEFAULT => "Standaard",
      self::ON_STOCK      => "Uit voorraad leverbaar",
      self::OUT_OF_STOCK  => "Tijdelijk niet op voorraad",
    ];

    public static function getStocks(): array {
      return [
        'STOCK_DEFAULT' => self::STOCK_DEFAULT,
        'ON_STOCK'      => self::ON_STOCK,
        'OUT_OF_STOCK'  => self::OUT_OF_STOCK];
    }

    /**
     * Prijs voor particulier voor dit product.
     */
    public function getBasketPricePart($btw = false, $size = 1, $extra_options = null, $organid = null): float {
      $price = $this->price_part;
      if ($this->discount == 1 && $this->price_discount_part > 0) {
        $price = $this->price_discount_part;
      }
      else {
        $price *= $this->getSellSize();
      }

      if ($btw) {
        $vat = $this->getVat();
        if ($vat != 0) {
          $price = (($vat / 100) + 1) * $price;
        }
      }

      if ($organid != null) {
        //klantprijs is leidend
        $pop = ProductOrganPrice::getPiecePrice($organid, $this->id, $size);
        if ($pop) {
          $price = $pop;
        }
        elseif ($this->discountgroup_id != null) { //geen gevonden...misschien een kortinggroep op product?
          $disc = DiscountgroupOrgan::find_by(['organisation_id' => $organid, 'discountgroup_id' => $this->discountgroup_id]);
          if ($disc && $disc->discount > 0) {
            $price = ((100 - $disc->discount) / 100) * $price;
          }
        }
      }

      return round($price, 2);
    }

    public function getPriceByUser($luser = null, $btw = false, $size = 1, $extra_options = []): float {
      if ($luser == null && isset($_SESSION['userObject'])) {
        $luser = $_SESSION['userObject'];
      }
      return $this->getBasketPricePart($btw, $size, $extra_options, $luser->organisation_id);
    }

    /**
     * Prijs voor particulier voor dit product.
     */
    public function getDiscountStructure($btw = false, $size = 1, $extra_options = null, $organid = null) {
      $result = [
        'type'      => 'default',
        'baseprice' => $this->price_part,
        'discount'  => null,
        'price'     => 0.00,
      ];
      $price = $this->price_part;
      if ($this->discount == 1 && $this->price_discount_part > 0) {
        $price = $this->price_discount_part;
      }
      else {
        $price *= $this->getSellSize();
      }

      if ($btw) {
        $vat = $this->getVat();
        if ($vat != 0) {
          $price = (($vat / 100) + 1) * $price;
        }
      }

      if ($organid != null) {
        //klantprijs is leidend
        $pop = ProductOrganPrice::getPiecePrice($organid, $this->id, $size);
        if ($pop) {
          $price = $pop;
          $result['type'] = 'product_organ_price';
          $result['discount'] = number_format(100 * ((($this->price_part - $price) / $this->price_part)), 2, '.', '');
        }
        elseif ($this->discountgroup_id != null) { //geen gevonden...misschien een kortinggroep op product?
          $disc = DiscountgroupOrgan::find_by(['organisation_id' => $organid, 'discountgroup_id' => $this->discountgroup_id]);
          if ($disc && $disc->discount > 0) {
            $price = ((100 - $disc->discount) / 100) * $price;
            $result['type'] = 'discountgroup';
            $result['discount'] = $disc->discount;
          }
        }
      }
      $result['price'] = round($price, 2);

      return $result;
    }

    /**
     * Overwrite url function only used in backend
     * @param bool $lang
     * @return string
     */
    public function getUrl($lang = false, ?int $category_id = null) {
      if (!$lang) {
        $lang = $_SESSION['lang'];
      }
      $product_url = PageMap::getUrl('M_PRODUCTS') . '/' . StringHelper::slugify($this->getName($lang)) . '?id=' . $this->id;
      // add the category id, this is needed because products can be in multiple categories
      if ($category_id) {
        $product_url .= '&catid=' . $category_id;
      }
      return $product_url;
    }

    public function getMainUrlThumbForShop(Site $site): string {

      $main_thumb_url = $this->getMainUrlThumb();
      if ($main_thumb_url !== false && file_exists(DIR_UPLOADS."images/catalog/".$this->main_image_thumb)) return $main_thumb_url;

      return $site->getTemplateUrl() . 'images/product-no-image.jpg';
    }

    public function getMainUrlForShop(Site $site): string {
      $main_url = $this->getMainUrlOrig();
      if ($main_url !== false && file_exists(DIR_UPLOADS."images/catalog/".$this->main_image_orig)) return $main_url;

      return $site->getTemplateUrl() . 'images/product-no-image.jpg';
    }

    public function save(&$errors = []) {
      $changed_values = $this->getChanged();
      $is_new = ($this->id === null);

      $save_result = parent::save($errors);

      if ($is_new) {
        (new ProductChanged($this))->created();
      }
      else {
        (new ProductChanged($this))->updated($changed_values);
      }

      return $save_result;
    }

    public function destroy() {
      (new ProductChanged($this))->destroyed();
      return parent::destroy();
    }


    /**
     * Url for editing a product, backend only
     *
     * @return string
     */
    public function getEditUrl() {
      return PageMap::getUrl('M_PRODUCTLIST') . '?action=productedit&id=' . $this->id;
    }

    /**
     * Get products for select2 array
     */
    public static function getProductsFlat($llang = 'nl', $filter = '') {
      $language = DbHelper::escape($llang);
      $query = <<<SQL
      SELECT product.id, product.code, product_content.name, product.vatgroup, product.stock_level, product.stock_level_max
        , product.price_on_request, product.not_in_backorder 
      FROM product
        JOIN product_content ON product_content.product_id = product.id AND locale='$language'
      WHERE void=0
      $filter
SQL;

      $result = DBConn::db_link()->query($query);
      $products_ar = [];
      while ($row = $result->fetch_assoc()) {
        $row['text'] = $row['code'] . ' - ' . $row['name'];
        if ($row['price_on_request'] == '1') {
          $row['text'] .= ' (PRIJS OP AANVRAAG)';
          $row['disabled'] = true;
        }
        if ($row['not_in_backorder'] == '1') {
          $row['text'] .= ' (NIET MEER LEVERBAAR)';
          $row['disabled'] = true;
        }
        $products_ar[] = $row;
      }
      return $products_ar;
    }

    /**
     * Get products for select2 array
     */
    public static function getProductsFlatSimple($llang = 'nl', $filter = '') {
      $language = DbHelper::escape($llang);
      $query = <<<SQL
      SELECT product.id, product.code, product_content.name, product.vatgroup
        , product.price_on_request, product.not_in_backorder 
      FROM product
        JOIN product_content ON product_content.product_id = product.id AND locale='$language'
      WHERE void=0
      $filter
SQL;

      $result = DBConn::db_link()->query($query);
      $products_ar = [];
      while ($row = $result->fetch_assoc()) {
        $row['text'] = $row['code'] . ' - ' . $row['name'];
        $products_ar[] = $row;
      }
      return $products_ar;
    }

    public static function getFromCodes(array $codes): array {
      $inClause = DbHelper::getSqlIn('code', $codes);
      return self::find_all("WHERE $inClause");
    }

    public function getWebshopPriceNetto(): float {
      if ($this->discountgroup_id != null) {
        $disc = $this->getWebshopDiscount();
        if ($disc && $disc > 0) {
          return ((100 - $disc) / 100) * $this->price_part;
        }
      }
      return $this->price_part;
    }

    public function getWebshopDiscount(): float {
      $disc = DiscountgroupOrgan::find_by(['organisation_id' => ($_SESSION['userObject']->organisation_id), 'discountgroup_id' => $this->discountgroup_id]);
      if (!$disc || $disc->discount < 0) {
        return 0;
      }
      return $disc->discount;
    }

    public static function getStockColor($stock = 0): string {
      switch ($stock) {
        case self::ON_STOCK:
          $color = self::STOCK_COLOR_GREEN;
          break;
        case self::OUT_OF_STOCK:
          $color = self::STOCK_COLOR_RED;
          break;
        default:
          $color = self::STOCK_COLOR_DEFAULT;
      }
      return $color;
    }

//    oude functie voor
//    public function getWebshopStock(): string {
//      $stocks = self::$stocks;
//      if (array_key_exists($this->stock, $stocks) && $this->stock != Product::STOCK_DEFAULT) {
//        $color = self::getStockColor($this->stock);
//        $html = "<div style='display: inline-block;margin-left: 1em; border-radius: 0.40em; background-color: " . $color . "; width: 0.8em; height: 0.8em'></div><span style='color: " . $color . "; font-size: 80%'> - ";
//        $html .= __($stocks[$this->stock]) . "</span>";
//        return $html;
//      }
//      return "";
//    }

    public function getWebshopStock(): string {
      $html = "";
      $stock_level = (int)$this->stock_level;
      $stock_level_max = (int)$this->stock_level_max;
      switch (true){
        case ($stock_level >=1 && $stock_level <= 5):
          $color = self::STOCK_COLOR_GREEN;
          $html = "<div style='display: inline-block;margin-left: 1em; border-radius: 0.40em; background-color: " . $color . "; width: 0.8em; height: 0.8em'></div><span style='color: " . $color . "; font-size: 80%'> - ";
          $html .= $stock_level ." ". __("stk op voorraad")." </span>";
          break;
        case ($stock_level > 5):
          $color = self::STOCK_COLOR_GREEN;
          $html = "<div style='display: inline-block;margin-left: 1em; border-radius: 0.40em; background-color: " . $color . "; width: 0.8em; height: 0.8em'></div><span style='color: " . $color . "; font-size: 80%'> - ";
          $html .="5+ ".__("stk op voorraad")."</span>";
          break;
        case ($stock_level <= 0 && $stock_level_max <= 0):
//          $color = self::STOCK_COLOR_RED;
//          $html = "<div style='display: inline-block;margin-left: 1em; border-radius: 0.40em; background-color: " . $color . "; width: 0.8em; height: 0.8em'></div><span style='color: " . $color . "; font-size: 80%'> - ";
//          $html .=__("tijdelijk niet op voorraad")."</span>";
          break;
      }

        return $html;
    }

    public function getStockColorIcon(): string {
      $stocks = self::$stocks;
      if (array_key_exists($this->stock, $stocks) && $this->stock != Product::STOCK_DEFAULT) {
        $color = $this->stock == self::ON_STOCK ? self::STOCK_COLOR_GREEN : self::STOCK_COLOR_RED;
        $icon = "<div style='display: inline-block;margin-left: 1em; border-radius: 0.40em; background-color: " . $color . "; width: 0.8em; height: 0.8em'></div><span style='color: " . $color . "; font-size: 80%'></span>";
        return $icon;
      }
      return "";
    }

    public function getWebshopStaffel(): string {
      if (!$this->staffel) return "";

      $staffel = "";
      foreach ($this->getStaffel() as $i => $item) {
        $staffel .= $i . __(" stuk á ") . number_format((float)$item, 2, '.', '') . "<br>";
      }
      return $staffel;
    }

  }