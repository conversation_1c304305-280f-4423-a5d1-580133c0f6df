<script setup>
import { watch, watchEffect } from "vue"
import { useTresContext } from "@tresjs/core"
import { useRouterStore } from "@/store/routerStore"
import { useConfigStore } from "@/store/configStore"
import { usePartThreeStore } from "@/store/part/partThreeStore"
import { usePartEditStore } from "@/store/part/partEditStore"
import { useInteractionStore } from "@/store/interactionStore"
import threeContext from "@/utils/three/threeContext"
import partContext from "@/utils/part/partContext"
import TheEditor from "@/components/canvas/objects/TheEditor.vue"
import TheEnviroment from "@/components/canvas/objects/TheEnviroment.vue"
import TheDrawing from "@/components/canvas/objects/TheDrawing.vue"
import { useHover } from "@/composables/hover"
import TrackballControls from "@/components/canvas/objects/controls/TrackballControls.vue"
import { useHotkey } from 'vuetify'

const routerStore = useRouterStore()
const configStore = useConfigStore()
const partThreeStore = usePartThreeStore()
const partEditStore = usePartEditStore()
const interactionStore = useInteractionStore()

const { scene, controls, raycaster, renderer } = useTresContext()
watchEffect(() => {
  if (scene.value && controls.value && raycaster.value && renderer.value) {
    threeContext.init(scene.value, controls.value, raycaster.value, renderer.value)
  }
})

const { startHovering, stopHovering } = useHover(onHover)
function onHover() {
  const hoveredObject = threeContext.getIntersectedChildOfCurrentObject()
  interactionStore.hoveredPartName = hoveredObject ? hoveredObject.name : null
}

// Determine if hover state should be enabled or disabled
function setupHovering(clickedPart = null) {
  if (!partThreeStore.currentPart) return
  const part = partContext.getPartByName(partThreeStore.currentPart.name)
  // Turn on hoverstate if no part is connected or part should show its children, otherwise turn it off
  if (!clickedPart && (!part || part.has_children)) startHovering()
  else stopHovering()
}

// Reevaluate hover state when the current focused part changes, a part is submitted or a part is clicked
watch(() => partThreeStore.currentPart, () => setupHovering())
watch(() => partEditStore.lastSubmittedPartNr, () => setupHovering())
watch(() => interactionStore.clickedPartName, (clickedPart) => setupHovering(clickedPart))

useHotkey('Esc', () => {
  if (interactionStore.invisibleParts.size) {
    interactionStore.resetState()
    interactionStore.setAllPartsVisible()
  }
  else routerStore.popPartFromRoute()
})
useHotkey(' ', () => interactionStore.togglePartVisible())
</script>

<template>
  <TresPerspectiveCamera enabled />
  <trackball-controls make-default @start="stopHovering(false)" @end="startHovering(false)"/>
  <the-drawing/>
  <the-enviroment/>
  <the-editor v-if="configStore.isAdmin" @start="stopHovering" @end="startHovering"/>
</template>