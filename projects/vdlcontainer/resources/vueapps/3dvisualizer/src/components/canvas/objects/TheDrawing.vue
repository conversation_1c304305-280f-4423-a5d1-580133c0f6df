<script setup>
import { useDrawingLoader } from '@/composables/drawing'
import { useRouterStore } from "@/store/routerStore"
import { usePartThreeStore } from "@/store/part/partThreeStore"
import { usePartEditStore } from "@/store/part/partEditStore"
import { useInteractionStore } from "@/store/interactionStore"
import { useDrawingStore } from "@/store/drawingStore";
import threeContext from '@/utils/three/threeContext'
import { addStationaryClickListener } from '@/utils/events/stationaryClick'
import { watch, shallowRef } from 'vue'
import { useTresContext } from "@tresjs/core"
import partContext from "@/utils/part/partContext"

const routerStore = useRouterStore()
const partThreeStore = usePartThreeStore()
const partEditStore = usePartEditStore()
const interactionStore = useInteractionStore()
const drawingStore = useDrawingStore()

// 3D-object to be displayed in the scene (see template)
const drawing = useDrawingLoader()
watch(drawing, async (newDrawing) => {
  threeContext.currentObject = newDrawing

  const numbers = threeContext.getPartNumbers()
  await drawingStore.importPartsFromNumbers(numbers)
  drawingStore.drawingInitialized = true

  routerStore.focusOnPartByInitialRoute()
})

// Add event listener to the renderer.domElement (the canvas) to listen for stationary clicks
const { renderer } = useTresContext()
watch(renderer, (value) => {
  value?.domElement.addEventListener("stationaryclick", onClick)
}, { immediate: true })

// Determine if click event should be enabled or disabled
const cleanupEventListener = shallowRef()
function setupClickEvent() {
  cleanupEventListener.value?.()

  if (!partThreeStore.currentPart || !renderer.value) return

  const part = partContext.getPartByName(partThreeStore.currentPart.name)

  // Only add event listener if no part is found or part should show its children
  if (!part || part.has_children) cleanupEventListener.value = addStationaryClickListener(renderer.value.domElement)
}

// Reevaluate click event when the current focused part changes or a part is submitted
watch(() => partThreeStore.currentPart, setupClickEvent)
watch(() => partEditStore.lastSubmittedPartNr, setupClickEvent)

function onClick(event) {
  // Determine which object was clicked
  const { clientX, clientY } = event.detail
  const objectToClick = threeContext.getIntersectedChildOfCurrentObject(clientX, clientY)

  if (!objectToClick) return interactionStore.resetState()

  // If the part was already clicked, focus on the part by pushing it to the route
  const partWasAlreadyClicked = interactionStore.clickedPartName === objectToClick.name
  if (partWasAlreadyClicked) routerStore.pushPartToRouteFromNr(objectToClick.name)

  else interactionStore.clickedPartName = objectToClick.name
}
</script>

<template>
  <primitive v-if="drawing" :object="drawing"/>
</template>