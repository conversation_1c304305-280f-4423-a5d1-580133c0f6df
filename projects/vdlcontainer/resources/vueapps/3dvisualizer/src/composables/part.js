import partContext from "@/utils/part/partContext"
import { getBaseName } from "@/utils/helpers"
import { usePartEditStore } from "@/store/part/partEditStore"
import { usePartOrderStore } from "@/store/part/partOrderStore"
import { ref, watch, onUnmounted } from "vue"

/**
 * A composable function to manage part-specific data, including display name and orderability.
 *
 * This composable reacts to changes in part data and updates the display name and orderable status.
 * It also allows dynamic updates to the part name.
 *
 * @param {string} partName - The initial name of the part to manage.
 *
 * @returns {{
 *   displayName: Ref<string>,
 *   isOrderable: Ref<boolean>,
 *   updatePartName: (newPartName: string) => void
 *   openPartEditDialog: () => void,
 *   openPartOrderDialog: () => void
 * }} An object containing:
 *   - `displayName`: A reactive reference to the part's display name.
 *   - `isOrderable`: A reactive reference indicating whether the part is orderable.
 *   - `updatePartName`: A function to update the part name and recalculate its state.
 */
export function usePart(partName) {
  const partEditStore = usePartEditStore()
  const partOrderStore = usePartOrderStore()

  function getDisplayNameFromPartName(partName) {
    const part = partContext.getPartByName(partName)
    return part ? `${part.name} (${part.part_nr})` : getBaseName(partName)
  }
  function isPartNameOrderable(partName) {
    const part = partContext.getPartByName(partName)
    return part && part.product
  }
  const displayName = ref(getDisplayNameFromPartName(partName))
  const isOrderable = ref(isPartNameOrderable(partName))
  // update display name when this part was edited or submitted
  watch(() => partEditStore.lastSubmittedPartNr, (value) => {
    if (value && String(value) === getBaseName(partName)) refresh()
  })
  function updatePartName(newPartName) {
    partName = newPartName
    displayName.value = getDisplayNameFromPartName(partName)
    isOrderable.value = isPartNameOrderable(partName)
  }
  function openPartEditDialog() {
    // if part does not exist, create a new part object with the part number
    const part = partContext.getPartByName(partName) ?? { part_nr: getBaseName(partName) }
    partEditStore.setPartToEdit(part)
  }
  function openPartOrderDialog() {
    const part = partContext.getPartByName(partName)
    partOrderStore.setPartToOrder(part)
  }
  function refresh() {
    displayName.value = getDisplayNameFromPartName(partName)
    isOrderable.value = isPartNameOrderable(partName)
  }

  partContext.onPartsRefreshed(refresh)
  onUnmounted(() => {
    partContext.cleanupPartsRefreshed(refresh)
  })
  return { displayName, isOrderable, updatePartName, openPartEditDialog, openPartOrderDialog }
}