<script setup>
import { useDrawingStore } from "@/store/drawingStore"
import { useRoute } from "vue-router"
import { computed, ref, onMounted, watch } from "vue"
import { useI18n } from 'vue-i18n'
import { useMachine } from "@/composables/machine"

const { t } = useI18n()
const drawingStore = useDrawingStore()
const route = useRoute()
const { machines, updateFilter } = useMachine()

const machineFilter = ref('')
watch(machineFilter, (newFilter) => {
  updateFilter(newFilter)
})

function saveDrawing() {
  drawingStore.saveDrawing(route.params.id)
}

function onInputChange() {
  drawingStore.drawingHasUnsavedChanges = true
}


// Handle machine selection
function onMachineSelect(machineOrderNr) {
  drawingStore.machineOrderNr = machineOrderNr || null
  drawingStore.drawingHasUnsavedChanges = true
}

watch(() => drawingStore.machineOrderNr, (newOrderNr) => {
  if (newOrderNr) machineFilter.value = newOrderNr
}, { immediate: true })

// If all contents are filled and there are unsaved changes, allow saving
const isAllowedToSave = computed(() => {
  const allContentsFilled = drawingStore.drawingContents.every(content => content.name)
  return allContentsFilled && drawingStore.drawingHasUnsavedChanges
})
</script>

<template>
  <v-card flat class="pa-4">
    <v-card-text>
      <v-form>
        <div
            v-for="content in drawingStore.drawingContents"
            :key="content.locale"
            class="mb-4"
        >
          <v-label class="text-h6 font-weight-medium mb-2 d-block">
            {{ t('name') }} ({{ content.locale.toUpperCase() }})
            <span class="text-error ml-1">*</span>
          </v-label>
          <v-text-field
              v-model="content.name"
              :placeholder="t('name')"
              variant="outlined"
              density="compact"
              required
              style="max-width: 400px;"
              @input="onInputChange"
          />
        </div>


        <v-label class="text-h6 font-weight-medium mb-2 d-block">
          {{ t('linkedMachine') }}
        </v-label>
        <v-autocomplete
            v-model="drawingStore.machineOrderNr"
            :items="machines"
            item-title="order_nr"
            item-value="order_nr"
            :placeholder="t('searchMachine')"
            variant="outlined"
            density="compact"
            clearable
            hide-details
            hide-no-data
            hide-selected
            :custom-filter="() => {}"
            @update:search="updateFilter"
            @update:model-value="onMachineSelect"
            :menu-props="{
              maxWidth: '100%',
              width: 'auto',
            }"
            style="max-width: 400px;"
        >
          <template #item="{ props, item }">
            <v-list-item v-bind="props">
              <v-list-item-subtitle class="text-truncate">{{ item.raw.description }}</v-list-item-subtitle>
            </v-list-item>
          </template>
        </v-autocomplete>
        <small v-if="drawingStore.machineOrderNr" class="machine-info">
          {{ t('currentlyLinked') }}: {{ drawingStore.machineOrderNr }}
        </small>

        <div class="mt-6">
          <v-btn
              color="primary"
              size="large"
              :disabled="!isAllowedToSave"
              @click="saveDrawing"
              prepend-icon="mdi-content-save"
          >
            {{ t('saveDrawing') }}
          </v-btn>
        </div>
      </v-form>
    </v-card-text>
  </v-card>
</template>

<style scoped>
button:disabled {
  cursor: not-allowed;
}
button:disabled {
  margin-top: 1em !important;
}
.default_table, .default_table td {
  border: 0 !important;
  border-bottom: 0 !important;
  width: auto !important;
}
.machine-info {
  display: block;
  color: #666;
  font-style: italic;
  margin-top: 5px;
}
#machine {
  width: 300px;
}
</style>