<?php

  use domain\machine\webshop\valueobject\MachineType;
  use domain\machine\service\MachineService;
  use domain\visualizer\service\VisualizerService;
  use domain\visualizer\VisualizerSharedActions;

  class webshop_machineVdlcontainerActions extends gsdActions {
    use VisualizerSharedActions;

    public function preExecute() {
      parent::preExecute();
    }

    public function executeSearch() {
      Context::addJavascript('/gsdfw/includes/jsscripts/vuejs/vue.3.2.31' . (DEVELOPMENT ? '' : '.min') . '.js');
    }

    public function executeView() {

      $machine = Machine::find_by_id($_GET['id']);

      if (!$machine) {
        MessageFlashCoordinator::addMessageAlert(__('Machine niet gevonden'));
        ResponseHelper::redirect(PageMap::getUrl('M_WEBSHOP_MACHINE_SEARCH'));
      }

      try {
        $machine_type = MachineType::createFromCodenr($machine->code_nr);
        $category_ids = $machine_type->getCategoryIdsOfType();
      }
      catch (GsdException $exception) {
        ResponseHelper::redirectError($exception->getMessage());
      }
      $categories = $this->getCategoriesWithProductAmount($machine->order_nr, $category_ids, $_SESSION['lang']);

      $machine_documents = MachineDocument::find_all_by(['machine_id' => $machine->id]);

      BreadCrumbs::getInstance()->removeLastItem();
      BreadCrumbs::getInstance()->addItem($machine->order_nr);

      $this->machine = $machine;
      $this->categories = $categories;
      $this->machine_documents = $machine_documents;

      Context::addJavascript(URL_INCLUDES . 'jsscripts/vuejs/vue.2.5.13' . (DEVELOPMENT ? '' : '.min') . '.js', false);
    }

    public function executeVisualizer() {
      $this->config = VisualizerService::getConfigFromSession();
    }

    public function executeCheckserialnr() {

      $this->template = null;

      //$_POST['order_nr'] = 2013252572; // for debugging

      $machine_order_nr = trim($_POST['order_nr'] ?? '');

      $machine_service = new MachineService();
      $machines = $machine_service::checkSerialNr($machine_order_nr);


//      if (empty($machine_order_nr) || !in_array(strlen($machine_order_nr), [5, 6, 10]) || is_numeric($machine_order_nr) === false) {
//        ResponseHelper::exitAsJson([
//          'result'  => false,
//          'message' => __('Een serienummer dient 5, 6 of 10 cijfers lang te zijn.'),
//        ]);
//      }
//
//      $machines = Machine::find_all("WHERE order_nr LIKE '%".escapeForDB($machine_order_nr)."'"); //alle machines die eindigen op dit getal
//
//      if(count($machines)==0) {
//        ResponseHelper::exitAsJson([
//          'result'  => false,
//          'message' => __('Er is geen machine gevonden met dit serienummer. Neem contact op met onze aftersales <NAME_EMAIL>'),
//        ]);
//      }
//
//      $message = "";
//      foreach($machines as $k=>$machine) {
//        try {
//          $machine_type = MachineType::createFromCodenr($machine->code_nr);
//
//          $machine_product_count = MachineProduct::count_all_by(['machine_order_nr' => $machine_order_nr]);
//          if($machine_product_count === 0) {
//            $message = __('Er is geen machine gevonden met dit serienummer. Neem contact op met onze aftersales <NAME_EMAIL>');
//            unset($machines[$k]);
//          }
//        }
//        catch (GsdException $exception) {
//          $message = $exception->getMessage();
//          unset($machines[$k]);
//        }
//      }
//
//      if(count($machines)==0) {
//        ResponseHelper::exitAsJson([
//          'result'  => false,
//          'message' => $message,
//        ]);
//      }

      foreach ($machines as $machine) {
        $machine->url = PageMap::getUrl('M_WEBSHOP_MACHINE_VIEW') . '?id=' . $machine->id;
      }

      ResponseHelper::exitAsJson([
        'result'   => true,
        'machines' => $machines,
      ]);
    }

    public function executeCategory() {

      $machine = Machine::find_by_id($_GET['id']);

      if (empty($_GET['cat'])) {
        MessageFlashCoordinator::addMessageAlert('Categorie niet gevonden');
        ResponseHelper::redirect(reconstructQueryAdd(['id']));
      }

      $active_category = Category::find_by(['id' => $_GET['cat'], 'online_custshop' => 1, 'void' => 0]);
      $active_category->findAndSetCategoryContent($_SESSION['lang']);
      if (!$active_category) {
        MessageFlashCoordinator::addMessageAlert('Categorie niet gevonden');
        ResponseHelper::redirect(reconstructQueryAdd(['id']));
      }

      $subcategories = Category::find_all_by([
        'parent_id'       => $active_category->id,
        'void'            => 0,
        'online_custshop' => 1,
      ]);

      if (!$subcategories) {
        $this->executeProductlist($machine, $active_category);
        return;
      }

      BreadCrumbs::getInstance()->removeLastItem();
      BreadCrumbs::getInstance()->addItem($machine->order_nr, PageMap::getUrl('M_WEBSHOP_MACHINE_VIEW') . '?id=' . $machine->id);
      $parents = Category::getParents($active_category);
      if ($parents != null) {
        foreach ($parents as $parent) {
          BreadCrumbs::getInstance()->addItem($parent->getName($_SESSION['lang']), '?cat=' . $parent->id . "&action=category&id=" . $machine->id);
        }
      }


      $category_ids = array_map(function (Category $category) {
        return $category->id;
      }, $subcategories);

      $sub_categories = $this->getCategoriesWithProductAmount($machine->order_nr, $category_ids, $_SESSION['lang']);

      $sub_category_ids = array_map(function (Category $category) {
        return $category->id;
      }, $subcategories);

      $zoom_images = CategoryImage::find_all_by([
        'category_id' => $sub_category_ids,
        'type'        => CategoryImage::TYPE_ZOOM,
      ]);
      $zoom_images = AppModel::mapObjectIds($zoom_images, 'category_id');

      $this->active_category = $active_category;
      $this->sub_categories = $sub_categories;
      $this->machine = $machine;
      $this->zoom_images = $zoom_images;
    }

    private function executeProductlist(Machine $machine, Category $active_category) {
      $query = "SELECT product.*, product_content.*, machine_product.amount as amount_in_machine ";
      $query .= ", COUNT(DISTINCT product_combi.id) as spareparts, COUNT(DISTINCT product_related.id) AS related_products ";
      $query .= " FROM machine_product ";
      $query .= "JOIN product ON machine_product.product_id = product.id AND product.online_custshop = 1 ";
      $query .= "LEFT JOIN product_content ON product_content.product_id = product.id ";
      $query .= "AND product_content.locale = '" . DbHelper::escape($_SESSION['lang']) . "' ";

      $query .= "JOIN category_product ON category_product.category_id = " . DbHelper::escape($active_category->id) . " ";
      $query .= "AND category_product.product_id = product.id AND category_product.online = 1 ";
      $query .= "LEFT JOIN product_combi ON product_combi.product_id = product.id ";
      $query .= "LEFT JOIN product_related ON product_related.product_id = product.id ";

      $query .= "WHERE machine_product.machine_order_nr = '" . DbHelper::escape($machine->order_nr) . "' ";
      $query .= "AND machine_product.amount > 0 ";
      $query .= "GROUP BY machine_product.product_id ";

      $result = DBConn::db_link()->query($query);

      $products = [];
      while ($row = $result->fetch_array()) {
        $column_counter = 0;
        $product = new Product();
        $product->hydrate($row, $column_counter);
        $column_counter += count(Product::columns);

        $product_content = new ProductContent();
        $product_content->hydrate($row, $column_counter);
        $column_counter += count(ProductContent::columns);

        $product->product_amount = $row[$column_counter++];
        $product->has_spareparts = ((int)$row['spareparts'] > 0) ? true : false;
        $product->has_related_products = ((int)$row['related_products'] > 0) ? true : false;

        $products[] = $product;
      }

      BreadCrumbs::getInstance()->removeLastItem();
      BreadCrumbs::getInstance()->addItem($machine->order_nr, PageMap::getUrl('M_WEBSHOP_MACHINE_VIEW') . '?id=' . $machine->id);

      $parents = Category::getParents($active_category);
      if ($parents != null) {
        foreach ($parents as $parent) {
          BreadCrumbs::getInstance()->addItem($parent->getName($_SESSION['lang']), '?cat=' . $parent->id . "&action=category&id=" . $machine->id);
        }
      }

      $this->machine = $machine;
      $this->active_category = $active_category;
      $this->products = $products;

      $this->template = 'productlistSuccess.php';
    }

    public function executeSearchMachineProductsAjax() {
      $query = "SELECT product.*, product_content.*, machine_product.amount as amount_in_machine ";
      $query .= "FROM machine_product ";
      $query .= "JOIN product ON machine_product.product_id = product.id AND product.online_custshop = 1 ";
      $query .= "LEFT JOIN product_content ON product_content.product_id = product.id ";
      $query .= "AND product_content.locale = '" . DbHelper::escape($_SESSION['lang']) . "' ";
      $query .= "WHERE machine_product.machine_order_nr = '" . DbHelper::escape($_POST['machine_order_nr']) . "' ";
      $query .= "AND machine_product.amount > 0 ";
      $query .= "AND product.online_custshop = 1 AND product.void=0 ";
      // search value can be empty, to show all products
      if (!empty(trim($_POST['product_search_value']))) {
        $query .= "AND (product.code LIKE '%" . DbHelper::escape(trim($_POST['product_search_value'])) . "%' ";
        $query .= "OR product_content.name LIKE '%" . DbHelper::escape(trim($_POST['product_search_value'])) . "%') ";
      }
      $query .= "GROUP BY machine_product.product_id ";

      $result = DBConn::db_link()->query($query);

      $products = [];
      while ($row = $result->fetch_row()) {
        $column_counter = 0;
        $product = new Product();
        $product->hydrate($row, $column_counter);
        $column_counter += count(Product::columns);

        $product_content = new ProductContent();
        $product_content->hydrate($row, $column_counter);
        $column_counter += count(ProductContent::columns);

        $product_amount_in_machine = $row[$column_counter++];

        $product_data = [
          'image'             => $product->getMainUrlForShop($_SESSION['site']),
          'image_thumb'       => $product->getMainUrlThumbForShop($_SESSION['site']),
          'url'               => $product->getUrl(),
          'name'              => $product_content->name,
          'code'              => $product->code,
          'price'             => StringHelper::getPriceDot($product->getPriceBruto()),
          'id'                => $product->id,
          'amount_in_machine' => $product_amount_in_machine,
          'size'              => '',
          'price_on_request'  => (int)$product->price_on_request,
          'not_in_backorder'  => (int)$product->not_in_backorder,
        ];

        $products[] = $product_data;
      }

      $this->template = null;

      ResponseHelper::exitAsJson([
        'result' => true,
        'data'   => $products,
      ]);

    }

    /**
     * Retrieve all categories from category_ids and the amount of products that is in the category or its subcategories
     *
     * @param        $machine_order_nr
     * @param array $category_ids
     * @param string $current_language
     * @return array
     */
    private function getCategoriesWithProductAmount($machine_order_nr, array $category_ids, string $current_language) {

      $machine_order_nr = DbHelper::escape($machine_order_nr);
      $current_language = DbHelper::escape($current_language);
      $category_ids = implode(',', $category_ids);

      // get the ids of all subcategories up to 2 levels deep
      $sub_category_ids_query = /** @lang MYSQL-SQL */
        <<<SQL
        SELECT
          CONCAT_WS(
            ',',
            GROUP_CONCAT(DISTINCT(category_lvl2.id)),
            GROUP_CONCAT(DISTINCT(category_lvl3.id)),
            GROUP_CONCAT(DISTINCT(category_lvl4.id))
          )
        FROM
          category AS category_lvl2
          LEFT JOIN category category_lvl3 ON category_lvl3.parent_id = category_lvl2.id
          LEFT JOIN category category_lvl4 ON category_lvl4.parent_id = category_lvl3.id
        WHERE
          category_lvl2.id = category.id
        GROUP BY
          category_lvl2.id
SQL;

      // get the categories from category ids, and check how many machine related products are in the category
      // or in the children categories
      $category_query = <<<SQL
        SELECT
          category.*,
          category_content.*,
          COUNT(DISTINCT(product.id)) as product_amount
        FROM
          category
          JOIN machine_product ON machine_product.machine_order_nr = $machine_order_nr
          JOIN product on product.id = machine_product.product_id
          AND product.void = 0
          AND product.online_custshop = 1
          JOIN category_product ON category_product.product_id = product.id
          AND category_product.void = 0
          AND FIND_IN_SET(
            category_product.category_id,($sub_category_ids_query)
          )
          JOIN category_content ON category_content.category_id = category.id
          AND category_content.locale = '$current_language'
        WHERE
          category.void = 0
          AND category.id IN ($category_ids)
        GROUP BY
          category.id
        ORDER BY
          category.sort
SQL;


      $result = DBConn::db_link()->query($category_query);
      $sub_categories = [];
      while ($row = $result->fetch_row()) {
        $column_counter = 0;
        $category = new Category();
        $category->hydrate($row, $column_counter);
        $column_counter += count(Category::columns);

        $category_content = new CategoryContent();
        $category_content->hydrate($row, $column_counter);
        $category->setCategoryContent($category_content);
        $column_counter += count(CategoryContent::columns);

        $category->amount_of_products = $row[$column_counter];
        $sub_categories[] = $category;
      }

      return $sub_categories;
    }

    public function executeWebshopmachinefiledownload(){
      if (!isset($_GET['fileid']) || $_GET['fileid'] == "") {
        throw new GsdException("Openen bestand zonder fileid?");
      }

      $file = MachineDocument::find_by_id($_GET['fileid']);

      if (!$file) {
        ResponseHelper::redirectNotFound("Het bestand wat u probeert te downloaden bestaat niet in de datbase.");
      }
      if (Privilege::hasRight('GLOBAL_ADMIN') === false && empty($_SESSION['userObject']->id)) {
        ResponseHelper::redirectAccessDenied();
      }
      $force_download = !empty($_GET['forcedownload']);

      $f_location = DIR_UPLOADS."machine_documents/".$file->filelocation;

      if (!$file || $file->filelocation == "" || !file_exists($f_location)) {
        ResponseHelper::redirectNotFound("Het bestand wat u probeert te downloaden bestaat niet op de server.");
      }

      FileHelper::getFileAndOuput($f_location, "", $force_download);
      ResponseHelper::exit();
    }
  }