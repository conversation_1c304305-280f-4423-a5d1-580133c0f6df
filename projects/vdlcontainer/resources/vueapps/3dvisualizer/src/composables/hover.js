import { onBeforeUnmount, onMounted } from 'vue'

const INACTIVITY_DURATION = 300 // Duration to consider mouse inactive (in milliseconds)
const HOVER_INTERVAL_DURATION = 100 // Duration for hover action checks, lower value is more reactive but can cause more lag on slower devices

/**
 * A composable to manage hover-related behavior and actions.
 * Tracks mouse movements, executes an interval action when the mouse is moving,
 * and manages event listeners for hover detection.
 *
 * @param {Function} intervalAction - A function to be executed at regular intervals when hovering.
 * @returns {Object} - Public methods to start and stop hovering behavior.
 */
export function useHover(intervalAction) {
  // properties are non-reactive because <PERSON><PERSON> doesn't need to watch them
  let cleanupHovering = null
  let hoverInterval = null
  let moveTimeout = null
  let mouseMoved = false
  let hoveringDisabled = false

  // Setup interval for hover action
  function setupHovering() {
    hoverInterval ??= setInterval(() => {
      if (mouseMoved) intervalAction() // Execute the action if mouse has moved
    }, HOVER_INTERVAL_DURATION)

    // Return cleanup function to avoid memory leaks
    return () => {
      clearInterval(hoverInterval)
      hoverInterval = null
    }
  }

  // Start hovering if not disabled
  function startHovering(strict = false) {
    if (strict) hoveringDisabled = false
    if (!hoveringDisabled) cleanupHovering ??= setupHovering()
  }

  // Stop hovering if not disabled
  function stopHovering(strict = false) {
    if (strict) hoveringDisabled = true
    if (cleanupHovering) {
      cleanupHovering() // Call the cleanup function
      cleanupHovering = null // Reset the ref
    }
  }

  function onMouseMove() {
    mouseMoved = true // Set mouse moved to true on movement
    resetMoveTimeout() // Reset the inactivity timeout
  }

  function resetMoveTimeout() {
    clearTimeout(moveTimeout)
    moveTimeout = setTimeout(() => {
      mouseMoved = false // Mark mouse as inactive after inactivity
    }, INACTIVITY_DURATION)
  }

  // Set up event listeners for mouse movement in the canvas
  let canvas
  onMounted(() => {
    startHovering(true)
    canvas = document.querySelector('canvas')
    canvas?.addEventListener('mousemove', onMouseMove)
  })

  onBeforeUnmount(() => {
    stopHovering(true) // Ensure cleanup on unmount, if it was not called already
    canvas?.removeEventListener('mousemove', onMouseMove)
  })

  // Return public methods for external use (strict mode is the default)
  return {
    startHovering: (strict = true) => startHovering(strict),
    stopHovering: (strict = true) => stopHovering(strict),
  }
}