<?php
  Config::set('IS_DEALER_PORTAL', true); // needs to be set before headercode include, because else it won't be available in the action
  include("gsdfw/includes/headercode_backend.inc.php");
  include(DIR_PROJECT_FOLDER . "classes/DealerPortalApp.php");
  (new DealerPortalApp())->pageInit();

?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="<?php echo $_SESSION['lang'] ?>"
      xml:lang="<?php echo $_SESSION['lang'] ?>">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
  <meta http-equiv="expires" content="-1" />
  <meta http-equiv="pragma" content="no-cache" />
  <title><?php echo $seo_title ?></title>
  <meta name="description" content="<?php echo strip_tags(escapeForInput($seo_description)) ?>" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />

  <link rel="shortcut icon" href="<?php echo $site->getTemplateUrl() ?>images/favicon.ico" type="image/x-icon" />

  <link rel="preconnect" href="https://fonts.gstatic.com">
  <link href="https://fonts.googleapis.com/css2?family=Lato:wght@400;700&display=swap" rel="stylesheet">
  <link href="//fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet" />

  <script type="text/javascript" src="//cdn.jsdelivr.net/es6-promise/latest/es6-promise.auto.min.js"></script>

  <?php echo TemplateHelper::includeJavascript('/gsdfw/projects/default/templates/backend/dist/deploy', true); ?>
  <?php echo TemplateHelper::includeStylesheet($site->getTemplateUrl().'dist/main', true); ?>

  <?php echo TemplateHelper::includeJavascript('/gsdfw/includes/jsscripts/general', true); ?>

  <script type="text/javascript">
    let main_css_url = '<?php echo $site->getTemplateUrl().'dist/main' . (!DEVELOPMENT ? '.min' : '').'.css?v='.get_asset_version(DIR_ROOT . $site->getTemplateUrl() . 'dist/main' . (!DEVELOPMENT ? '.min' : '') . '.css'); ?>';
  </script>

  <script type="text/javascript" src="<?php echo $site->getTemplateUrl() ?>components/floating_flash_message.js" defer></script>
  <script type="text/javascript" src="<?php echo $site->getTemplateUrl() ?>components/modal.js" defer></script>
  <script type="text/javascript" src="<?php echo $site->getTemplateUrl() ?>components/link-confirm-modal.js" defer></script>
  <script type="text/javascript" src="<?php echo $site->getTemplateUrl() ?>components/popout-box.js"></script>
  <script type="text/javascript" src="<?php echo $site->getTemplateUrl() ?>components/modal-trigger.js" defer></script>

  <?php if(file_exists($site->getTemplateDir() . 'js/project.js')): ?>
    <?php echo TemplateHelper::includeJavascript($site->getTemplateUrl() . 'js/project'); ?>
  <?php endif; ?>

  <?php Context::printJavascripts() ?>
  <?php Context::printStylesheets() ?>

  <?php if(!DEVELOPMENT): ?>
    <!-- Global site tag (gtag.js) - Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-HX5WKCBSC2"></script>
    <script>
      window.dataLayer = window.dataLayer || [];

      function gtag() {
        dataLayer.push(arguments);
      }

      gtag('js', new Date());
      gtag('config', 'G-HX5WKCBSC2');

      (function(h,o,t,j,a,r){
        h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
        h._hjSettings={hjid:3212956,hjsv:6};
        a=o.getElementsByTagName('head')[0];
        r=o.createElement('script');r.async=1;
        r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
        a.appendChild(r);
      })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');

    </script>
  <?php endif; ?>
</head>
<body class="<?php echo strtolower($current_action->getPageId()) ?>">

<?php TemplateHelper::writeStagingMessage() ?>

<?php include('_header.php'); ?>
<?php $pageId = $pageId=="M_EXTERNAL"?"M_HOME":$pageId ?>
<?php if(Navigation::getInstance()->getWritenav($pageId) == true ): ?>

  <div class="px-4 mb-4">
    <div class="container bg-white mt-12 px-3 md:px-8 py-4 shadow-md border border-gray-300">
      <div class="breadcrumb">
        <img src="<?php echo $site->getTemplateUrl() ?>images/icons/home.svg" class="w-4">
        <?php echo BreadCrumbs::writeBreadcrumbs('', true) ?>
      </div>

      <div class="content">
        <?php MessageFlashCoordinator::showMessages(); ?>
        <?php include($current_action->getTemplatePath()); ?>
      </div>

      <footer class="text-xs text-gray-600 mt-12 text-center">
        <div class="footermenu">
          VDL Container Systems bv | Industrieweg 21 | 5527 AJ Hapert | <?php echo __('Nederland') ?>
          <?php if(Navigation::getInstance()->getWritenav($pageId) == true): ?> |
            <a href="<?php echo $site->getTemplateUrl() ?>files/algemene-voorwaarden.pdf?v=2"
               target="_blank"><?php echo __("Algemene voorwaarden"); ?></a> |
            <a href="//www.vdlcontainersystems.com/nl/privacy-policy"
               target="_blank"><?php echo __("Privacy policy"); ?></a>
          <?php endif; ?>
        </div>

        <div class="footer">
          vdldealer.com <?php echo __("is een product van"); ?>
          <a href="//www.vdlcontainersystems.com" target="_blank">VDL Container Systems bv</a>
          - <?php echo __("Op het gebruik van"); ?>
          vdldealer.com <?php echo __("zijn de algemene voorwaarden van toepassing"); ?> - Powered by
          <a href="https://www.gsd.nl" title="GSD - maatwerk software" target="_blank">GSD</a>
        </div>
      </footer>

    </div>
  </div>
<?php endif; ?>

<script type="text/javascript">
  function toggleSidebarMenu() {
    if(document.getElementById('sidebar-box').classList.contains('open')) {
      document.getElementById('sidebar-box').classList.remove('open');
    }
    else {
      document.getElementById('sidebar-box').classList.add('open');
    }
  }
</script>


</body>
</html>
<style>

  .dealerportal-sale{
    color:rgb(0, 44, 81,1);
    font-size: 1.5em;
    text-align: center;
    width: 100%;
    height: 100px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .dealerportal-sale-btn {
    position: relative;
    color:rgb(255,255,255,1);
    width: auto;
    height: 64px;
    padding: 0 0.5em;
    line-height: 64px;
    transition: all 0.3s;
    background-color: rgb(191, 6, 6,1);
    border-radius: 15px;
    cursor: pointer;
  }
  .dealerportal-sale-btn:hover{
    background-color:rgb(191, 6, 6,0);
    color:rgb(191, 6, 6,1);
  }

  .dealerportal-sale-btn span {
    transition: all 0.3s;
    tranform: scale(1, 1);
  }

  .dealerportal-sale-btn a{
    padding: 0 2em;
    transition: all 0.3s;
    tranform: scale(1, 1);
  }

  .dealerportal-sale-btn::before, .dealerportal-sale-btn::after {
    content: '';
    position: absolute;
    transition: all 0.3s;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    transition: all 0.3s;
    border: 1px solid  rgb(230, 150, 14);
    border-radius: 15px;

  }

  .dealerportal-sale-btn:hover::after {
    animation-name: rotatecw;
    animation-duration: 2s;
  }
  .dealerportal-sale-btn::before {
    /*animation-name: rotateccw;*/
    /*animation-duration: 3s;*/
  }

  .dealerportal-sale-btn:hover::after,.dealerportal-sale-btn:hover::before {
    left: 80%;
    width: 64px;

    animation-iteration-count: infinite;
    animation-timing-function: linear;
  }

  @keyframes rotatecw {
    from {transform: rotate(0deg);}
    to {transform: rotate(360deg);}
  }

  @keyframes rotateccw {
    from {transform: rotate(0deg);}
    to {transform: rotate(-360deg);}
  }
</style>