<script setup>
import { watch } from "vue"
import { useConfigStore } from "@/store/configStore"
import { usePartThreeStore } from "@/store/part/partThreeStore"
import { usePart } from "@/composables/part"
import { useI18n } from "vue-i18n"

const configStore = useConfigStore()
const partThreeStore = usePartThreeStore()

const { t } = useI18n()
const { displayName, isOrderable, updatePartName, openPartEditDialog, openPartOrderDialog } =
    usePart(partThreeStore.currentPart?.name || '')

watch(() => partThreeStore.currentPart, () => {
  updatePartName(partThreeStore.currentPart?.name || '')
})
</script>

<template>
  <header v-if="displayName" class="d-flex justify-space-between align-center my-3 mx-0">
    <h3 class="flex-grow-1 d-flex align-center" style="word-break: break-word; min-height: 2.5em;">
      {{ t('part') }}: <span class="font-weight-bold ml-1">{{ displayName }}</span>
    </h3>

    <div class="d-flex ga-3 align-center ml-4" style="gap: 0.3em;">
      <v-icon
          v-if="configStore.isAdmin"
          size="large"
          @click.stop="openPartEditDialog"
          class="cursor-pointer pa-2"
        >
        mdi-pencil
      </v-icon>

      <v-icon
          v-if="isOrderable"
          size="large"
          @click.stop="openPartOrderDialog"
          class="cursor-pointer pa-2"
      >
        mdi-basket
      </v-icon>
    </div>
  </header>
</template>