<script setup>
import { ref, watch } from 'vue'
import { useI18n } from "vue-i18n"
import { usePartEditStore } from '@/store/part/partEditStore'
import { useDrawingStore } from '@/store/drawingStore'

const partEditStore = usePartEditStore()
const drawingStore = useDrawingStore()

const { t } = useI18n()
const searchTerm = ref('')

function onClick(part) {
  partEditStore.setPartToEdit({ part_nr: part })
}

watch(() => partEditStore.lastSubmittedPartNr, () => drawingStore.retrieveUnlinkedParts())
</script>

<template>
  <h3 class="mt-2 d-flex align-center justify-space-between">
    {{ t('unlinkedParts') }} ({{ drawingStore.unlinkedParts.length }})
  </h3>
  <div class="d-flex flex-column">
    <v-text-field
        v-model="searchTerm"
        :placeholder="t('searchUnlinkedParts')"
        variant="outlined"
        density="compact"
    />
    <ul class="unlinked-parts-list">
      <li
          v-for="part in drawingStore.unlinkedParts"
          :key="part"
          class="unlinked-part-item"
          @click="onClick(part)"
      >
        {{ part }}
      </li>
    </ul>
  </div>
</template>

<style scoped>
.unlinked-parts-list {
  padding: 0;
  margin: 0;
  list-style-type: none;
  overflow-y: auto;
  overflow-wrap: break-word;
  height: 27.5em;
}
.unlinked-part-item {
  padding: 12px 16px;
  border-bottom: 1px solid #e0e0e0;
  cursor: pointer;
}

.unlinked-part-item:hover {
  background-color: #f5f5f5;
}
</style>