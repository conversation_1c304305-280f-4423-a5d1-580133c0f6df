<?php
  /**
   * Definier hier de koppeling tussen pageId en url/module/action
   * 'def' = het unieke url gedeelte (verplicht)
   * 'name' = leesbare naam. Vetaling word automatisch opgezocht. (niet-verplicht, wel beter voor performance)
   * 'module' = welke module te laden
   * 'action' = welke actie te laden (default)
   * 'params' = welke parameters mee te nemen bij het bouwen van de url
   * 'target' = bij klik op link waar openen (_blank)
   */

  return [
//    'M_STATS_OFFERTE'                 => [
//      'def'    => 'offerte-statistieken',
//      'name'   => 'Offerte statistieken',
//      'module' => 'stats',
//      'action' => 'firstchild',
//    ],
    'M_ORDER_OV'                          => [
      'def'    => 'bestellingen-nav',
      'name'   => 'Spareparts',
      'module' => 'order',
      'action' => 'firstchild',
      'icon'   => 'orders.svg',
    ],
    'M_ORDER_REPEAT'                      => [
      'def'    => 'periodieke-offertes',
      'name'   => 'Onderhoudscontracten ',
      'module' => 'service',
      'action' => 'repeatlist',
    ],
    'M_API'                               => [
      'def'    => 'api',
      'name'   => 'VDL document uploads',
      'module' => 'api',
      'action' => 'authenticate',
    ],
    'M_STATS_PRODUCTSEXP'                 => [
      'def'    => 'rapportage-product-uitgebreid',
      'name'   => 'Producten uitgebreid',
      'module' => 'stats',
      'action' => 'productsexp',
    ],
    'M_CATALOG_RIGHTS_GROUP'              => [
      'def'    => 'catalogus-bekijk-rechten',
      'name'   => 'Rechten groepen',
      'module' => 'catalog',
      'action' => 'rightsgrouplist',
    ],
    'M_GUARANTEE'                         => [
      'def'    => 'garantie',
      'name'   => 'Garantie',
      'module' => 'guaranteeclaim',
      'action' => 'firstchild',
    ],
    'M_GUARANTEE_CLAIMS'                  => [
      'def'    => 'garantie-aanvragen',
      'name'   => 'Garantie aanvragen',
      'module' => 'guaranteeclaim',
      'action' => 'list',
    ],
    'M_GUARANTEE_STATS'                   => [
      'def'    => 'garantie-statistieken',
      'name'   => 'Garantie statistieken',
      'module' => 'guaranteeclaim',
      'action' => 'firstchild',
    ],
    'M_GUARANTEE_STATS_PRODUCTS'          => [
      'def'    => 'garantie-statistieken-producten',
      'name'   => 'Producten',
      'module' => 'guaranteeclaim',
      'action' => 'productstats',
    ],
    'M_GUARANTEE_STATS_PRODUCTS_EXPANDED' => [
      'def'    => 'garantie-statistieken-producten-uitgebreid',
      'name'   => 'Producten uitgebreid',
      'module' => 'guaranteeclaim',
      'action' => 'productstatsexpanded',
    ],
    'M_GUARANTEE_STATS_COSTS_BY_CUSTOMER' => [
      'def'    => 'garantie-statistieken-kosten-per-klant',
      'name'   => 'Garantie kosten per klant',
      'module' => 'guaranteeclaim',
      'action' => 'coststats',
    ],

    'M_GUARANTEE_STATS_GROUPED' => [
      'def'    => 'garantie-statistieken-groepen',
      'name'   => 'Groepen',
      'module' => 'guaranteeclaim',
      'action' => 'groupedstats',
    ],

    'M_SERVICE_NAV'  => [
      'def'    => 'service-nav',
      'name'   => 'Service',
      'module' => 'service',
      'action' => 'firstchild',
    ],
    'M_SERVICE'      => [
      'def'    => 'service',
      'name'   => 'Service',
      'module' => 'service',
      'action' => 'list',
    ],
    'M_PLANNING'     => [
      'def'    => 'planning',
      'name'   => 'Planning',
      'module' => 'planning',
      'action' => 'list',
    ],
    'M_MACHINES_NAV' => [
      'def'    => 'machines-nav',
      'name'   => 'Machines',
      'module' => 'machine',
      'action' => 'firstchild',
    ],

    'M_MACHINES' => [
      'def'    => 'machines',
      'name'   => 'Machines',
      'module' => 'machine',
      'action' => 'list',
    ],

    'M_MACHINES_IMPORT' => [
      'def'    => 'machines-import',
      'name'   => 'Machines import',
      'module' => 'machine',
      'action' => 'import',
    ],

    'M_MACHINES_IMPORT_LOG' => [
      'def'    => 'machines-import-logboek',
      'name'   => 'Machines import logboek',
      'module' => 'machine',
      'action' => 'importlog',
    ],

    'M_ORDER_EXPORT_VBS' => [
      'def'    => 'offerte-export-vbs',
      'name'   => 'Export naar VBS',
      'module' => 'order',
      'action' => 'exportlist',
    ],

    'M_DOWNLOADS_PRODUCTINFO' => [
      'def'    => 'download-productinfo',
      'name'   => 'Download Productinfo',
      'plugin' => 'download',
      'module' => 'downloadadmin',
      'action' => 'listProductinfo',
    ],

    'M_VISUALIZER' => [
      'def'    => 'visualizer',
      'name'   => 'Visualizer',
      'module' => 'visualizer',
      'action' => 'firstchild',
    ],

    'M_VISUALIZER_VIEWER' => [
      'def'    => 'visualizer-viewer',
      'name'   => 'Viewer',
      'module' => 'visualizer',
      'action' => 'viewer',
    ],

    'M_VISUALIZER_DRAWINGS' => [
      'def'    => 'visualizer-tekeningen',
      'name'   => 'Tekeningen',
      'module' => 'visualizer',
      'action' => 'drawings',
    ],

    'M_VISUALIZER_IMPORT' => [
      'def'    => 'visualizer-import',
      'name'   => 'Import',
      'module' => 'visualizer',
      'action' => 'import',
    ],

    'M_VISUALIZER_IMPORT_LOG' => [
      'def'    => 'visualizer-import-log',
      'name'   => 'Import logboek',
      'module' => 'visualizer',
      'action' => 'importlog',
    ],

    /*********************
     * WEBSHOP specific modules
     *********************/

    'M_WEBSHOP_MACHINE_SEARCH' => [
      'def'    => 'machine-search',
      'name'   => 'Machines',
      'module' => 'webshop_machine',
      'action' => 'search',
    ],

    'M_WEBSHOP_MACHINE_VIEW' => [
      'def'    => 'machine',
      'name'   => 'Machine',
      'module' => 'webshop_machine',
      'action' => 'view',
    ],

    'M_WEBSHOP_VISUALIZER' => [
      'def'    => 'visualizer-webshop',
      'name'   => 'Visualizer',
      'module' => 'webshop_machine',
      'action' => 'visualizer',
    ],

    // also on gsdfw level, but we add this for different name/def
    'M_PRODUCTS'   => [
      'def'    => 'webshop',
      'name'   => 'Webshop',
      'module' => 'products',
      'action' => 'list',
    ],

    'M_WEBSHOP_FAQ' => [
      'def'    => 'portal-faq',
      'name'   => 'Faq',
      'module' => 'webshop_faq',
      'action' => 'list',
    ],

    'M_WEBSHOP_ORDER' => [
      'def'    => 'portal-bestellingen',
      'name'   => 'Webshop_Bestellingen', // for translation
      'module' => 'webshop_order',
      'action' => 'list',
    ],

    'M_WEBSHOP_HELPDESK'              => [
      'def'    => 'helpdesk',
      'name'   => 'Helpdesk',
      'module' => 'webshop_contact',
      'action' => 'firstchild',
    ],
    'M_WEBSHOP_CUSTOMER_REQUEST'      => [
      'def'    => 'customer_request',
      'name'   => 'Aanvraag',
      'module' => 'webshop_customer_request',
      'action' => 'customer_request',
    ],
    'M_WEBSHOP_CONTACT'               => [
      'def'    => 'contact',
      'name'   => 'Contact',
      'module' => 'webshop_contact',
      'action' => 'contact',
    ],
    'M_WEBSHOP_DOWNLOADS_NAV'         => [
      'def'    => 'documenten-nav',
      'name'   => 'Productinfo',
      'module' => 'webshop_download',
      'action' => 'firstchild',
    ],
    'M_WEBSHOP_DOWNLOADS'             => [
      'def'    => 'documenten',
      'name'   => 'Documenten',
      'module' => 'webshop_download',
      'action' => 'list',
    ],
    'M_WEBSHOP_DOWNLOADS_PRODUCTINFO' => [
      'def'    => 'productinfo',
      'name'   => 'Productinfo',
      'module' => 'webshop_download',
      'action' => 'listProductinfo',
    ],

    'M_SAVED_BASKETS'     => [
      'def'    => 'bewaarde-winkelwagens',
      'name'   => 'Bewaarde winkelwagens',
      'module' => 'webshop_order',
      'action' => 'savedbaskets',
    ],
    'M_WEBSHOP_GUARANTEE' => [
      'def'    => 'portal-garanties',
      'name'   => 'webshop_garanties', // for translation
      'module' => 'webshop_guarantee',
      'action' => 'list',
    ],
    'M_BASKET'            => [
      'def'    => 'winkelwagen',
      'name'   => 'Winkelwagen',
      'module' => 'basket',
      'action' => 'list',
    ],

    'M_VDL_GPT' => [
      'def'    => 'gpt',
      'plugin' => 'gpt',
      'name'   => 'GPT',
      'module' => 'gpt',
      'action' => 'firstchild',
    ],

    'M_VDL_GPT_CHAT' => [
      'def'    => 'gpt-chat',
      'name'   => 'Chat',
      'plugin' => 'gpt',
      'module' => 'gpt',
      'action' => 'chat',
    ],

    'M_VDL_GPT_FILES' => [
      'def'    => 'gpt-files',
      'name'   => 'Bestanden',
      'plugin' => 'gpt',
      'module' => 'gpt',
      'action' => 'files',
    ],

    'M_VDL_GPT_HISTORY' => [
      'def'    => 'gpt-history',
      'name'   => 'Geschiedenis',
      'plugin' => 'gpt',
      'module' => 'gpt',
      'action' => 'history',
    ],

  ];