let modal_template = `
  <div class="modal-container">
  <div class="modal-overlay close-modal-overlay"></div>
    <div class="modal-box">

      <div class="modal-header">
        <h2>
          <slot name="title">Titel</slot>
        </h2>
        <a class="close-modal-cross">
          <svg style="width: 24px; height: 24px;" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
            <path
              d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z" />
          </svg>
        </a>
      </div>

      <div class="modal-content">
        <slot></slot>
      </div>
    </div>
  </div>
  
  <style>
    :host([fullwidth]) .modal-box {
      width: auto;
      max-width: 90%;
    }
  </style>
  `;


class BasicModal extends HTMLElement {
  constructor() {
    super();
    this.shadow = this.attachShadow({mode: "open"});
    this.main_css_url = main_css_url;
    this.close = this.close.bind(this);
    this.watchEscape = this.watchEscape.bind(this);
  }

  static get observedAttributes() {
    return ['open'];
  }

  // watch attribute changes
  attributeChangedCallback(attrName, oldValue, newValue) {
    if (oldValue !== newValue) {
      this[attrName] = this.hasAttribute(attrName);
    }
  }


  connectedCallback() {
    this.shadowRoot.innerHTML = modal_template;
    // add global styling to shadow root
    let link_main_css = document.createElement('link');
    link_main_css.rel = 'stylesheet';
    link_main_css.href = main_css_url;
    this.shadowRoot.appendChild(link_main_css);

    this.shadowRoot.querySelector('.close-modal-cross').addEventListener('click', this.close);
    this.shadowRoot.querySelector('.close-modal-overlay').addEventListener('click', this.close);
  }

  disconnectedCallback() {
    this.shadowRoot.querySelector('.close-modal-cross').removeEventListener('click', this.close);
    this.shadowRoot.querySelector('.close-modal-overlay').removeEventListener('click', this.close);
  }

  get open() {
    return this.hasAttribute('open');
  }

  set open(isOpen) {
    this.shadowRoot.querySelector('.modal-container').classList.toggle('open', isOpen);
    if(isOpen) {
      document.addEventListener('keydown', this.watchEscape);
    }
    else {
      document.removeEventListener('keydown', this.watchEscape);
    }
  }

  watchEscape(event) {
    if (event.key === 'Escape') {
      this.close();
    }
  }

  close() {
    this.open = false;
  }
}

window.customElements.define("basic-modal", BasicModal);