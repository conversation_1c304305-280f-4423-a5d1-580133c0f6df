import { watch } from 'vue'
import { defineStore } from 'pinia'
import { useRouter } from "vue-router"
import { useRoute } from "vue-router"
import { usePartThreeStore } from '@/store/part/partThreeStore'
import { useDrawingStore } from "@/store/drawingStore"
import threeContext from "@/utils/three/threeContext"
import { getRootObjectFromChild, returnObjectToUntouchedState } from "@/utils/three/utils/threeHelpers"

export const useRouterStore = defineStore('router', () => {
  const router = useRouter()
  const route = useRoute()

  const drawingStore = useDrawingStore()
  const partThreeStore = usePartThreeStore()

  // Import the drawing when the route changes to a drawing id, clear the drawing when the id is removed
  watch(() => route.params.id, async (id, oldId) => {
    if (!oldId && id) await drawingStore.importDrawingById(id)
    else if (!id) drawingStore.clearDrawing()
  })

  // Evaluate the part numbers in the route and focus on the corresponding parts
  watch(() => route.params.partNrs, (newNrs, oldNrs) => {
    if (!partThreeStore.currentPart || (!newNrs && !oldNrs)) return

    // Normalize the part numbers to arrays
    const normalizedNewNrs = Array.isArray(newNrs) ? newNrs : []
    const normalizedOldNrs = Array.isArray(oldNrs) ? oldNrs : []

    const addedParts = normalizedNewNrs.filter(part => !normalizedOldNrs.includes(part))
    const removedParts = normalizedOldNrs.filter(part => !normalizedNewNrs.includes(part))

    // If only one part was added and none were removed, directly focus on that part
    if (addedParts.length === 1 && removedParts.length === 0) focusOnNextPartInRouteByNr(addedParts[0])

    // If only one part was removed and none were added, go back to the previous/parent part
    else if (removedParts.length === 1 && addedParts.length === 0) partThreeStore.goBackToPreviousPart()

    // If multiple parts were added or removed, reset to root and focus on the parts chronologically
    else resetToRootAndFocusOnParts()
  })

  function pushPartToRouteFromNr(partNr) {
    const currentPath = router.currentRoute.value.path.replace(/\/$/, '') // Remove trailing slash
    router.push(`${currentPath}/${partNr}`)
  }

  function pushPartToRouteFromId(id) {
    const object = threeContext.getObjectById(id)
    pushPartToRouteFromNr(object.name)
  }

  function popPartFromRoute() {
    const updatedPath = route.fullPath.split('/').slice(0, -1).join('/') // Remove the last segment
    router.push(updatedPath)
  }

  // Focus on each part in the route chronologically, if no parts are specified, focus on the current part
  function focusOnPartByInitialRoute() {
    if (route.params.partNrs && route.params.partNrs.length)  {
      route.params.partNrs.forEach(partNr => focusOnNextPartInRouteByNr(partNr))
    } else {
      partThreeStore.focusOnPartById(threeContext.currentObject.id)
    }
  }

  function focusOnNextPartInRouteByNr(partNr) {
    const object = threeContext.currentObject.children.find(child => child.name === partNr)
    if (!object) return

    partThreeStore.focusOnPartByName(object.name)
  }

  function resetToRootAndFocusOnParts(partNrs) {
    threeContext.currentObject = getRootObjectFromChild(threeContext.currentObject)
    returnObjectToUntouchedState(threeContext.currentObject)
    focusOnPartByInitialRoute()
  }

  return {
    router,
    route,
    pushPartToRouteFromNr,
    pushPartToRouteFromId,
    popPartFromRoute,
    focusOnPartByInitialRoute,
  }
})