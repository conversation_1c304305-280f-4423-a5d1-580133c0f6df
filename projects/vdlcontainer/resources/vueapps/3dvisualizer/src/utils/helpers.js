/**
 * Converts a Base64 string to an ArrayBuffer, with automatic gzip decompression.
 * @param {string} base64 - The Base64-encoded string (potentially gzip compressed).
 * @returns {Promise<ArrayBuffer>} - The decoded and decompressed ArrayBuffer.
 */
export async function base64ToArrayBuffer(base64) {
  // Convert the Base64 string to a data URL
  const dataUrl = `data:application/octet-stream;base64,${base64}`

  // Fetch the data as an ArrayBuffer
  const response = await fetch(dataUrl)
  const compressedBuffer = await response.arrayBuffer()

  // Decompress using browser's built-in gzip decompression
  const decompressedStream = new Response(compressedBuffer).body.pipeThrough(
    new DecompressionStream('gzip')
  )

  const decompressedResponse = new Response(decompressedStream)
  return decompressedResponse.arrayBuffer()
}

// Helper function to extract the base name (e.g., "1234" from "1234_1" or "4213" from "4213-2")
export function getBaseName(name) {
  return name.split(/[_-]/)[0]
}

// Helper function to debounce a function call (e.g., to avoid spamming the server with requests)
export function debounce(func, delay) {
  let timeout
  return function (...args) {
    clearTimeout(timeout)
    timeout = setTimeout(() => func.apply(this, args), delay)
  }
}

export function formatPrice(price) {
  const num = parseFloat(price)
  return isNaN(num) ? '0.00' : num.toFixed(2)
}

export function formatAmount(amount) {
  const num = parseFloat(amount)
  return num % 1 === 0 ? num.toFixed(0) : num.toFixed(2);
}