.sidebar {
  @apply w-full;
  /* desktop */
  @apply lg:w-1/4;
}

.sidebar-box {
  @apply fixed inset-0 overflow-hidden w-64 bg-white hidden text-lg p-6 pr-2 shadow-xl border-r border-gray-400;
  @apply flex-col items-start;
  z-index: 1000;
  /* desktop */
  @apply lg:relative lg:flex lg:p-0 xl:pl-20 lg:z-10 lg:shadow-none lg:border-0 lg:w-auto;
}

.sidebar-box.open {
  @apply flex;
}

.open-sidebar {
  @apply flex justify-end lg:hidden mb-6;
}
.btn-close-sidebar {
  @apply inline-block lg:hidden mb-6;
}

.sidebar .link {
  @apply inline-flex relative mb-2 pr-6 text-primary;
}

.sidebar .search {
  @apply inline-flex relative mb-3 pr-4 text-primary;
}

.sidebar .link:hover {
  @apply text-tertiary;
}

.sidebar .link.active {
  @apply text-highlight;
}

.sidebar .icon {
  @apply w-5 mr-2;
}

.sidebar .link .badge {
  @apply absolute top-0 right-0  flex px-1 justify-center items-center bg-highlight rounded-full text-white text-xs text-center;
}
