import { <PERSON><PERSON>FLoader } from "three/addons/loaders/GLTFLoader"
import { GLTFExporter } from "three/addons/exporters/GLTFExporter"

const gltfLoader = new GLTFLoader()
const gltfExporter = new GLTFExporter()

/**
 * Serializes a Three.js Object3D into a GLB ArrayBuffer, which can be sent to the server or saved as a file.
 *
 * @param {THREE.Object3D} object - The Object3D to serialize into GLB format.
 * @returns {Promise<ArrayBuffer>} A promise that resolves to an ArrayBuffer containing the GLB data.
 * @throws {Error} If the provided object is not a valid instance of THREE.Object3D, or if the export fails.
 */
export async function serializeThreeObjectToArrayBuffer(object) {
  return new Promise((resolve, reject) => {
    gltfExporter.parse(
      object,
      result => resolve(result),
      error => reject(error),
      { binary: true }
    )
  })
}

/**
 * Deserializes a GLB (GL Transmission Format Binary) file from an ArrayBuffer into a Three.js Object3D, which can be loaded in a scene.
 *
 * @param {ArrayBuffer} buffer - The ArrayBuffer containing the GLB data.
 * @returns {Promise<THREE.Object3D>} A promise that resolves to a Three.js Object3D parsed from the buffer.
 * @throws {Error} If the provided buffer is not a valid ArrayBuffer.
 */
export function deserializeThreeObjectFromArrayBuffer(buffer) {
  return new Promise((resolve, reject) => {
    gltfLoader.parse(
      buffer,
      '',
      object => resolve(object),
      error => reject(error)
    )
  })
}