import { watch, onUnmounted } from "vue"

/**
 * Adds a `beforeunload` listener to warn the user about unsaved changes.
 *
 * @param {Function} condition - A reactive condition to determine if the listener should be active.
 */
export function useBeforeUnload(condition) {
  function handleBeforeUnload(event) {
    event.preventDefault()
    event.returnValue = '' // Triggers the confirmation dialog
  }

  // Watch the condition and toggle the listener accordingly
  watch(condition, (shouldWarn) => {
    if (shouldWarn) {
      window.addEventListener("beforeunload", handleBeforeUnload)
    } else {
      window.removeEventListener("beforeunload", handleBeforeUnload)
    }
  })

  // Cleanup on component unmount
  onUnmounted(() => {
    window.removeEventListener("beforeunload", handleBeforeUnload)
  })
}