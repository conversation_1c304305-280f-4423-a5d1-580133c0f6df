<?php

  use Gsd\DataTable\DataTable;

  trait ImportLogActions {

    /**
     * @throws Exception
     */
    public function executeImportlog(): void {
      $this->createListFilters();
    }

    /**
     * @throws Exception
     */
    private function createListFilters(): void {
      $dataTable = new DataTable("importlog");

      $dataTable->setRequestUrl(reconstructQueryAdd(['action' => 'listajax']));
      $dataTable->addColumnHelper("type", "Type import");
      $dataTable->addColumnHelper("rows_processed", "Aantal rijen");
      $dataTable->addColumnHelper("amount_added", "Rijen toegevoegd");
      $dataTable->addColumnHelper("amount_updated", "Rijen aangepast");
      $dataTable->addColumnHelper("insertTS", "Geïmporteerd op");
      $dataTable->addColumnHelper("insertUser", "Geïmporteerd door");
      $dataTable->addColumnHelper("actions", "Acties");

      $dataTable->setDefaultSort("insertTS", "desc");
      $dataTable->handleRequest($_POST);

      $this->dataTable = $dataTable;
    }

    /**
     * @throws Exception
     */
    public function executeListAjax(): void {
      $this->createListFilters();

      $imports = $this->getImportLogs();
      $totalCount = VisualImportLog::count_all_by([]);

      $tableData = array_map(function($import) {
        return [
          'DT_RowId' => $import->id,
          'type' => $import->type_label(),
          'rows_processed' => $import->rows_processed,
          'amount_added' => $import->amount_added,
          'amount_updated' => $import->amount_updated,
          'insertTS' => DateTimeHelper::convertToReadable($import->insertTS, '%d %B %Y %H:%I:%S'),
          'insertUser' => $import->user_name,
          'actions' => (string) BtnHelper::getView(PageMap::getUrl('M_VISUALIZER_IMPORT', ['action' => 'details', 'id' => $import->id])),
        ];
      }, $imports);

      ResponseHelper::exitAsJson([
        'data'            => $tableData,
        'recordsTotal'    => $totalCount,
        'recordsFiltered' => $totalCount,
        'draw'            => (int)$_POST['draw'],
      ]);
    }

    private function getImportLogs(): array {
      $userTable = User::getTablename();
      $logTable = VisualImportLog::getTablename();

      $sort = $this->dataTable->getSortQuery();
      $limit = $this->dataTable->getPager()->getLimitQuery();

      $query = <<<SQL
        SELECT $logTable.*,
        CONCAT(user.firstname, ' ', user.insertion, ' ', user.lastname) as user_name
        FROM $logTable
        LEFT JOIN $userTable ON $userTable.id = $logTable.insertUser
        $sort $limit
      SQL;
      $result = DBConn::db_link()->query($query);

      $logs = [];
      while ($row = $result->fetch_array()) {
        $columnCount = 0;
        $log = new VisualImportLog()->hydrateNext($row, $columnCount);
        $log->user_name = $row[$columnCount];
        $logs[] = $log;
      }
      return $logs;
    }

    public function executeDetails(): void {
      $this->log = VisualImportLog::find_by_id($_GET['id']);
    }
  }