@CHARSET "UTF-8";
#exported_vbs {
  padding: 10px;
  margin-bottom: 10px;
  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
  font-size: 12px;
  border-radius: 4px;
  border: 1px solid #ffeeba;
  color: #856404;
  background-color: #fff3cd;
}

/* Remove arrow down in main navigation - to fit more nav items in the bar */
ul#navmenu-h > li.sub > a::after {
  display: none;
}

input.w-sm {
  width: 60px;
}

/* Styling for list filters */
.filters-container {
  border-bottom: 1px solid #e6e6e666;
  padding-bottom: 8px;
}

.filters-bar {
  display:     flex;
  align-items: flex-start;
  /*margin-bottom: 8px;*/
}

.filters-group {
  margin-right: 25px;
}

.filter-box {
  display:       block;
  margin-bottom: 8px;
}

.filters-group-header {
  font-size: 12px;
  margin-top: 12px;
  margin-bottom: 8px;
  font-weight: 600;
}

.filters-bar .select2 {
  margin-bottom: 8px;
}


.stat-color-primary,
.default_table td.stat-color-primary {
  background-color: hsla(207, 97%, 29%, 0.2);
}
.stat-color-secondary,
.default_table td.stat-color-secondary {
  background-color: rgb(253, 212, 0);
}

a.orderactive {
  color: red;
}

.alert-info {
  color: #31708f;
  background-color: #d9edf7;
  border-color: #bce8f1;
}

/**
webshop garantie module
 */
.sidebar .link {
  width: 98%;
}
