<?php

  namespace domain\visualizer\entity;

  class VisualProductImportRow extends VisualImportRow {
    protected string $material_nr;

    public function __construct(array $excel_row) {
      $this->group_nr = trim($excel_row[0] ?? '');
      $this->material_nr = trim($excel_row[1] ?? '');
      $this->description = trim($excel_row[3] ?? '');
      $this->part_nr = trim($excel_row[5] ?? '');
    }

    public function getMaterialNr(): string {
      return $this->material_nr;
    }

    public function validate(array &$errors, int $rowNumber): bool {
      $initialErrorCount = count($errors);

      if (empty($this->group_nr)) {
        $errors[$rowNumber][] = "Groepnummer is verplicht";
      }
      if (empty($this->material_nr)) {
        $errors[$rowNumber][] = "Materialnummer is verplicht";
      }
      if (empty($this->description)) {
        $errors[$rowNumber][] = "Beschrijving is verplicht";
      }
      if (empty($this->part_nr)) {
        $errors[$rowNumber][] = "Onderdeelnummer is verplicht";
      }
      return count($errors) === $initialErrorCount;
    }
  }