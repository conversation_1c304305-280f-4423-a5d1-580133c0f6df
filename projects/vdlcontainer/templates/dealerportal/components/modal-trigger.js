let modal_trigger_template = `
  <a id="a-link">
    <slot name="trigger"></slot>
  </a> 
  <slot id="modal"></slot>   
  <style>
    a#a-link {
      cursor: pointer;
      display: inline-block;
    }
  </style>
  `;


/**
 * A very simple element to combine a trigger and modal
 */
class ModalTrigger extends HTMLElement {
  constructor() {
    super();
    this.shadow       = this.attachShadow({mode: "open"});
    this.clickTrigger    = this.clickTrigger.bind(this);
  }

  connectedCallback() {
    this.shadowRoot.innerHTML = modal_trigger_template;
    this.shadowRoot.getElementById('a-link').addEventListener('click', this.clickTrigger);
  }

  disconnectedCallback() {
    this.shadowRoot.getElementById('a-link').removeEventListener('click', this.clickTrigger);
  }

  clickTrigger() {
    // get the nodes from the modal slot
    let slot_nodes = this.shadowRoot.querySelector('slot#modal').assignedNodes();
    // retrieve the basic modal web component
    let basic_modal = slot_nodes.find(node => node.nodeName == 'basic-modal'.toUpperCase());
    basic_modal.open = true;
  }
}

window.customElements.define("modal-trigger", ModalTrigger);