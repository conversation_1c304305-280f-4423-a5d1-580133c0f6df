<script setup>
import TheCanvas from "@/components/canvas/TheCanvas.vue"
import TheUploadForm from "@/components/layout/TheUploadForm.vue"
import PartSelectionList from "@/components/part/PartSelectionList.vue"
import PartUnlinkedList from "../components/part/PartUnlinkedList.vue";
import CurrentPart from "@/components/part/CurrentPart.vue"
import TheInstructions from "@/components/layout/TheInstructions.vue"
import TheMenu from "@/components/layout/TheMenu.vue"
import TheConfirmationField from "@/components/layout/TheConfirmationField.vue"
import PartEditForm from "@/components/part/PartEditForm.vue"
import PartOrderForm from "@/components/part/PartOrderForm.vue"
import { useConfigStore } from "@/store/configStore"
import { useDrawingStore } from "@/store/drawingStore"
import { usePartEditStore } from "@/store/part/partEditStore"
import { usePartOrderStore } from "@/store/part/partOrderStore"
import { useInteractionStore } from "@/store/interactionStore"
import { useRoute } from "vue-router"
import { computed, ref } from "vue"
import { useI18n } from "vue-i18n"

const { t } = useI18n()
const configStore = useConfigStore()
const drawingStore = useDrawingStore()
const partEditStore = usePartEditStore()
const partOrderStore = usePartOrderStore()
const interactionStore = useInteractionStore()
const route = useRoute()

const showDialog = computed(() => !!partEditStore.partToEdit || !!partOrderStore.partToOrder)
const dialogTitle = computed(() => {
  const partNr = partEditStore.partToEdit?.part_nr || partOrderStore.partToOrder?.part_nr || ''
  return `${t('part')} ${partNr}`
})
function closeDialog() {
  partEditStore.setPartToEdit(null)
  partOrderStore.setPartToOrder(null)
}

const instructionsAreCollapsed = ref(true)
function toggleInstructions() {
  instructionsAreCollapsed.value = !instructionsAreCollapsed.value
}
</script>

<template>
  <the-upload-form v-if="!route.params.id && configStore.isAdmin"/>
  <div class="viewer d-flex">
    <div class="canvas content-block position-relative ma-0 pa-0">
      <div class="editor position-absolute" v-if="drawingStore.drawingData && configStore.isAdmin"/>
      <div class="instructions position-absolute" v-if="drawingStore.drawingData" @click="toggleInstructions">
        <v-icon size="large" class="info" v-if="instructionsAreCollapsed">mdi-information</v-icon>
        <the-instructions v-else/>
      </div>
      <div v-if="interactionStore.invisibleParts.size" class="toggle-invisible position-absolute">
        <v-icon size="large" class="info" @click="interactionStore.setAllPartsVisible">mdi-eye-off</v-icon>
      </div>
      <the-canvas v-if="drawingStore.drawingData"></the-canvas>
    </div>
    <div class="sidemenu content-block ma-0" :class="{ 'd-flex justify-center align-center': !drawingStore.drawingData }">
      <div class="details d-flex flex-column" v-if="drawingStore.drawingData">
        <current-part />
        <part-selection-list />
        <the-menu />
      </div>
      <div v-else class="no-model-message">
        {{ t('noDrawingLoaded') }}
      </div>
    </div>
  </div>
  <div class="d-flex bottom-section" v-if="drawingStore.drawingData && configStore.isAdmin">
    <div class="canvas-width content-block ma-0">
      <the-confirmation-field/>
    </div>
    <div class="sidemenu content-block ma-0" :class="{ 'd-flex justify-center align-center': !drawingStore.drawingData }">
      <div class="details d-flex flex-column" >
        <part-unlinked-list />
      </div>
    </div>
  </div>
  <v-dialog
      :model-value="showDialog"
      @update:model-value="closeDialog"
      width="auto"
      max-width="80%"
  >
    <v-card>
      <v-card-title class="bg-primary pa-4">
        {{ dialogTitle }}
      </v-card-title>
      <v-card-text class="pa-0">
        <part-edit-form @submitted="closeDialog" v-if="partEditStore.partToEdit" :part="partEditStore.partToEdit"/>
        <part-order-form @submitted="closeDialog" v-else-if="partOrderStore.partToOrder" :part="partOrderStore.partToOrder" />
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<style scoped>
.editor {
  top: 1em;
  right: 1em;
  z-index: 1;
}
.instructions {
  top: 1em;
  left: 1em;
  z-index: 1;
  cursor: pointer;
}
.toggle-invisible {
  bottom: 1em;
  left: 1em;
  z-index: 1;
}
.info {
  width: 2em;
  height: 2em;
}
.canvas {
  flex: 3 1 0;
  aspect-ratio: 9/6 auto;
}
.sidemenu {
  padding: 1em;
  flex: 1 0 0;
}
.details {
  height: 100%;
  gap: .5em;
}
.details > * {
  flex: 0 1 0;
}
.canvas-width {
  flex: 3 1 0;
}
@media (max-width: 768px) {
  .viewer {
    flex-direction: column;
  }
  .sidemenu {
    flex: 0 0 auto;
    height: 40em;
  }
  .canvas {
    flex: 0 1 auto;
    width: 100%;
  }
  .canvas-width,
  .sidemenu-width {
    flex: 1 1 auto;
  }
}
</style>