input, textarea, select {
  font-size: 14px;
  @apply rounded-none border border-gray-500 px-3 py-1;
}

input::placeholder,
textarea::placeholder {
  @apply text-gray-600;
}

:focus {
  @apply outline-none; /** remove Chrome black focus border **/
}



.form-group-header {
  @apply text-lg font-semibold text-primary mt-5 mb-2 pb-1 border-b border-gray-300;
}

.default_table .dataTableHeadingRow {
  @apply font-semibold;
}

.default_table.addresses .dataTableRow td {
  @apply pb-2 border-b border-gray-300;
}

.mydata-form input[type="text"],
.mydata-form input[type="file"],
.mydata-form select {
  @apply w-full md:w-64;
}