3D Visualizer
------------------
De 3D Visualizer is een interactieve 3D visualisatietool voor het bekijken, bewerken en beheren van 3D-modellen (GLB/GLTF). De app is bedoeld voor het selecteren, inspecteren en eventueel bestellen van onderdelen binnen een 3D-omgeving. Zowel beheerders (admin) als reguliere gebruikers kunnen via een intuïtieve interface werken met 3D-tekeningen en onderdelen.

Belangrijkste features:
- Laden en weergeven van 3D-modellen in de browser
- Selecteren en inspecteren van onderdelen in het model
- Bewerken van onderdelen (admin)
- Onderdelen toevoegen aan een winkelmandje (eindgebruiker)
- Meertalige ondersteuning

Installatie instructies
----------------------
1. Zorg dat je Node.js (aanbevolen: v19+) en npm geïnstalleerd hebt.
2. Installeer de dependencies:
   ```
   npm install
   ```
3. Start de ontwikkelserver:
   ```
   npm run dev
   ```
4. Voor productie build:
   ```
   npm run build
   ```
   Of voor een ontwikkel-build:
   ```
   npm run build-dev
   ```

5. Deze app afhankelijk is van backend endpoints. Zorg dus dat je docker container draait (zie https://gsd-bv.atlassian.net/wiki/spaces/SD/pages/3506217/Docker+setup).

App starten
-----------

**Backend / Beheer**
- Lokaal:
  1. Start de backend (docker container, zie [Docker setup](https://gsd-bv.atlassian.net/wiki/spaces/SD/pages/3506217/Docker+setup)).
  2. Start de viewer met:
     ```
     npm run dev
     ```
  3. Ga naar het dealerportal: [https://vdldealer.com.vdlcontainer.localhost/](https://vdldealer.com.vdlcontainer.localhost/)
  4. Klik op de visualizer module om de 3D Visualizer te openen.

- Productie:
  1. Sla de eerste 2 stappen over en ga naar het dealerportal: [https://vdldealer.com/](https://vdldealer.com/)

**Frontend / Webshop**
- Lokaal:
  1. Start de viewer met:
     ```
     npm run dev
     ```
  2. Log in als klant via een direct login [https://vdldealer.com.vdlcontainer.localhost/nl/relaties](https://vdldealer.com.vdlcontainer.localhost/nl/relaties)
  3. Zoek een machine via: [https://vdldealer.com.vdlcontainer.localhost/en/machine-search](https://vdldealer.com.vdlcontainer.localhost/en/machine-search)
  4. Klik op "open visualizer" als deze optie beschikbaar is.

- Productie:
  1. Zoek machine via [https://vdldealer.com/en/machine-search](https://vdldealer.com/en/machine-search)
  2. Gebruik de visualizer via het dealerportal zoals hierboven beschreven.

Let op: De app is alleen beschikbaar voor machines waarvoor een 3D-tekening is opgeslagen.

Documentatie van gebruikte libraries
------------------------------------
- **Pinia** (state management):
  - Alle globale state (zoals gebruikersrol, taal, tekeningdata) wordt beheerd via Pinia stores in `src/store/`.
  - Voorbeeld: `useConfigStore`, `useDrawingStore`.
  - Pinia wordt geïnitialiseerd in `src/main.js` en gebruikt de Composition API.
  - Documentatie: https://pinia.vuejs.org/

- **Vue Router** (routing):
  - De app gebruikt Vue Router voor navigatie tussen de verschillende tekeningonderdelen.
  - Routes zijn gedefinieerd in `src/router/router.js`.
  - Navigatie vindt plaats op basis van tekening-ID's en partnummers in de URL.
  - Documentatie: https://router.vuejs.org/

- **Three.js & TresJS** (3D visualisatie):
  - 3D-weergave en interactie worden verzorgd door Three.js (https://threejs.org/) en de declaratieve Vue-wrapper TresJS (https://docs.tresjs.org/).
  - Het canvas en de 3D-scene zijn te vinden in `src/components/canvas/` (`TheCanvas.vue`, `TheScene.vue`, `TheDrawing.vue`).
  - TresJS maakt het mogelijk om Three.js objecten als Vue componenten te gebruiken.

- **Overige:**
  - `vue-i18n` voor meertaligheid
  - `axios` voor API-requests
  - `tweakpane` voor 3D-editor controls

Composition API & Composables
-----------------------------
Deze app maakt intensief gebruik van de Vue 3 Composition API en eigen composables voor herbruikbare logica en state.

- **State management:**
  - Stores (zoals `useDrawingStore`, `useConfigStore`) zijn opgezet met de Composition API (`defineStore` met setup-syntax).
  - Reactieve refs en computed properties worden gebruikt voor state en afgeleide waarden.

- **Composables:**
  - Te vinden in `src/composables/`.
  - Voorbeelden:
    - `useDrawingLoader`: laadt en beheert het 3D-model, inclusief lifecycle hooks (`onMounted`, `onBeforeUnmount`, `watch`).
    - `usePart`: beheert de logica rondom onderdelen, zoals naamgeving en orderbaarheid.
    - `useTaskState`: beheert de status van asynchrone taken (loading, success, error).
  - Composables worden gebruikt in componenten om logica te delen zonder code duplicatie.

Bekijk de map `src/composables/` voor voorbeelden en documentatie in de code.