/** can't target mobile only with tailwind */
@media screen and (max-width: 640px) {

  table.responsive-table thead {
    clip:     rect(0 0 0 0);
    height:   1px;
    margin:   -1px;
    overflow: hidden;
    padding:  0;
    position: absolute;
    width:    1px;
  }

  table.responsive-table tr {
    @apply block pb-8 mb-10;
  }

  table.responsive-table td {
    @apply py-2 block text-right;
  }

  table.responsive-table td::before {
    @apply text-gray-600;
    content: attr(data-label);
    float:   left;
  }

  table.responsive-table td:last-child {
    border-bottom: 0;
  }


  table.default_table tr.dataTableRow {
    @apply block;
  }

  table.default_table td.head {
    @apply block font-semibold;
  }

  table.default_table input[type="text"],
  table.default_table textarea,
  table.default_table select {
    @apply w-full;
  }
}

.default_table tr.dataTableRow td {
  @apply pb-2;
}


/*********************
 * TABLE LIST: see orders and saved baskets
 *********************/


.table-list-line {
  @apply w-full grid gap-2 mt-3 border-b border-gray-400 hover:bg-gray-100 px-2;
  grid-template-columns: 1fr 1fr;
}

.table-list-header {
  @apply text-primary hidden sm:grid hover:bg-white;
}

.table-list-col {
  @apply py-1 text-right;
}

.table-list-label {
  @apply sm:hidden py-1 text-gray-600;
}

.table-list-line-detail {
  @apply border border-gray-400 border-t-0 p-4 mb-6 ml-6;
}

@screen md {
  .table-list-line {
    @apply mt-0;
    grid-template-columns: auto 1fr auto auto;
  }

  .table-list-line.table-list-saved-basket {
    grid-template-columns: 1fr auto auto auto;
  }

  .table-list-col {
    @apply py-2 text-left;
  }
}
