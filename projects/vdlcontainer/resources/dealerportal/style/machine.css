/*********************
 * SEARCH
 *********************/

input.order_nr_input {
  @apply h-16 ml-1 mr-3 text-black text-3xl text-center rounded w-64 border-primary;
}

.btn-search {
  @apply px-10 h-16 mx-1 text-xl text-center rounded;
}

.find-serialnr-list {
  @apply w-full grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-8;
}

.find-serialnr-list a {
  @apply px-4 pt-14 pb-3 shadow-md border border-gray-300;
}

.find-serialnr-list a:hover {
  @apply border-gray-700;
}

.find-serialnr-list p {
  @apply text-center text-tertiary font-semibold mt-4 py-3;
}

/*********************
 * MACHINE VIEW
 *********************/
.document-box {
  @apply inline-flex justify-start items-center p-1 mr-3 mb-4;
  width: 100%;
}

.document-box:hover {
  @apply underline;
}

.machine-name {
  @apply text-base text-gray-600 mb-5;
}

.category-boxes {
  @apply flex flex-wrap py-3 -mx-3;
}

.category-box {
  @apply w-1/2 md:w-1/4 mb-3 mb-6 px-3;
}

.category-box a {
  @apply flex flex-col relative shadow-md;
  background:          #eeeeee;
  background-image:    url('../images/category_bg.jpg');
  background-position: top left;
  background-repeat:   no-repeat;
  background-size:     cover;
  margin:              0 10px 10px 0;
  padding-bottom:      50px;
}

.category-box.main-category a {
  @apply bg-white border border-gray-300;
  background-image: none;
}

.category-box a:hover {
  @apply no-underline;
  box-shadow: 0px 0px #182f54;
  margin:     10px 0 0 10px;
  transition: all 1s;
}

.category-box .image-container {
  @apply flex items-center w-full overflow-hidden relative;
}

.category-box a:hover .image-container {
  flex-grow:  1;
  transition: all 0.5s;
}

.category-box .image-container.zoom .zoomout {
  opacity: 1;
}

.category-box .image-container.zoom .zoomin {
  @apply absolute;
  top:     0;
  opacity: 0;
}

.category-box a:hover .image-container.zoom .zoomin {
  opacity:    1;
  transition: opacity 0.5s;
}

.category-box a:hover .image-container.zoom .zoomout {
  opacity:    0;
  transition: all 0.6s;
}

.category-box .title {
  @apply absolute bottom-0 w-full;
  @apply py-2 text-center flex flex-grow flex-shrink-0 flex-col justify-center items-center px-2;
}

.category-box .title h4 {
  @apply text-lg text-tertiary font-semibold;
}

.category-box .title .subtext {
  @apply text-sm text-gray-700 text-center;
}

@media (min-width: 768px) {
  .document-box {
    width: 30%;
  }
}