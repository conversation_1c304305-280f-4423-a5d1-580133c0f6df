<script setup>
import { useDrawingStore } from "@/store/drawingStore"
import { useRouterStore } from "@/store/routerStore"
import { useBeforeUnload } from "@/composables/beforeUnload"
import { computed } from 'vue'

const drawingStore = useDrawingStore()

useRouterStore()
useBeforeUnload(() => drawingStore.drawingHasUnsavedChanges)

const dialogVisible = computed(() => drawingStore.isLoading || drawingStore.taskMessage)
</script>

<template>
  <v-app>
    <v-dialog
        v-model="dialogVisible"
        :persistent="drawingStore.isLoading"
        max-width="400"
    >
      <v-card>
        <v-card-text class="text-center pa-8">
          <div v-if="drawingStore.isLoading" class="d-flex justify-center align-center" style="min-height: 120px;">
            <v-progress-circular
                indeterminate
                color="primary"
                size="48"
            />
          </div>
          <div v-else-if="drawingStore.taskMessage" class="d-flex flex-column" style="min-height: 120px;">
            <v-row justify="center" align="center" class="flex-grow-1">
              <v-col cols="12">
                <p class="mb-4">{{ drawingStore.taskMessage }}</p>
              </v-col>
            </v-row>
            <v-card-actions>
              <v-spacer />
              <v-btn color="primary" @click="drawingStore.resetState">Ok</v-btn>
              <v-spacer />
            </v-card-actions>
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>

    <router-view/>
  </v-app>
</template>

<style>
.v-application__wrap {
  min-height: unset !important;
}
</style>