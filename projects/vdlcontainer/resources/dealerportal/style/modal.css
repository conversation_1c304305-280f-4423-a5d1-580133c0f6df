/*********************
 * MODAL
 *********************/

.modal-container {
  display:          flex;
  position:         fixed;
  z-index:          1000;
  top:              0;
  left:             0;
  bottom:           0;
  right:            0;
  padding:          0 20px;
  overflow:         auto;
  visibility:       hidden;
  opacity:          0;
  transition:       opacity 0.2s ease-in;
}

.modal-container.open {
  visibility: visible;
  opacity:    100;
  transition: opacity 0.2s ease-in;
}

.modal-overlay {
  background-color: rgba(0, 0, 0, 0.5);
  height: 100%;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 100%;
}

.modal-box {
  display:        flex;
  flex-direction: column;
  position:       relative;
  width:          500px;
  margin:         auto;
}

.modal-header {
  @apply bg-primary;
  display:         flex;
  justify-content: space-between;
  align-items:     start;
  color:           white;
  padding:         12px 10px;
}

.modal-header h2 {
  @apply m-0 p-0 text-white;
}

.modal-header .close-modal-cross svg {
  fill: darkgrey;
}

.modal-content {
  background: white;
  padding:    10px;
  font-size:  15px;
}

.close-modal {
  display:         flex;
  justify-content: flex-end;
  background:      white;
  padding-top:     6px;
  padding-right:   6px;
}