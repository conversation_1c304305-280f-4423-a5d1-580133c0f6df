import { ref } from "vue"
import axios from "axios"
import { debounce } from "@/utils/helpers"

/**
 * A composable function to manage machine filtering and retrieval.
 *
 * @returns {Object} - The reactive state and methods for managing machines.
 * @property {Ref<Array>} machines - A reactive array of the most recent fetched machines.
 * @property {Function} fetchMachines - Fetches machines from the server based on the filter.
 * @property {Function} updateFilter - Updates the filter string and fetches machines using debouncing.
 */
export function useMachine() {
  const machines = ref([])
  async function fetchMachines(query = '') {
    try {
      const { data } = await axios.get('?action=getMachines', {
        params: { filter: query, limit: 20 },
      })
      machines.value = data
    } catch (err) {
      console.error("Failed to fetch machines:", err)
      machines.value = []
    }
  }
  // Debounce to avoid spamming the server with requests
  const debouncedFetchMachines = debounce(fetchMachines, 300)

  function updateFilter(newFilter) {
    debouncedFetchMachines(newFilter) // Trigger API call with new filter
  }

  return {
    machines,
    fetchMachines,
    updateFilter
  }
}
