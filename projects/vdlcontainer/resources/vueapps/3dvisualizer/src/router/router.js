import { createRouter, createWebHashHistory } from 'vue-router'
import ViewerView from "@/views/ViewerView.vue"

// defined vue routes for adding new drawing or editing existing drawing based on id
const routes = [
  {
    path: '/new/:partNrs*',
    component: ViewerView
  },
  {
    path: '/:id?/:partNrs*',
    component: ViewerView
  },
  {
    path: '/',
    redirect: '/new'
  }
]

const router = createRouter({
  history: createWebHashHistory(import.meta.env.BASE_URL),
  base: '/',
  routes: routes,
  linkActiveClass: "activeNav",
})

export default router
