import { getBaseName } from "@/utils/helpers"
import mitt from 'mitt'

// A class to store the available parts and submitted/changed parts
class PartContext {
  // Maps part numbers to their respective parts, to quickly retrieve parts by name/number
  partMap = new Map()

  // Event bus to notify components when parts are refreshed
  eventBus = mitt()

  // Initialize the part map with the parts from the drawing
  setMapFromParts(parts) {
    this.partMap = new Map(
      parts.map(part => [part.part_nr.toString(), part])
    )
  }

  getPartByName(name) {
    return this.partMap.get(getBaseName(name))
  }

  submitPart(part) {
    this.partMap.set(part.part_nr.toString(), part)
  }

  getPartsForSaving() {
    return Array.from(this.partMap.values())
  }

  emitPartsRefreshed() {
    this.eventBus.emit('parts-refreshed')
  }

  onPartsRefreshed(callback) {
    this.eventBus.on('parts-refreshed', callback)
  }

  cleanupPartsRefreshed(callback) {
    this.eventBus.off('parts-refreshed', callback)
  }
}

// Export a singleton instance, to share across components
const partContext = new PartContext()
export default partContext