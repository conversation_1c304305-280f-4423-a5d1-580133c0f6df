import { shallowRef, onMounted, watch, onBeforeUnmount } from "vue"
import { dispose } from '@tresjs/core'
import { useGLTF } from '@tresjs/cientos'
import { preProcessObject } from '@/utils/three/utils/threeProcessors'
import { getRootObject } from '@/utils/three/utils/threeHelpers'
import { useDrawingStore } from '@/store/drawingStore'

/**
 * A composable for loading, initializing, and managing a GLB/GLTF model
 *
 * @returns {Ref<THREE.Object3D | null>} - A reactive reference to the loaded model.
 */
export function useDrawingLoader() {
  const drawingStore = useDrawingStore()

  const drawing = shallowRef(null)
  async function initializeDrawing() {
    if (!drawingStore.drawingData) return
    // dispose of the old model (frees up memory)
    if (drawing.value) dispose(drawing.value)

    const result = await useGLTF(drawingStore.drawingData.url, { draco: true });
    const object = getRootObject(result.scene)
    preProcessObject(object, drawingStore.drawingData)

    drawing.value = object
  }

  onMounted(async() => {
    await initializeDrawing()
  })
  // When url changes, initialize new model
  watch(() => drawingStore.drawingData, async() => {
    await initializeDrawing()
  })
  // Clean up recources when component is unmounted
  onBeforeUnmount(() => dispose(drawing.value))

  return drawing
}