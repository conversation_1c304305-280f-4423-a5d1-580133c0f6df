import { ref } from 'vue'

/**
 * Composable to manage async task state with loading, success, and error messaging.
 *
 * @typedef {Object} TaskStateComposable
 * @property {Ref<boolean>} isLoading - A reactive reference to the loading state.
 * @property {Function} runTask - A function to execute an asynchronous task with state management and messaging.
 * @property {Function} resetState - A function to reset the task state.
 * @property {Ref<string|null>} taskMessage - A reactive reference to success or error messages.
 *
 * @returns {TaskStateComposable} The composable object containing task states and handlers.
 */
export function useTaskState() {
  const isLoading = ref(false)
  const taskMessage = ref(null)

  async function runTask(task, errorMessage = null, successMessage = null) {
    try {
      isLoading.value = true
      await task()
      if (successMessage) taskMessage.value = successMessage
    } catch (err) {
      console.error(err)
      if (errorMessage) taskMessage.value = errorMessage
    } finally {
      isLoading.value = false
    }
  }

  function resetState() {
    isLoading.value = false
    taskMessage.value = null
  }

  return { isLoading, runTask, resetState, taskMessage }
}