<script setup>
import { reactive, onMounted, computed, ref } from 'vue'
import { usePartEditStore } from '@/store/part/partEditStore'
import partContext from '@/utils/part/partContext'
import { useConfigStore } from "@/store/configStore"
import { useDrawingStore } from '@/store/drawingStore'
import { useI18n } from "vue-i18n"
import { useProduct } from "@/composables/product"
import axios from "axios";

const { t } = useI18n()
const { products, fetchProducts, updateFilter } = useProduct()

const configStore = useConfigStore()
const partEditStore = usePartEditStore()
const drawingStore = useDrawingStore()

const emit = defineEmits(['submitted'])
const props = defineProps(['part'])
const part = props.part

// reactive part data to bind to the form
const partData = reactive({
  id: part.id ?? null,
  part_nr: part.part_nr,
  has_children: part.has_children ?? true,
  code: part.product?.code ?? ''
})

// Initialize contents with empty names for each locale
const partContents = ref(configStore.siteLangs.map(locale => reactive({ locale, name: '' })));

// computed property to check if all required fields are filled
const isFormValid = computed(() => {
  const allNamesAreFilled = partContents.value.every(content => content.name)
  return partData.part_nr && allNamesAreFilled
})

async function onSubmit() {
  const partToSubmit = partData

  // find the product that matches the code
  const product = products.value.find(p => p.code === partData.code)
  if (product) partToSubmit.product = product

  // set the part contents (name & locale) and prefered name
  partToSubmit.contents = partContents.value
  partToSubmit.name = partContents.value.find(content => content.locale === configStore.lang).name

  // submit the part and notify the stores
  partContext.submitPart(partToSubmit)
  await partEditStore.partNrWasSubmitted(partToSubmit.part_nr)
  drawingStore.drawingHasUnsavedChanges = true

  emit('submitted')
}

onMounted(async () => {
  if (part.product) updateFilter(part.product.code)
  else await fetchProducts()

  // Use part.contents if available, otherwise fetch from server
  if (part.contents && part.contents.length > 0) {
    partContents.value = part.contents.map(content => reactive(content))
  } else if (part.id) {
    // fetch part contents from the server if part.id is available
    const { data } = await axios.get('?action=GetPartContents', {
      params: { id: part.id }
    })
    if (!data || !data.length) return
    partContents.value = data.map(content => reactive(content))
  }
});

// check if the input name matches a product name, if not fetch products from server with the new filter
function onProductInput(name) {
  const product = products.value.find(p => p.name === name)
  if (!product) updateFilter(name)

  // if contents are empty, fill them with the product contents
  else if (!partContents.value.some(content => content.name)) {
    partContents.value = product.contents
        .filter(content => configStore.siteLangs.includes(content.locale))
        .map(content => reactive(content))
  }
}
</script>

<template>
  <v-form @submit.prevent="onSubmit">
    <v-container>
      <v-row class="dataTableRow">
        <v-col cols="12" md="4">
          <v-label for="number">{{ t('part') }} <span class="required">*</span></v-label>
        </v-col>
        <v-col cols="12" md="8">
          <v-text-field
              id="number"
              v-model="partData.part_nr"
              variant="outlined"
              density="compact"
              disabled
              hide-details
          ></v-text-field>
        </v-col>
      </v-row>

      <v-row v-for="content in partContents" :key="content.locale" class="dataTableRow">
        <v-col cols="12" md="4">
          <v-label :for="`name-${content.locale}`">{{ t('name') }} ({{ content.locale.toUpperCase() }}) <span class="required">*</span></v-label>
        </v-col>
        <v-col cols="12" md="8">
          <v-text-field
              :id="`name-${content.locale}`"
              v-model="content.name"
              variant="outlined"
              density="compact"
              hide-details
          ></v-text-field>
        </v-col>
      </v-row>

      <v-row class="dataTableRow">
        <v-col cols="12" md="4">
          <v-label for="hasSubs">{{ t('hasSubs') }}</v-label>
        </v-col>
        <v-col cols="12" md="8">
          <v-checkbox
              id="hasSubs"
              v-model="partData.has_children"
              hide-details
              density="compact"
          ></v-checkbox>
        </v-col>
      </v-row>

      <v-row class="dataTableRow">
        <v-col cols="12" md="4">
          <v-label for="product">{{ t('product') }}</v-label>
        </v-col>
        <v-col cols="12" md="8">
          <v-autocomplete
              id="product"
              v-model="partData.code"
              :items="products"
              item-title="name"
              item-value="code"
              variant="outlined"
              density="compact"
              hide-details
              hide-no-data
              hide-selected
              clearable
              :custom-filter="() => {}"
              @update:search="onProductInput"
              :menu-props="{
                maxWidth: '100%',
                width: 'auto',
              }"
              style="max-width: 250px"
          >
            <template #item="{ props, item }">
              <v-list-item v-bind="props">
                <v-list-item-subtitle class="text-truncate">{{ item.raw.code }}</v-list-item-subtitle>
              </v-list-item>
            </template>
          </v-autocomplete>
        </v-col>
      </v-row>

      <v-row class="mt-4">
        <v-col cols="12">
          <v-btn type="submit" color="primary" :disabled="!isFormValid">{{ t('bevestig') }}</v-btn>
        </v-col>
      </v-row>
    </v-container>
  </v-form>
</template>