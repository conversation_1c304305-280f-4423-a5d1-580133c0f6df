/**
 * Adds an event listener to an element that detects stationary mouse clicks,
 * where the mouse movement during the click is below a given threshold.
 * A custom `stationaryclick` event is dispatched when a stationary click is detected.
 *
 * @param {Element} element - The DOM element to attach the stationary click listener to.
 * @param {number} [movementThreshold=5] - The maximum allowed movement (in pixels) for a click to be considered stationary. Default is 5 pixels.
 *
 * @returns {Function} A cleanup function that removes the `mousedown` and `mouseup` event listeners.
 *                     Call this function to remove the stationary click listener when no longer needed.
 *
 * @example
 * // Usage example:
 * const cleanup = addStationaryClickListener(button, 10)
 * button.addEventListener("stationaryclick", (e) => {
 *   console.log("Stationary click event detected!")
 * })
 * // Later, call cleanup to remove the listener
 * cleanup()
 */
export function addStationaryClickListener(element, movementThreshold = 5) {
  let startX, startY

  function inputDown(event) {
    event.preventDefault()

    if (event.type === "mousedown" && event.button !== 0) return // Only left-click is processed

    // Store the initial position of the click
    const point = event.touches ? event.touches[0] : event // Touch or mouse event
    startX = point.clientX
    startY = point.clientY
  }

  function inputUp(event) {
    event.preventDefault()

    // Evaluate the movement during the click
    const point = event.changedTouches ? event.changedTouches[0] : event
    const movementX = Math.abs(point.clientX - startX)
    const movementY = Math.abs(point.clientY - startY)
    const isWithinThreshold = movementX <= movementThreshold && movementY <= movementThreshold

    if (isWithinThreshold) {
      // Dispatch a custom event
      const stationaryClickEvent = new CustomEvent("stationaryclick", {
        bubbles: true,
        cancelable: true,
        detail: { clientX: startX, clientY: startY },
      })
      element.dispatchEvent(stationaryClickEvent)
    }
  }

  element.addEventListener("mousedown", inputDown)
  element.addEventListener("mouseup", inputUp)
  element.addEventListener("touchstart", inputDown)
  element.addEventListener("touchend", inputUp)
  return () => {
    element.removeEventListener("mousedown", inputDown)
    element.removeEventListener("mouseup", inputUp)
    element.removeEventListener("touchstart", inputDown)
    element.removeEventListener("touchend", inputUp)
  }
}