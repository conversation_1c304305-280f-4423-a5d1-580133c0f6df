<script setup>
// Add 9 directional lights to the scene to evenly light the scene from different angles (subject to be replaced with environment map)
const lightSpots = []
const positions = [-5, 0, 5]
positions.forEach(x => positions.forEach(z => lightSpots.push([x, 5, z])))
</script>

<template>
  <TresAmbientLight/>
  <TresDirectionalLight v-for="(spot, index) in lightSpots" :key="index" :position="spot"/>
</template>