<?php

  namespace domain\visualizer\entity;

  abstract class VisualImportRow {
    protected string $group_nr;
    protected string $description;
    protected string $part_nr;

    public function getGroupNr(): string {
      return $this->group_nr;
    }

    public function getDescription(): string {
      return $this->description;
    }

    public function getPartNr(): string {
      return $this->part_nr;
    }

    public function getHasChildren(): int {
      $groupNr = intval($this->group_nr);
      return (int) ($groupNr < 200 || $groupNr > 299);
    }

    public abstract function validate(&$errors): bool;
  }