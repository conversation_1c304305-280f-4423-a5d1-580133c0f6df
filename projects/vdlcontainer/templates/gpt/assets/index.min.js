(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))r(s);new MutationObserver(s=>{for(const o of s)if(o.type==="childList")for(const i of o.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&r(i)}).observe(document,{childList:!0,subtree:!0});function n(s){const o={};return s.integrity&&(o.integrity=s.integrity),s.referrerPolicy&&(o.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?o.credentials="include":s.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function r(s){if(s.ep)return;s.ep=!0;const o=n(s);fetch(s.href,o)}})();/**
* @vue/shared v3.5.17
* (c) 2018-present <PERSON><PERSON> (<PERSON>) <PERSON> and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function dl(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const Be={},jr=[],fn=()=>{},_v=()=>!1,ei=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),ml=e=>e.startsWith("onUpdate:"),lt=Object.assign,hl=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},Sv=Object.prototype.hasOwnProperty,Fe=(e,t)=>Sv.call(e,t),ce=Array.isArray,Wr=e=>ti(e)==="[object Map]",fd=e=>ti(e)==="[object Set]",he=e=>typeof e=="function",Xe=e=>typeof e=="string",qn=e=>typeof e=="symbol",We=e=>e!==null&&typeof e=="object",dd=e=>(We(e)||he(e))&&he(e.then)&&he(e.catch),md=Object.prototype.toString,ti=e=>md.call(e),wv=e=>ti(e).slice(8,-1),hd=e=>ti(e)==="[object Object]",gl=e=>Xe(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,gs=dl(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),ni=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},Ev=/-(\w)/g,It=ni(e=>e.replace(Ev,(t,n)=>n?n.toUpperCase():"")),Cv=/\B([A-Z])/g,Yn=ni(e=>e.replace(Cv,"-$1").toLowerCase()),Qr=ni(e=>e.charAt(0).toUpperCase()+e.slice(1)),Mi=ni(e=>e?`on${Qr(e)}`:""),Hn=(e,t)=>!Object.is(e,t),po=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},va=(e,t,n,r=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:r,value:n})},pa=e=>{const t=parseFloat(e);return isNaN(t)?e:t},xv=e=>{const t=Xe(e)?Number(e):NaN;return isNaN(t)?e:t};let gc;const ri=()=>gc||(gc=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function be(e){if(ce(e)){const t={};for(let n=0;n<e.length;n++){const r=e[n],s=Xe(r)?Pv(r):be(r);if(s)for(const o in s)t[o]=s[o]}return t}else if(Xe(e)||We(e))return e}const Tv=/;(?![^(]*\))/g,kv=/:([^]+)/,Av=/\/\*[^]*?\*\//g;function Pv(e){const t={};return e.replace(Av,"").split(Tv).forEach(n=>{if(n){const r=n.split(kv);r.length>1&&(t[r[0].trim()]=r[1].trim())}}),t}function de(e){let t="";if(Xe(e))t=e;else if(ce(e))for(let n=0;n<e.length;n++){const r=de(e[n]);r&&(t+=r+" ")}else if(We(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const Iv="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",Ov=dl(Iv);function gd(e){return!!e||e===""}const vd=e=>!!(e&&e.__v_isRef===!0),tt=e=>Xe(e)?e:e==null?"":ce(e)||We(e)&&(e.toString===md||!he(e.toString))?vd(e)?tt(e.value):JSON.stringify(e,pd,2):String(e),pd=(e,t)=>vd(t)?pd(e,t.value):Wr(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[r,s],o)=>(n[Vi(r,o)+" =>"]=s,n),{})}:fd(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>Vi(n))}:qn(t)?Vi(t):We(t)&&!ce(t)&&!hd(t)?String(t):t,Vi=(e,t="")=>{var n;return qn(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let vt;class yd{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=vt,!t&&vt&&(this.index=(vt.scopes||(vt.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=vt;try{return vt=this,t()}finally{vt=n}}}on(){++this._on===1&&(this.prevScope=vt,vt=this)}off(){this._on>0&&--this._on===0&&(vt=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let n,r;for(n=0,r=this.effects.length;n<r;n++)this.effects[n].stop();for(this.effects.length=0,n=0,r=this.cleanups.length;n<r;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,r=this.scopes.length;n<r;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const s=this.parent.scopes.pop();s&&s!==this&&(this.parent.scopes[this.index]=s,s.index=this.index)}this.parent=void 0}}}function Wn(e){return new yd(e)}function vl(){return vt}function St(e,t=!1){vt&&vt.cleanups.push(e)}let He;const $i=new WeakSet;class bd{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,vt&&vt.active&&vt.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,$i.has(this)&&($i.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||Sd(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,vc(this),wd(this);const t=He,n=Xt;He=this,Xt=!0;try{return this.fn()}finally{Ed(this),He=t,Xt=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)bl(t);this.deps=this.depsTail=void 0,vc(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?$i.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){ya(this)&&this.run()}get dirty(){return ya(this)}}let _d=0,vs,ps;function Sd(e,t=!1){if(e.flags|=8,t){e.next=ps,ps=e;return}e.next=vs,vs=e}function pl(){_d++}function yl(){if(--_d>0)return;if(ps){let t=ps;for(ps=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;vs;){let t=vs;for(vs=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(r){e||(e=r)}t=n}}if(e)throw e}function wd(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Ed(e){let t,n=e.depsTail,r=n;for(;r;){const s=r.prevDep;r.version===-1?(r===n&&(n=s),bl(r),Rv(r)):t=r,r.dep.activeLink=r.prevActiveLink,r.prevActiveLink=void 0,r=s}e.deps=t,e.depsTail=n}function ya(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(Cd(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function Cd(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===Ts)||(e.globalVersion=Ts,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!ya(e))))return;e.flags|=2;const t=e.dep,n=He,r=Xt;He=e,Xt=!0;try{wd(e);const s=e.fn(e._value);(t.version===0||Hn(s,e._value))&&(e.flags|=128,e._value=s,t.version++)}catch(s){throw t.version++,s}finally{He=n,Xt=r,Ed(e),e.flags&=-3}}function bl(e,t=!1){const{dep:n,prevSub:r,nextSub:s}=e;if(r&&(r.nextSub=s,e.prevSub=void 0),s&&(s.prevSub=r,e.nextSub=void 0),n.subs===e&&(n.subs=r,!r&&n.computed)){n.computed.flags&=-5;for(let o=n.computed.deps;o;o=o.nextDep)bl(o,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function Rv(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let Xt=!0;const xd=[];function Pn(){xd.push(Xt),Xt=!1}function In(){const e=xd.pop();Xt=e===void 0?!0:e}function vc(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=He;He=void 0;try{t()}finally{He=n}}}let Ts=0;class Lv{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class _l{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,this.__v_skip=!0}track(t){if(!He||!Xt||He===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==He)n=this.activeLink=new Lv(He,this),He.deps?(n.prevDep=He.depsTail,He.depsTail.nextDep=n,He.depsTail=n):He.deps=He.depsTail=n,Td(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const r=n.nextDep;r.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=r),n.prevDep=He.depsTail,n.nextDep=void 0,He.depsTail.nextDep=n,He.depsTail=n,He.deps===n&&(He.deps=r)}return n}trigger(t){this.version++,Ts++,this.notify(t)}notify(t){pl();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{yl()}}}function Td(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let r=t.deps;r;r=r.nextDep)Td(r)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const Ao=new WeakMap,vr=Symbol(""),ba=Symbol(""),ks=Symbol("");function pt(e,t,n){if(Xt&&He){let r=Ao.get(e);r||Ao.set(e,r=new Map);let s=r.get(n);s||(r.set(n,s=new _l),s.map=r,s.key=n),s.track()}}function En(e,t,n,r,s,o){const i=Ao.get(e);if(!i){Ts++;return}const a=l=>{l&&l.trigger()};if(pl(),t==="clear")i.forEach(a);else{const l=ce(e),u=l&&gl(n);if(l&&n==="length"){const c=Number(r);i.forEach((f,d)=>{(d==="length"||d===ks||!qn(d)&&d>=c)&&a(f)})}else switch((n!==void 0||i.has(void 0))&&a(i.get(n)),u&&a(i.get(ks)),t){case"add":l?u&&a(i.get("length")):(a(i.get(vr)),Wr(e)&&a(i.get(ba)));break;case"delete":l||(a(i.get(vr)),Wr(e)&&a(i.get(ba)));break;case"set":Wr(e)&&a(i.get(vr));break}}yl()}function Nv(e,t){const n=Ao.get(e);return n&&n.get(t)}function Rr(e){const t=Ee(e);return t===e?t:(pt(t,"iterate",ks),zt(e)?t:t.map(mt))}function si(e){return pt(e=Ee(e),"iterate",ks),e}const Dv={__proto__:null,[Symbol.iterator](){return Bi(this,Symbol.iterator,mt)},concat(...e){return Rr(this).concat(...e.map(t=>ce(t)?Rr(t):t))},entries(){return Bi(this,"entries",e=>(e[1]=mt(e[1]),e))},every(e,t){return yn(this,"every",e,t,void 0,arguments)},filter(e,t){return yn(this,"filter",e,t,n=>n.map(mt),arguments)},find(e,t){return yn(this,"find",e,t,mt,arguments)},findIndex(e,t){return yn(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return yn(this,"findLast",e,t,mt,arguments)},findLastIndex(e,t){return yn(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return yn(this,"forEach",e,t,void 0,arguments)},includes(...e){return Ui(this,"includes",e)},indexOf(...e){return Ui(this,"indexOf",e)},join(e){return Rr(this).join(e)},lastIndexOf(...e){return Ui(this,"lastIndexOf",e)},map(e,t){return yn(this,"map",e,t,void 0,arguments)},pop(){return as(this,"pop")},push(...e){return as(this,"push",e)},reduce(e,...t){return pc(this,"reduce",e,t)},reduceRight(e,...t){return pc(this,"reduceRight",e,t)},shift(){return as(this,"shift")},some(e,t){return yn(this,"some",e,t,void 0,arguments)},splice(...e){return as(this,"splice",e)},toReversed(){return Rr(this).toReversed()},toSorted(e){return Rr(this).toSorted(e)},toSpliced(...e){return Rr(this).toSpliced(...e)},unshift(...e){return as(this,"unshift",e)},values(){return Bi(this,"values",mt)}};function Bi(e,t,n){const r=si(e),s=r[t]();return r!==e&&!zt(e)&&(s._next=s.next,s.next=()=>{const o=s._next();return o.value&&(o.value=n(o.value)),o}),s}const Fv=Array.prototype;function yn(e,t,n,r,s,o){const i=si(e),a=i!==e&&!zt(e),l=i[t];if(l!==Fv[t]){const f=l.apply(e,o);return a?mt(f):f}let u=n;i!==e&&(a?u=function(f,d){return n.call(this,mt(f),d,e)}:n.length>2&&(u=function(f,d){return n.call(this,f,d,e)}));const c=l.call(i,u,r);return a&&s?s(c):c}function pc(e,t,n,r){const s=si(e);let o=n;return s!==e&&(zt(e)?n.length>3&&(o=function(i,a,l){return n.call(this,i,a,l,e)}):o=function(i,a,l){return n.call(this,i,mt(a),l,e)}),s[t](o,...r)}function Ui(e,t,n){const r=Ee(e);pt(r,"iterate",ks);const s=r[t](...n);return(s===-1||s===!1)&&El(n[0])?(n[0]=Ee(n[0]),r[t](...n)):s}function as(e,t,n=[]){Pn(),pl();const r=Ee(e)[t].apply(e,n);return yl(),In(),r}const Mv=dl("__proto__,__v_isRef,__isVue"),kd=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(qn));function Vv(e){qn(e)||(e=String(e));const t=Ee(this);return pt(t,"has",e),t.hasOwnProperty(e)}class Ad{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,r){if(n==="__v_skip")return t.__v_skip;const s=this._isReadonly,o=this._isShallow;if(n==="__v_isReactive")return!s;if(n==="__v_isReadonly")return s;if(n==="__v_isShallow")return o;if(n==="__v_raw")return r===(s?o?qv:Rd:o?Od:Id).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(r)?t:void 0;const i=ce(t);if(!s){let l;if(i&&(l=Dv[n]))return l;if(n==="hasOwnProperty")return Vv}const a=Reflect.get(t,n,je(t)?t:r);return(qn(n)?kd.has(n):Mv(n))||(s||pt(t,"get",n),o)?a:je(a)?i&&gl(n)?a:a.value:We(a)?s?oi(a):st(a):a}}class Pd extends Ad{constructor(t=!1){super(!1,t)}set(t,n,r,s){let o=t[n];if(!this._isShallow){const l=zn(o);if(!zt(r)&&!zn(r)&&(o=Ee(o),r=Ee(r)),!ce(t)&&je(o)&&!je(r))return l?!1:(o.value=r,!0)}const i=ce(t)&&gl(n)?Number(n)<t.length:Fe(t,n),a=Reflect.set(t,n,r,je(t)?t:s);return t===Ee(s)&&(i?Hn(r,o)&&En(t,"set",n,r):En(t,"add",n,r)),a}deleteProperty(t,n){const r=Fe(t,n);t[n];const s=Reflect.deleteProperty(t,n);return s&&r&&En(t,"delete",n,void 0),s}has(t,n){const r=Reflect.has(t,n);return(!qn(n)||!kd.has(n))&&pt(t,"has",n),r}ownKeys(t){return pt(t,"iterate",ce(t)?"length":vr),Reflect.ownKeys(t)}}class $v extends Ad{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const Bv=new Pd,Uv=new $v,Hv=new Pd(!0);const _a=e=>e,so=e=>Reflect.getPrototypeOf(e);function jv(e,t,n){return function(...r){const s=this.__v_raw,o=Ee(s),i=Wr(o),a=e==="entries"||e===Symbol.iterator&&i,l=e==="keys"&&i,u=s[e](...r),c=n?_a:t?Po:mt;return!t&&pt(o,"iterate",l?ba:vr),{next(){const{value:f,done:d}=u.next();return d?{value:f,done:d}:{value:a?[c(f[0]),c(f[1])]:c(f),done:d}},[Symbol.iterator](){return this}}}}function oo(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function Wv(e,t){const n={get(s){const o=this.__v_raw,i=Ee(o),a=Ee(s);e||(Hn(s,a)&&pt(i,"get",s),pt(i,"get",a));const{has:l}=so(i),u=t?_a:e?Po:mt;if(l.call(i,s))return u(o.get(s));if(l.call(i,a))return u(o.get(a));o!==i&&o.get(s)},get size(){const s=this.__v_raw;return!e&&pt(Ee(s),"iterate",vr),Reflect.get(s,"size",s)},has(s){const o=this.__v_raw,i=Ee(o),a=Ee(s);return e||(Hn(s,a)&&pt(i,"has",s),pt(i,"has",a)),s===a?o.has(s):o.has(s)||o.has(a)},forEach(s,o){const i=this,a=i.__v_raw,l=Ee(a),u=t?_a:e?Po:mt;return!e&&pt(l,"iterate",vr),a.forEach((c,f)=>s.call(o,u(c),u(f),i))}};return lt(n,e?{add:oo("add"),set:oo("set"),delete:oo("delete"),clear:oo("clear")}:{add(s){!t&&!zt(s)&&!zn(s)&&(s=Ee(s));const o=Ee(this);return so(o).has.call(o,s)||(o.add(s),En(o,"add",s,s)),this},set(s,o){!t&&!zt(o)&&!zn(o)&&(o=Ee(o));const i=Ee(this),{has:a,get:l}=so(i);let u=a.call(i,s);u||(s=Ee(s),u=a.call(i,s));const c=l.call(i,s);return i.set(s,o),u?Hn(o,c)&&En(i,"set",s,o):En(i,"add",s,o),this},delete(s){const o=Ee(this),{has:i,get:a}=so(o);let l=i.call(o,s);l||(s=Ee(s),l=i.call(o,s)),a&&a.call(o,s);const u=o.delete(s);return l&&En(o,"delete",s,void 0),u},clear(){const s=Ee(this),o=s.size!==0,i=s.clear();return o&&En(s,"clear",void 0,void 0),i}}),["keys","values","entries",Symbol.iterator].forEach(s=>{n[s]=jv(s,e,t)}),n}function Sl(e,t){const n=Wv(e,t);return(r,s,o)=>s==="__v_isReactive"?!e:s==="__v_isReadonly"?e:s==="__v_raw"?r:Reflect.get(Fe(n,s)&&s in r?n:r,s,o)}const zv={get:Sl(!1,!1)},Kv={get:Sl(!1,!0)},Gv={get:Sl(!0,!1)};const Id=new WeakMap,Od=new WeakMap,Rd=new WeakMap,qv=new WeakMap;function Yv(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Xv(e){return e.__v_skip||!Object.isExtensible(e)?0:Yv(wv(e))}function st(e){return zn(e)?e:wl(e,!1,Bv,zv,Id)}function Ld(e){return wl(e,!1,Hv,Kv,Od)}function oi(e){return wl(e,!0,Uv,Gv,Rd)}function wl(e,t,n,r,s){if(!We(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const o=Xv(e);if(o===0)return e;const i=s.get(e);if(i)return i;const a=new Proxy(e,o===2?r:n);return s.set(e,a),a}function jn(e){return zn(e)?jn(e.__v_raw):!!(e&&e.__v_isReactive)}function zn(e){return!!(e&&e.__v_isReadonly)}function zt(e){return!!(e&&e.__v_isShallow)}function El(e){return e?!!e.__v_raw:!1}function Ee(e){const t=e&&e.__v_raw;return t?Ee(t):e}function ii(e){return!Fe(e,"__v_skip")&&Object.isExtensible(e)&&va(e,"__v_skip",!0),e}const mt=e=>We(e)?st(e):e,Po=e=>We(e)?oi(e):e;function je(e){return e?e.__v_isRef===!0:!1}function J(e){return Nd(e,!1)}function Ae(e){return Nd(e,!0)}function Nd(e,t){return je(e)?e:new Jv(e,t)}class Jv{constructor(t,n){this.dep=new _l,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:Ee(t),this._value=n?t:mt(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,r=this.__v_isShallow||zt(t)||zn(t);t=r?t:Ee(t),Hn(t,n)&&(this._rawValue=t,this._value=r?t:mt(t),this.dep.trigger())}}function ot(e){return je(e)?e.value:e}function gn(e){return he(e)?e():ot(e)}const Zv={get:(e,t,n)=>t==="__v_raw"?e:ot(Reflect.get(e,t,n)),set:(e,t,n,r)=>{const s=e[t];return je(s)&&!je(n)?(s.value=n,!0):Reflect.set(e,t,n,r)}};function Dd(e){return jn(e)?e:new Proxy(e,Zv)}function Cl(e){const t=ce(e)?new Array(e.length):{};for(const n in e)t[n]=Fd(e,n);return t}class Qv{constructor(t,n,r){this._object=t,this._key=n,this._defaultValue=r,this.__v_isRef=!0,this._value=void 0}get value(){const t=this._object[this._key];return this._value=t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return Nv(Ee(this._object),this._key)}}class ep{constructor(t){this._getter=t,this.__v_isRef=!0,this.__v_isReadonly=!0,this._value=void 0}get value(){return this._value=this._getter()}}function X(e,t,n){return je(e)?e:he(e)?new ep(e):We(e)&&arguments.length>1?Fd(e,t,n):J(e)}function Fd(e,t,n){const r=e[t];return je(r)?r:new Qv(e,t,n)}class tp{constructor(t,n,r){this.fn=t,this.setter=n,this._value=void 0,this.dep=new _l(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=Ts-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=r}notify(){if(this.flags|=16,!(this.flags&8)&&He!==this)return Sd(this,!0),!0}get value(){const t=this.dep.track();return Cd(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function np(e,t,n=!1){let r,s;return he(e)?r=e:(r=e.get,s=e.set),new tp(r,s,n)}const io={},Io=new WeakMap;let fr;function rp(e,t=!1,n=fr){if(n){let r=Io.get(n);r||Io.set(n,r=[]),r.push(e)}}function sp(e,t,n=Be){const{immediate:r,deep:s,once:o,scheduler:i,augmentJob:a,call:l}=n,u=y=>s?y:zt(y)||s===!1||s===0?Cn(y,1):Cn(y);let c,f,d,m,g=!1,p=!1;if(je(e)?(f=()=>e.value,g=zt(e)):jn(e)?(f=()=>u(e),g=!0):ce(e)?(p=!0,g=e.some(y=>jn(y)||zt(y)),f=()=>e.map(y=>{if(je(y))return y.value;if(jn(y))return u(y);if(he(y))return l?l(y,2):y()})):he(e)?t?f=l?()=>l(e,2):e:f=()=>{if(d){Pn();try{d()}finally{In()}}const y=fr;fr=c;try{return l?l(e,3,[m]):e(m)}finally{fr=y}}:f=fn,t&&s){const y=f,E=s===!0?1/0:s;f=()=>Cn(y(),E)}const w=vl(),h=()=>{c.stop(),w&&w.active&&hl(w.effects,c)};if(o&&t){const y=t;t=(...E)=>{y(...E),h()}}let v=p?new Array(e.length).fill(io):io;const b=y=>{if(!(!(c.flags&1)||!c.dirty&&!y))if(t){const E=c.run();if(s||g||(p?E.some((P,k)=>Hn(P,v[k])):Hn(E,v))){d&&d();const P=fr;fr=c;try{const k=[E,v===io?void 0:p&&v[0]===io?[]:v,m];v=E,l?l(t,3,k):t(...k)}finally{fr=P}}}else c.run()};return a&&a(b),c=new bd(f),c.scheduler=i?()=>i(b,!1):b,m=y=>rp(y,!1,c),d=c.onStop=()=>{const y=Io.get(c);if(y){if(l)l(y,4);else for(const E of y)E();Io.delete(c)}},t?r?b(!0):v=c.run():i?i(b.bind(null,!0),!0):c.run(),h.pause=c.pause.bind(c),h.resume=c.resume.bind(c),h.stop=h,h}function Cn(e,t=1/0,n){if(t<=0||!We(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,je(e))Cn(e.value,t,n);else if(ce(e))for(let r=0;r<e.length;r++)Cn(e[r],t,n);else if(fd(e)||Wr(e))e.forEach(r=>{Cn(r,t,n)});else if(hd(e)){for(const r in e)Cn(e[r],t,n);for(const r of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,r)&&Cn(e[r],t,n)}return e}/**
* @vue/runtime-core v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function zs(e,t,n,r){try{return r?e(...r):e()}catch(s){ai(s,t,n)}}function Jt(e,t,n,r){if(he(e)){const s=zs(e,t,n,r);return s&&dd(s)&&s.catch(o=>{ai(o,t,n)}),s}if(ce(e)){const s=[];for(let o=0;o<e.length;o++)s.push(Jt(e[o],t,n,r));return s}}function ai(e,t,n,r=!0){const s=t?t.vnode:null,{errorHandler:o,throwUnhandledErrorInProduction:i}=t&&t.appContext.config||Be;if(t){let a=t.parent;const l=t.proxy,u=`https://vuejs.org/error-reference/#runtime-${n}`;for(;a;){const c=a.ec;if(c){for(let f=0;f<c.length;f++)if(c[f](e,l,u)===!1)return}a=a.parent}if(o){Pn(),zs(o,null,10,[e,l,u]),In();return}}op(e,n,s,r,i)}function op(e,t,n,r=!0,s=!1){if(s)throw e;console.error(e)}const xt=[];let ln=-1;const zr=[];let Vn=null,Mr=0;const Md=Promise.resolve();let Oo=null;function bt(e){const t=Oo||Md;return e?t.then(this?e.bind(this):e):t}function ip(e){let t=ln+1,n=xt.length;for(;t<n;){const r=t+n>>>1,s=xt[r],o=As(s);o<e||o===e&&s.flags&2?t=r+1:n=r}return t}function xl(e){if(!(e.flags&1)){const t=As(e),n=xt[xt.length-1];!n||!(e.flags&2)&&t>=As(n)?xt.push(e):xt.splice(ip(t),0,e),e.flags|=1,Vd()}}function Vd(){Oo||(Oo=Md.then(Bd))}function ap(e){ce(e)?zr.push(...e):Vn&&e.id===-1?Vn.splice(Mr+1,0,e):e.flags&1||(zr.push(e),e.flags|=1),Vd()}function yc(e,t,n=ln+1){for(;n<xt.length;n++){const r=xt[n];if(r&&r.flags&2){if(e&&r.id!==e.uid)continue;xt.splice(n,1),n--,r.flags&4&&(r.flags&=-2),r(),r.flags&4||(r.flags&=-2)}}}function $d(e){if(zr.length){const t=[...new Set(zr)].sort((n,r)=>As(n)-As(r));if(zr.length=0,Vn){Vn.push(...t);return}for(Vn=t,Mr=0;Mr<Vn.length;Mr++){const n=Vn[Mr];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}Vn=null,Mr=0}}const As=e=>e.id==null?e.flags&2?-1:1/0:e.id;function Bd(e){try{for(ln=0;ln<xt.length;ln++){const t=xt[ln];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),zs(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;ln<xt.length;ln++){const t=xt[ln];t&&(t.flags&=-2)}ln=-1,xt.length=0,$d(),Oo=null,(xt.length||zr.length)&&Bd()}}let kt=null,Ud=null;function Ro(e){const t=kt;return kt=e,Ud=e&&e.type.__scopeId||null,t}function Ue(e,t=kt,n){if(!t||e._n)return e;const r=(...s)=>{r._d&&Rc(-1);const o=Ro(t);let i;try{i=e(...s)}finally{Ro(o),r._d&&Rc(1)}return i};return r._n=!0,r._c=!0,r._d=!0,r}function Kt(e,t){if(kt===null)return e;const n=di(kt),r=e.dirs||(e.dirs=[]);for(let s=0;s<t.length;s++){let[o,i,a,l=Be]=t[s];o&&(he(o)&&(o={mounted:o,updated:o}),o.deep&&Cn(i),r.push({dir:o,instance:n,value:i,oldValue:void 0,arg:a,modifiers:l}))}return e}function ir(e,t,n,r){const s=e.dirs,o=t&&t.dirs;for(let i=0;i<s.length;i++){const a=s[i];o&&(a.oldValue=o[i].value);let l=a.dir[r];l&&(Pn(),Jt(l,n,8,[e.el,a,e,t]),In())}}const Hd=Symbol("_vte"),jd=e=>e.__isTeleport,ys=e=>e&&(e.disabled||e.disabled===""),bc=e=>e&&(e.defer||e.defer===""),_c=e=>typeof SVGElement<"u"&&e instanceof SVGElement,Sc=e=>typeof MathMLElement=="function"&&e instanceof MathMLElement,Sa=(e,t)=>{const n=e&&e.to;return Xe(n)?t?t(n):null:n},Wd={name:"Teleport",__isTeleport:!0,process(e,t,n,r,s,o,i,a,l,u){const{mc:c,pc:f,pbc:d,o:{insert:m,querySelector:g,createText:p,createComment:w}}=u,h=ys(t.props);let{shapeFlag:v,children:b,dynamicChildren:y}=t;if(e==null){const E=t.el=p(""),P=t.anchor=p("");m(E,n,r),m(P,n,r);const k=(A,H)=>{v&16&&(s&&s.isCE&&(s.ce._teleportTarget=A),c(b,A,H,s,o,i,a,l))},T=()=>{const A=t.target=Sa(t.props,g),H=zd(A,t,p,m);A&&(i!=="svg"&&_c(A)?i="svg":i!=="mathml"&&Sc(A)&&(i="mathml"),h||(k(A,H),yo(t,!1)))};h&&(k(n,P),yo(t,!0)),bc(t.props)?(t.el.__isMounted=!1,Ct(()=>{T(),delete t.el.__isMounted},o)):T()}else{if(bc(t.props)&&e.el.__isMounted===!1){Ct(()=>{Wd.process(e,t,n,r,s,o,i,a,l,u)},o);return}t.el=e.el,t.targetStart=e.targetStart;const E=t.anchor=e.anchor,P=t.target=e.target,k=t.targetAnchor=e.targetAnchor,T=ys(e.props),A=T?n:P,H=T?E:k;if(i==="svg"||_c(P)?i="svg":(i==="mathml"||Sc(P))&&(i="mathml"),y?(d(e.dynamicChildren,y,A,s,o,i,a),Ol(e,t,!0)):l||f(e,t,A,H,s,o,i,a,!1),h)T?t.props&&e.props&&t.props.to!==e.props.to&&(t.props.to=e.props.to):ao(t,n,E,u,1);else if((t.props&&t.props.to)!==(e.props&&e.props.to)){const V=t.target=Sa(t.props,g);V&&ao(t,V,null,u,0)}else T&&ao(t,P,k,u,1);yo(t,h)}},remove(e,t,n,{um:r,o:{remove:s}},o){const{shapeFlag:i,children:a,anchor:l,targetStart:u,targetAnchor:c,target:f,props:d}=e;if(f&&(s(u),s(c)),o&&s(l),i&16){const m=o||!ys(d);for(let g=0;g<a.length;g++){const p=a[g];r(p,t,n,m,!!p.dynamicChildren)}}},move:ao,hydrate:lp};function ao(e,t,n,{o:{insert:r},m:s},o=2){o===0&&r(e.targetAnchor,t,n);const{el:i,anchor:a,shapeFlag:l,children:u,props:c}=e,f=o===2;if(f&&r(i,t,n),(!f||ys(c))&&l&16)for(let d=0;d<u.length;d++)s(u[d],t,n,2);f&&r(a,t,n)}function lp(e,t,n,r,s,o,{o:{nextSibling:i,parentNode:a,querySelector:l,insert:u,createText:c}},f){const d=t.target=Sa(t.props,l);if(d){const m=ys(t.props),g=d._lpa||d.firstChild;if(t.shapeFlag&16)if(m)t.anchor=f(i(e),t,a(e),n,r,s,o),t.targetStart=g,t.targetAnchor=g&&i(g);else{t.anchor=i(e);let p=g;for(;p;){if(p&&p.nodeType===8){if(p.data==="teleport start anchor")t.targetStart=p;else if(p.data==="teleport anchor"){t.targetAnchor=p,d._lpa=t.targetAnchor&&i(t.targetAnchor);break}}p=i(p)}t.targetAnchor||zd(d,t,c,u),f(g&&i(g),t,d,n,r,s,o)}yo(t,m)}return t.anchor&&i(t.anchor)}const cp=Wd;function yo(e,t){const n=e.ctx;if(n&&n.ut){let r,s;for(t?(r=e.el,s=e.anchor):(r=e.targetStart,s=e.targetAnchor);r&&r!==s;)r.nodeType===1&&r.setAttribute("data-v-owner",n.uid),r=r.nextSibling;n.ut()}}function zd(e,t,n,r){const s=t.targetStart=n(""),o=t.targetAnchor=n("");return s[Hd]=o,e&&(r(s,e),r(o,e)),o}const $n=Symbol("_leaveCb"),lo=Symbol("_enterCb");function Kd(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return Gt(()=>{e.isMounted=!0}),Qt(()=>{e.isUnmounting=!0}),e}const jt=[Function,Array],Gd={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:jt,onEnter:jt,onAfterEnter:jt,onEnterCancelled:jt,onBeforeLeave:jt,onLeave:jt,onAfterLeave:jt,onLeaveCancelled:jt,onBeforeAppear:jt,onAppear:jt,onAfterAppear:jt,onAppearCancelled:jt},qd=e=>{const t=e.subTree;return t.component?qd(t.component):t},up={name:"BaseTransition",props:Gd,setup(e,{slots:t}){const n=On(),r=Kd();return()=>{const s=t.default&&Tl(t.default(),!0);if(!s||!s.length)return;const o=Yd(s),i=Ee(e),{mode:a}=i;if(r.isLeaving)return Hi(o);const l=wc(o);if(!l)return Hi(o);let u=Ps(l,i,r,n,f=>u=f);l.type!==Tt&&Sr(l,u);let c=n.subTree&&wc(n.subTree);if(c&&c.type!==Tt&&!dr(l,c)&&qd(n).type!==Tt){let f=Ps(c,i,r,n);if(Sr(c,f),a==="out-in"&&l.type!==Tt)return r.isLeaving=!0,f.afterLeave=()=>{r.isLeaving=!1,n.job.flags&8||n.update(),delete f.afterLeave,c=void 0},Hi(o);a==="in-out"&&l.type!==Tt?f.delayLeave=(d,m,g)=>{const p=Xd(r,c);p[String(c.key)]=c,d[$n]=()=>{m(),d[$n]=void 0,delete u.delayedLeave,c=void 0},u.delayedLeave=()=>{g(),delete u.delayedLeave,c=void 0}}:c=void 0}else c&&(c=void 0);return o}}};function Yd(e){let t=e[0];if(e.length>1){for(const n of e)if(n.type!==Tt){t=n;break}}return t}const fp=up;function Xd(e,t){const{leavingVNodes:n}=e;let r=n.get(t.type);return r||(r=Object.create(null),n.set(t.type,r)),r}function Ps(e,t,n,r,s){const{appear:o,mode:i,persisted:a=!1,onBeforeEnter:l,onEnter:u,onAfterEnter:c,onEnterCancelled:f,onBeforeLeave:d,onLeave:m,onAfterLeave:g,onLeaveCancelled:p,onBeforeAppear:w,onAppear:h,onAfterAppear:v,onAppearCancelled:b}=t,y=String(e.key),E=Xd(n,e),P=(A,H)=>{A&&Jt(A,r,9,H)},k=(A,H)=>{const V=H[1];P(A,H),ce(A)?A.every(D=>D.length<=1)&&V():A.length<=1&&V()},T={mode:i,persisted:a,beforeEnter(A){let H=l;if(!n.isMounted)if(o)H=w||l;else return;A[$n]&&A[$n](!0);const V=E[y];V&&dr(e,V)&&V.el[$n]&&V.el[$n](),P(H,[A])},enter(A){let H=u,V=c,D=f;if(!n.isMounted)if(o)H=h||u,V=v||c,D=b||f;else return;let G=!1;const ee=A[lo]=ne=>{G||(G=!0,ne?P(D,[A]):P(V,[A]),T.delayedLeave&&T.delayedLeave(),A[lo]=void 0)};H?k(H,[A,ee]):ee()},leave(A,H){const V=String(e.key);if(A[lo]&&A[lo](!0),n.isUnmounting)return H();P(d,[A]);let D=!1;const G=A[$n]=ee=>{D||(D=!0,H(),ee?P(p,[A]):P(g,[A]),A[$n]=void 0,E[V]===e&&delete E[V])};E[V]=e,m?k(m,[A,G]):G()},clone(A){const H=Ps(A,t,n,r,s);return s&&s(H),H}};return T}function Hi(e){if(li(e))return e=Kn(e),e.children=null,e}function wc(e){if(!li(e))return jd(e.type)&&e.children?Yd(e.children):e;if(e.component)return e.component.subTree;const{shapeFlag:t,children:n}=e;if(n){if(t&16)return n[0];if(t&32&&he(n.default))return n.default()}}function Sr(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Sr(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function Tl(e,t=!1,n){let r=[],s=0;for(let o=0;o<e.length;o++){let i=e[o];const a=n==null?i.key:String(n)+String(i.key!=null?i.key:o);i.type===ke?(i.patchFlag&128&&s++,r=r.concat(Tl(i.children,t,a))):(t||i.type!==Tt)&&r.push(a!=null?Kn(i,{key:a}):i)}if(s>1)for(let o=0;o<r.length;o++)r[o].patchFlag=-2;return r}/*! #__NO_SIDE_EFFECTS__ */function es(e,t){return he(e)?lt({name:e.name},t,{setup:e}):e}function Ks(){const e=On();return e?(e.appContext.config.idPrefix||"v")+"-"+e.ids[0]+e.ids[1]++:""}function Jd(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function bs(e,t,n,r,s=!1){if(ce(e)){e.forEach((g,p)=>bs(g,t&&(ce(t)?t[p]:t),n,r,s));return}if(_s(r)&&!s){r.shapeFlag&512&&r.type.__asyncResolved&&r.component.subTree.component&&bs(e,t,n,r.component.subTree);return}const o=r.shapeFlag&4?di(r.component):r.el,i=s?null:o,{i:a,r:l}=e,u=t&&t.r,c=a.refs===Be?a.refs={}:a.refs,f=a.setupState,d=Ee(f),m=f===Be?()=>!1:g=>Fe(d,g);if(u!=null&&u!==l&&(Xe(u)?(c[u]=null,m(u)&&(f[u]=null)):je(u)&&(u.value=null)),he(l))zs(l,a,12,[i,c]);else{const g=Xe(l),p=je(l);if(g||p){const w=()=>{if(e.f){const h=g?m(l)?f[l]:c[l]:l.value;s?ce(h)&&hl(h,o):ce(h)?h.includes(o)||h.push(o):g?(c[l]=[o],m(l)&&(f[l]=c[l])):(l.value=[o],e.k&&(c[e.k]=l.value))}else g?(c[l]=i,m(l)&&(f[l]=i)):p&&(l.value=i,e.k&&(c[e.k]=i))};i?(w.id=-1,Ct(w,n)):w()}}}ri().requestIdleCallback;ri().cancelIdleCallback;const _s=e=>!!e.type.__asyncLoader,li=e=>e.type.__isKeepAlive;function dp(e,t){Zd(e,"a",t)}function mp(e,t){Zd(e,"da",t)}function Zd(e,t,n=ft){const r=e.__wdc||(e.__wdc=()=>{let s=n;for(;s;){if(s.isDeactivated)return;s=s.parent}return e()});if(ci(t,r,n),n){let s=n.parent;for(;s&&s.parent;)li(s.parent.vnode)&&hp(r,t,n,s),s=s.parent}}function hp(e,t,n,r){const s=ci(t,e,r,!0);Al(()=>{hl(r[t],s)},n)}function ci(e,t,n=ft,r=!1){if(n){const s=n[e]||(n[e]=[]),o=t.__weh||(t.__weh=(...i)=>{Pn();const a=qs(n),l=Jt(t,n,e,i);return a(),In(),l});return r?s.unshift(o):s.push(o),o}}const Rn=e=>(t,n=ft)=>{(!Os||e==="sp")&&ci(e,(...r)=>t(...r),n)},Gs=Rn("bm"),Gt=Rn("m"),gp=Rn("bu"),kl=Rn("u"),Qt=Rn("bum"),Al=Rn("um"),vp=Rn("sp"),pp=Rn("rtg"),yp=Rn("rtc");function bp(e,t=ft){ci("ec",e,t)}const Qd="components";function em(e,t){return tm(Qd,e,!0,t)||e}const _p=Symbol.for("v-ndc");function Sp(e){return Xe(e)&&tm(Qd,e,!1)||e}function tm(e,t,n=!0,r=!1){const s=kt||ft;if(s){const o=s.type;{const a=ay(o,!1);if(a&&(a===t||a===It(t)||a===Qr(It(t))))return o}const i=Ec(s[e]||o[e],t)||Ec(s.appContext[e],t);return!i&&r?o:i}}function Ec(e,t){return e&&(e[t]||e[It(t)]||e[Qr(It(t))])}function Lo(e,t,n,r){let s;const o=n,i=ce(e);if(i||Xe(e)){const a=i&&jn(e);let l=!1,u=!1;a&&(l=!zt(e),u=zn(e),e=si(e)),s=new Array(e.length);for(let c=0,f=e.length;c<f;c++)s[c]=t(l?u?Po(mt(e[c])):mt(e[c]):e[c],c,void 0,o)}else if(typeof e=="number"){s=new Array(e);for(let a=0;a<e;a++)s[a]=t(a+1,a,void 0,o)}else if(We(e))if(e[Symbol.iterator])s=Array.from(e,(a,l)=>t(a,l,void 0,o));else{const a=Object.keys(e);s=new Array(a.length);for(let l=0,u=a.length;l<u;l++){const c=a[l];s[l]=t(e[c],c,l,o)}}else s=[];return s}const wa=e=>e?bm(e)?di(e):wa(e.parent):null,Ss=lt(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>wa(e.parent),$root:e=>wa(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>rm(e),$forceUpdate:e=>e.f||(e.f=()=>{xl(e.update)}),$nextTick:e=>e.n||(e.n=bt.bind(e.proxy)),$watch:e=>jp.bind(e)}),ji=(e,t)=>e!==Be&&!e.__isScriptSetup&&Fe(e,t),wp={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:r,data:s,props:o,accessCache:i,type:a,appContext:l}=e;let u;if(t[0]!=="$"){const m=i[t];if(m!==void 0)switch(m){case 1:return r[t];case 2:return s[t];case 4:return n[t];case 3:return o[t]}else{if(ji(r,t))return i[t]=1,r[t];if(s!==Be&&Fe(s,t))return i[t]=2,s[t];if((u=e.propsOptions[0])&&Fe(u,t))return i[t]=3,o[t];if(n!==Be&&Fe(n,t))return i[t]=4,n[t];Ea&&(i[t]=0)}}const c=Ss[t];let f,d;if(c)return t==="$attrs"&&pt(e.attrs,"get",""),c(e);if((f=a.__cssModules)&&(f=f[t]))return f;if(n!==Be&&Fe(n,t))return i[t]=4,n[t];if(d=l.config.globalProperties,Fe(d,t))return d[t]},set({_:e},t,n){const{data:r,setupState:s,ctx:o}=e;return ji(s,t)?(s[t]=n,!0):r!==Be&&Fe(r,t)?(r[t]=n,!0):Fe(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(o[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:r,appContext:s,propsOptions:o}},i){let a;return!!n[i]||e!==Be&&Fe(e,i)||ji(t,i)||(a=o[0])&&Fe(a,i)||Fe(r,i)||Fe(Ss,i)||Fe(s.config.globalProperties,i)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:Fe(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Cc(e){return ce(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let Ea=!0;function Ep(e){const t=rm(e),n=e.proxy,r=e.ctx;Ea=!1,t.beforeCreate&&xc(t.beforeCreate,e,"bc");const{data:s,computed:o,methods:i,watch:a,provide:l,inject:u,created:c,beforeMount:f,mounted:d,beforeUpdate:m,updated:g,activated:p,deactivated:w,beforeDestroy:h,beforeUnmount:v,destroyed:b,unmounted:y,render:E,renderTracked:P,renderTriggered:k,errorCaptured:T,serverPrefetch:A,expose:H,inheritAttrs:V,components:D,directives:G,filters:ee}=t;if(u&&Cp(u,r,null),i)for(const Z in i){const re=i[Z];he(re)&&(r[Z]=re.bind(n))}if(s){const Z=s.call(n,n);We(Z)&&(e.data=st(Z))}if(Ea=!0,o)for(const Z in o){const re=o[Z],pe=he(re)?re.bind(n,n):he(re.get)?re.get.bind(n,n):fn,ue=!he(re)&&he(re.set)?re.set.bind(n):fn,xe=U({get:pe,set:ue});Object.defineProperty(r,Z,{enumerable:!0,configurable:!0,get:()=>xe.value,set:_e=>xe.value=_e})}if(a)for(const Z in a)nm(a[Z],r,n,Z);if(l){const Z=he(l)?l.call(n):l;Reflect.ownKeys(Z).forEach(re=>{Ft(re,Z[re])})}c&&xc(c,e,"c");function oe(Z,re){ce(re)?re.forEach(pe=>Z(pe.bind(n))):re&&Z(re.bind(n))}if(oe(Gs,f),oe(Gt,d),oe(gp,m),oe(kl,g),oe(dp,p),oe(mp,w),oe(bp,T),oe(yp,P),oe(pp,k),oe(Qt,v),oe(Al,y),oe(vp,A),ce(H))if(H.length){const Z=e.exposed||(e.exposed={});H.forEach(re=>{Object.defineProperty(Z,re,{get:()=>n[re],set:pe=>n[re]=pe})})}else e.exposed||(e.exposed={});E&&e.render===fn&&(e.render=E),V!=null&&(e.inheritAttrs=V),D&&(e.components=D),G&&(e.directives=G),A&&Jd(e)}function Cp(e,t,n=fn){ce(e)&&(e=Ca(e));for(const r in e){const s=e[r];let o;We(s)?"default"in s?o=Le(s.from||r,s.default,!0):o=Le(s.from||r):o=Le(s),je(o)?Object.defineProperty(t,r,{enumerable:!0,configurable:!0,get:()=>o.value,set:i=>o.value=i}):t[r]=o}}function xc(e,t,n){Jt(ce(e)?e.map(r=>r.bind(t.proxy)):e.bind(t.proxy),t,n)}function nm(e,t,n,r){let s=r.includes(".")?hm(n,r):()=>n[r];if(Xe(e)){const o=t[e];he(o)&&le(s,o)}else if(he(e))le(s,e.bind(n));else if(We(e))if(ce(e))e.forEach(o=>nm(o,t,n,r));else{const o=he(e.handler)?e.handler.bind(n):t[e.handler];he(o)&&le(s,o,e)}}function rm(e){const t=e.type,{mixins:n,extends:r}=t,{mixins:s,optionsCache:o,config:{optionMergeStrategies:i}}=e.appContext,a=o.get(t);let l;return a?l=a:!s.length&&!n&&!r?l=t:(l={},s.length&&s.forEach(u=>No(l,u,i,!0)),No(l,t,i)),We(t)&&o.set(t,l),l}function No(e,t,n,r=!1){const{mixins:s,extends:o}=t;o&&No(e,o,n,!0),s&&s.forEach(i=>No(e,i,n,!0));for(const i in t)if(!(r&&i==="expose")){const a=xp[i]||n&&n[i];e[i]=a?a(e[i],t[i]):t[i]}return e}const xp={data:Tc,props:kc,emits:kc,methods:hs,computed:hs,beforeCreate:wt,created:wt,beforeMount:wt,mounted:wt,beforeUpdate:wt,updated:wt,beforeDestroy:wt,beforeUnmount:wt,destroyed:wt,unmounted:wt,activated:wt,deactivated:wt,errorCaptured:wt,serverPrefetch:wt,components:hs,directives:hs,watch:kp,provide:Tc,inject:Tp};function Tc(e,t){return t?e?function(){return lt(he(e)?e.call(this,this):e,he(t)?t.call(this,this):t)}:t:e}function Tp(e,t){return hs(Ca(e),Ca(t))}function Ca(e){if(ce(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function wt(e,t){return e?[...new Set([].concat(e,t))]:t}function hs(e,t){return e?lt(Object.create(null),e,t):t}function kc(e,t){return e?ce(e)&&ce(t)?[...new Set([...e,...t])]:lt(Object.create(null),Cc(e),Cc(t??{})):t}function kp(e,t){if(!e)return t;if(!t)return e;const n=lt(Object.create(null),e);for(const r in t)n[r]=wt(e[r],t[r]);return n}function sm(){return{app:null,config:{isNativeTag:_v,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Ap=0;function Pp(e,t){return function(r,s=null){he(r)||(r=lt({},r)),s!=null&&!We(s)&&(s=null);const o=sm(),i=new WeakSet,a=[];let l=!1;const u=o.app={_uid:Ap++,_component:r,_props:s,_container:null,_context:o,_instance:null,version:cy,get config(){return o.config},set config(c){},use(c,...f){return i.has(c)||(c&&he(c.install)?(i.add(c),c.install(u,...f)):he(c)&&(i.add(c),c(u,...f))),u},mixin(c){return o.mixins.includes(c)||o.mixins.push(c),u},component(c,f){return f?(o.components[c]=f,u):o.components[c]},directive(c,f){return f?(o.directives[c]=f,u):o.directives[c]},mount(c,f,d){if(!l){const m=u._ceVNode||R(r,s);return m.appContext=o,d===!0?d="svg":d===!1&&(d=void 0),e(m,c,d),l=!0,u._container=c,c.__vue_app__=u,di(m.component)}},onUnmount(c){a.push(c)},unmount(){l&&(Jt(a,u._instance,16),e(null,u._container),delete u._container.__vue_app__)},provide(c,f){return o.provides[c]=f,u},runWithContext(c){const f=pr;pr=u;try{return c()}finally{pr=f}}};return u}}let pr=null;function Ft(e,t){if(ft){let n=ft.provides;const r=ft.parent&&ft.parent.provides;r===n&&(n=ft.provides=Object.create(r)),n[e]=t}}function Le(e,t,n=!1){const r=ft||kt;if(r||pr){let s=pr?pr._context.provides:r?r.parent==null||r.ce?r.vnode.appContext&&r.vnode.appContext.provides:r.parent.provides:void 0;if(s&&e in s)return s[e];if(arguments.length>1)return n&&he(t)?t.call(r&&r.proxy):t}}function Ip(){return!!(ft||kt||pr)}const om={},im=()=>Object.create(om),am=e=>Object.getPrototypeOf(e)===om;function Op(e,t,n,r=!1){const s={},o=im();e.propsDefaults=Object.create(null),lm(e,t,s,o);for(const i in e.propsOptions[0])i in s||(s[i]=void 0);n?e.props=r?s:Ld(s):e.type.props?e.props=s:e.props=o,e.attrs=o}function Rp(e,t,n,r){const{props:s,attrs:o,vnode:{patchFlag:i}}=e,a=Ee(s),[l]=e.propsOptions;let u=!1;if((r||i>0)&&!(i&16)){if(i&8){const c=e.vnode.dynamicProps;for(let f=0;f<c.length;f++){let d=c[f];if(ui(e.emitsOptions,d))continue;const m=t[d];if(l)if(Fe(o,d))m!==o[d]&&(o[d]=m,u=!0);else{const g=It(d);s[g]=xa(l,a,g,m,e,!1)}else m!==o[d]&&(o[d]=m,u=!0)}}}else{lm(e,t,s,o)&&(u=!0);let c;for(const f in a)(!t||!Fe(t,f)&&((c=Yn(f))===f||!Fe(t,c)))&&(l?n&&(n[f]!==void 0||n[c]!==void 0)&&(s[f]=xa(l,a,f,void 0,e,!0)):delete s[f]);if(o!==a)for(const f in o)(!t||!Fe(t,f))&&(delete o[f],u=!0)}u&&En(e.attrs,"set","")}function lm(e,t,n,r){const[s,o]=e.propsOptions;let i=!1,a;if(t)for(let l in t){if(gs(l))continue;const u=t[l];let c;s&&Fe(s,c=It(l))?!o||!o.includes(c)?n[c]=u:(a||(a={}))[c]=u:ui(e.emitsOptions,l)||(!(l in r)||u!==r[l])&&(r[l]=u,i=!0)}if(o){const l=Ee(n),u=a||Be;for(let c=0;c<o.length;c++){const f=o[c];n[f]=xa(s,l,f,u[f],e,!Fe(u,f))}}return i}function xa(e,t,n,r,s,o){const i=e[n];if(i!=null){const a=Fe(i,"default");if(a&&r===void 0){const l=i.default;if(i.type!==Function&&!i.skipFactory&&he(l)){const{propsDefaults:u}=s;if(n in u)r=u[n];else{const c=qs(s);r=u[n]=l.call(null,t),c()}}else r=l;s.ce&&s.ce._setProp(n,r)}i[0]&&(o&&!a?r=!1:i[1]&&(r===""||r===Yn(n))&&(r=!0))}return r}const Lp=new WeakMap;function cm(e,t,n=!1){const r=n?Lp:t.propsCache,s=r.get(e);if(s)return s;const o=e.props,i={},a=[];let l=!1;if(!he(e)){const c=f=>{l=!0;const[d,m]=cm(f,t,!0);lt(i,d),m&&a.push(...m)};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}if(!o&&!l)return We(e)&&r.set(e,jr),jr;if(ce(o))for(let c=0;c<o.length;c++){const f=It(o[c]);Ac(f)&&(i[f]=Be)}else if(o)for(const c in o){const f=It(c);if(Ac(f)){const d=o[c],m=i[f]=ce(d)||he(d)?{type:d}:lt({},d),g=m.type;let p=!1,w=!0;if(ce(g))for(let h=0;h<g.length;++h){const v=g[h],b=he(v)&&v.name;if(b==="Boolean"){p=!0;break}else b==="String"&&(w=!1)}else p=he(g)&&g.name==="Boolean";m[0]=p,m[1]=w,(p||Fe(m,"default"))&&a.push(f)}}const u=[i,a];return We(e)&&r.set(e,u),u}function Ac(e){return e[0]!=="$"&&!gs(e)}const Pl=e=>e[0]==="_"||e==="$stable",Il=e=>ce(e)?e.map(cn):[cn(e)],Np=(e,t,n)=>{if(t._n)return t;const r=Ue((...s)=>Il(t(...s)),n);return r._c=!1,r},um=(e,t,n)=>{const r=e._ctx;for(const s in e){if(Pl(s))continue;const o=e[s];if(he(o))t[s]=Np(s,o,r);else if(o!=null){const i=Il(o);t[s]=()=>i}}},fm=(e,t)=>{const n=Il(t);e.slots.default=()=>n},dm=(e,t,n)=>{for(const r in t)(n||!Pl(r))&&(e[r]=t[r])},Dp=(e,t,n)=>{const r=e.slots=im();if(e.vnode.shapeFlag&32){const s=t.__;s&&va(r,"__",s,!0);const o=t._;o?(dm(r,t,n),n&&va(r,"_",o,!0)):um(t,r)}else t&&fm(e,t)},Fp=(e,t,n)=>{const{vnode:r,slots:s}=e;let o=!0,i=Be;if(r.shapeFlag&32){const a=t._;a?n&&a===1?o=!1:dm(s,t,n):(o=!t.$stable,um(t,s)),i=t}else t&&(fm(e,t),i={default:1});if(o)for(const a in s)!Pl(a)&&i[a]==null&&delete s[a]},Ct=Xp;function Mp(e){return Vp(e)}function Vp(e,t){const n=ri();n.__VUE__=!0;const{insert:r,remove:s,patchProp:o,createElement:i,createText:a,createComment:l,setText:u,setElementText:c,parentNode:f,nextSibling:d,setScopeId:m=fn,insertStaticContent:g}=e,p=(C,x,L,j=null,z=null,W=null,_=void 0,S=null,O=!!x.dynamicChildren)=>{if(C===x)return;C&&!dr(C,x)&&(j=F(C),_e(C,z,W,!0),C=null),x.patchFlag===-2&&(O=!1,x.dynamicChildren=null);const{type:M,ref:Q,shapeFlag:K}=x;switch(M){case ts:w(C,x,L,j);break;case Tt:h(C,x,L,j);break;case zi:C==null&&v(x,L,j,_);break;case ke:D(C,x,L,j,z,W,_,S,O);break;default:K&1?E(C,x,L,j,z,W,_,S,O):K&6?G(C,x,L,j,z,W,_,S,O):(K&64||K&128)&&M.process(C,x,L,j,z,W,_,S,O,ie)}Q!=null&&z?bs(Q,C&&C.ref,W,x||C,!x):Q==null&&C&&C.ref!=null&&bs(C.ref,null,W,C,!0)},w=(C,x,L,j)=>{if(C==null)r(x.el=a(x.children),L,j);else{const z=x.el=C.el;x.children!==C.children&&u(z,x.children)}},h=(C,x,L,j)=>{C==null?r(x.el=l(x.children||""),L,j):x.el=C.el},v=(C,x,L,j)=>{[C.el,C.anchor]=g(C.children,x,L,j,C.el,C.anchor)},b=({el:C,anchor:x},L,j)=>{let z;for(;C&&C!==x;)z=d(C),r(C,L,j),C=z;r(x,L,j)},y=({el:C,anchor:x})=>{let L;for(;C&&C!==x;)L=d(C),s(C),C=L;s(x)},E=(C,x,L,j,z,W,_,S,O)=>{x.type==="svg"?_="svg":x.type==="math"&&(_="mathml"),C==null?P(x,L,j,z,W,_,S,O):A(C,x,z,W,_,S,O)},P=(C,x,L,j,z,W,_,S)=>{let O,M;const{props:Q,shapeFlag:K,transition:I,dirs:B}=C;if(O=C.el=i(C.type,W,Q&&Q.is,Q),K&8?c(O,C.children):K&16&&T(C.children,O,null,j,z,Wi(C,W),_,S),B&&ir(C,null,j,"created"),k(O,C,C.scopeId,_,j),Q){for(const fe in Q)fe!=="value"&&!gs(fe)&&o(O,fe,null,Q[fe],W,j);"value"in Q&&o(O,"value",null,Q.value,W),(M=Q.onVnodeBeforeMount)&&rn(M,j,C)}B&&ir(C,null,j,"beforeMount");const ae=$p(z,I);ae&&I.beforeEnter(O),r(O,x,L),((M=Q&&Q.onVnodeMounted)||ae||B)&&Ct(()=>{M&&rn(M,j,C),ae&&I.enter(O),B&&ir(C,null,j,"mounted")},z)},k=(C,x,L,j,z)=>{if(L&&m(C,L),j)for(let W=0;W<j.length;W++)m(C,j[W]);if(z){let W=z.subTree;if(x===W||vm(W.type)&&(W.ssContent===x||W.ssFallback===x)){const _=z.vnode;k(C,_,_.scopeId,_.slotScopeIds,z.parent)}}},T=(C,x,L,j,z,W,_,S,O=0)=>{for(let M=O;M<C.length;M++){const Q=C[M]=S?Bn(C[M]):cn(C[M]);p(null,Q,x,L,j,z,W,_,S)}},A=(C,x,L,j,z,W,_)=>{const S=x.el=C.el;let{patchFlag:O,dynamicChildren:M,dirs:Q}=x;O|=C.patchFlag&16;const K=C.props||Be,I=x.props||Be;let B;if(L&&ar(L,!1),(B=I.onVnodeBeforeUpdate)&&rn(B,L,x,C),Q&&ir(x,C,L,"beforeUpdate"),L&&ar(L,!0),(K.innerHTML&&I.innerHTML==null||K.textContent&&I.textContent==null)&&c(S,""),M?H(C.dynamicChildren,M,S,L,j,Wi(x,z),W):_||re(C,x,S,null,L,j,Wi(x,z),W,!1),O>0){if(O&16)V(S,K,I,L,z);else if(O&2&&K.class!==I.class&&o(S,"class",null,I.class,z),O&4&&o(S,"style",K.style,I.style,z),O&8){const ae=x.dynamicProps;for(let fe=0;fe<ae.length;fe++){const we=ae[fe],nt=K[we],Ze=I[we];(Ze!==nt||we==="value")&&o(S,we,nt,Ze,z,L)}}O&1&&C.children!==x.children&&c(S,x.children)}else!_&&M==null&&V(S,K,I,L,z);((B=I.onVnodeUpdated)||Q)&&Ct(()=>{B&&rn(B,L,x,C),Q&&ir(x,C,L,"updated")},j)},H=(C,x,L,j,z,W,_)=>{for(let S=0;S<x.length;S++){const O=C[S],M=x[S],Q=O.el&&(O.type===ke||!dr(O,M)||O.shapeFlag&198)?f(O.el):L;p(O,M,Q,null,j,z,W,_,!0)}},V=(C,x,L,j,z)=>{if(x!==L){if(x!==Be)for(const W in x)!gs(W)&&!(W in L)&&o(C,W,x[W],null,z,j);for(const W in L){if(gs(W))continue;const _=L[W],S=x[W];_!==S&&W!=="value"&&o(C,W,S,_,z,j)}"value"in L&&o(C,"value",x.value,L.value,z)}},D=(C,x,L,j,z,W,_,S,O)=>{const M=x.el=C?C.el:a(""),Q=x.anchor=C?C.anchor:a("");let{patchFlag:K,dynamicChildren:I,slotScopeIds:B}=x;B&&(S=S?S.concat(B):B),C==null?(r(M,L,j),r(Q,L,j),T(x.children||[],L,Q,z,W,_,S,O)):K>0&&K&64&&I&&C.dynamicChildren?(H(C.dynamicChildren,I,L,z,W,_,S),(x.key!=null||z&&x===z.subTree)&&Ol(C,x,!0)):re(C,x,L,Q,z,W,_,S,O)},G=(C,x,L,j,z,W,_,S,O)=>{x.slotScopeIds=S,C==null?x.shapeFlag&512?z.ctx.activate(x,L,j,_,O):ee(x,L,j,z,W,_,O):ne(C,x,O)},ee=(C,x,L,j,z,W,_)=>{const S=C.component=ny(C,j,z);if(li(C)&&(S.ctx.renderer=ie),ry(S,!1,_),S.asyncDep){if(z&&z.registerDep(S,oe,_),!C.el){const O=S.subTree=R(Tt);h(null,O,x,L)}}else oe(S,C,x,L,z,W,_)},ne=(C,x,L)=>{const j=x.component=C.component;if(qp(C,x,L))if(j.asyncDep&&!j.asyncResolved){Z(j,x,L);return}else j.next=x,j.update();else x.el=C.el,j.vnode=x},oe=(C,x,L,j,z,W,_)=>{const S=()=>{if(C.isMounted){let{next:K,bu:I,u:B,parent:ae,vnode:fe}=C;{const qt=mm(C);if(qt){K&&(K.el=fe.el,Z(C,K,_)),qt.asyncDep.then(()=>{C.isUnmounted||S()});return}}let we=K,nt;ar(C,!1),K?(K.el=fe.el,Z(C,K,_)):K=fe,I&&po(I),(nt=K.props&&K.props.onVnodeBeforeUpdate)&&rn(nt,ae,K,fe),ar(C,!0);const Ze=Ic(C),Ht=C.subTree;C.subTree=Ze,p(Ht,Ze,f(Ht.el),F(Ht),C,z,W),K.el=Ze.el,we===null&&Yp(C,Ze.el),B&&Ct(B,z),(nt=K.props&&K.props.onVnodeUpdated)&&Ct(()=>rn(nt,ae,K,fe),z)}else{let K;const{el:I,props:B}=x,{bm:ae,m:fe,parent:we,root:nt,type:Ze}=C,Ht=_s(x);ar(C,!1),ae&&po(ae),!Ht&&(K=B&&B.onVnodeBeforeMount)&&rn(K,we,x),ar(C,!0);{nt.ce&&nt.ce._def.shadowRoot!==!1&&nt.ce._injectChildStyle(Ze);const qt=C.subTree=Ic(C);p(null,qt,L,j,C,z,W),x.el=qt.el}if(fe&&Ct(fe,z),!Ht&&(K=B&&B.onVnodeMounted)){const qt=x;Ct(()=>rn(K,we,qt),z)}(x.shapeFlag&256||we&&_s(we.vnode)&&we.vnode.shapeFlag&256)&&C.a&&Ct(C.a,z),C.isMounted=!0,x=L=j=null}};C.scope.on();const O=C.effect=new bd(S);C.scope.off();const M=C.update=O.run.bind(O),Q=C.job=O.runIfDirty.bind(O);Q.i=C,Q.id=C.uid,O.scheduler=()=>xl(Q),ar(C,!0),M()},Z=(C,x,L)=>{x.component=C;const j=C.vnode.props;C.vnode=x,C.next=null,Rp(C,x.props,j,L),Fp(C,x.children,L),Pn(),yc(C),In()},re=(C,x,L,j,z,W,_,S,O=!1)=>{const M=C&&C.children,Q=C?C.shapeFlag:0,K=x.children,{patchFlag:I,shapeFlag:B}=x;if(I>0){if(I&128){ue(M,K,L,j,z,W,_,S,O);return}else if(I&256){pe(M,K,L,j,z,W,_,S,O);return}}B&8?(Q&16&&Ne(M,z,W),K!==M&&c(L,K)):Q&16?B&16?ue(M,K,L,j,z,W,_,S,O):Ne(M,z,W,!0):(Q&8&&c(L,""),B&16&&T(K,L,j,z,W,_,S,O))},pe=(C,x,L,j,z,W,_,S,O)=>{C=C||jr,x=x||jr;const M=C.length,Q=x.length,K=Math.min(M,Q);let I;for(I=0;I<K;I++){const B=x[I]=O?Bn(x[I]):cn(x[I]);p(C[I],B,L,null,z,W,_,S,O)}M>Q?Ne(C,z,W,!0,!1,K):T(x,L,j,z,W,_,S,O,K)},ue=(C,x,L,j,z,W,_,S,O)=>{let M=0;const Q=x.length;let K=C.length-1,I=Q-1;for(;M<=K&&M<=I;){const B=C[M],ae=x[M]=O?Bn(x[M]):cn(x[M]);if(dr(B,ae))p(B,ae,L,null,z,W,_,S,O);else break;M++}for(;M<=K&&M<=I;){const B=C[K],ae=x[I]=O?Bn(x[I]):cn(x[I]);if(dr(B,ae))p(B,ae,L,null,z,W,_,S,O);else break;K--,I--}if(M>K){if(M<=I){const B=I+1,ae=B<Q?x[B].el:j;for(;M<=I;)p(null,x[M]=O?Bn(x[M]):cn(x[M]),L,ae,z,W,_,S,O),M++}}else if(M>I)for(;M<=K;)_e(C[M],z,W,!0),M++;else{const B=M,ae=M,fe=new Map;for(M=ae;M<=I;M++){const Rt=x[M]=O?Bn(x[M]):cn(x[M]);Rt.key!=null&&fe.set(Rt.key,M)}let we,nt=0;const Ze=I-ae+1;let Ht=!1,qt=0;const is=new Array(Ze);for(M=0;M<Ze;M++)is[M]=0;for(M=B;M<=K;M++){const Rt=C[M];if(nt>=Ze){_e(Rt,z,W,!0);continue}let nn;if(Rt.key!=null)nn=fe.get(Rt.key);else for(we=ae;we<=I;we++)if(is[we-ae]===0&&dr(Rt,x[we])){nn=we;break}nn===void 0?_e(Rt,z,W,!0):(is[nn-ae]=M+1,nn>=qt?qt=nn:Ht=!0,p(Rt,x[nn],L,null,z,W,_,S,O),nt++)}const mc=Ht?Bp(is):jr;for(we=mc.length-1,M=Ze-1;M>=0;M--){const Rt=ae+M,nn=x[Rt],hc=Rt+1<Q?x[Rt+1].el:j;is[M]===0?p(null,nn,L,hc,z,W,_,S,O):Ht&&(we<0||M!==mc[we]?xe(nn,L,hc,2):we--)}}},xe=(C,x,L,j,z=null)=>{const{el:W,type:_,transition:S,children:O,shapeFlag:M}=C;if(M&6){xe(C.component.subTree,x,L,j);return}if(M&128){C.suspense.move(x,L,j);return}if(M&64){_.move(C,x,L,ie);return}if(_===ke){r(W,x,L);for(let K=0;K<O.length;K++)xe(O[K],x,L,j);r(C.anchor,x,L);return}if(_===zi){b(C,x,L);return}if(j!==2&&M&1&&S)if(j===0)S.beforeEnter(W),r(W,x,L),Ct(()=>S.enter(W),z);else{const{leave:K,delayLeave:I,afterLeave:B}=S,ae=()=>{C.ctx.isUnmounted?s(W):r(W,x,L)},fe=()=>{K(W,()=>{ae(),B&&B()})};I?I(W,ae,fe):fe()}else r(W,x,L)},_e=(C,x,L,j=!1,z=!1)=>{const{type:W,props:_,ref:S,children:O,dynamicChildren:M,shapeFlag:Q,patchFlag:K,dirs:I,cacheIndex:B}=C;if(K===-2&&(z=!1),S!=null&&(Pn(),bs(S,null,L,C,!0),In()),B!=null&&(x.renderCache[B]=void 0),Q&256){x.ctx.deactivate(C);return}const ae=Q&1&&I,fe=!_s(C);let we;if(fe&&(we=_&&_.onVnodeBeforeUnmount)&&rn(we,x,C),Q&6)Se(C.component,L,j);else{if(Q&128){C.suspense.unmount(L,j);return}ae&&ir(C,null,x,"beforeUnmount"),Q&64?C.type.remove(C,x,L,ie,j):M&&!M.hasOnce&&(W!==ke||K>0&&K&64)?Ne(M,x,L,!1,!0):(W===ke&&K&384||!z&&Q&16)&&Ne(O,x,L),j&&Me(C)}(fe&&(we=_&&_.onVnodeUnmounted)||ae)&&Ct(()=>{we&&rn(we,x,C),ae&&ir(C,null,x,"unmounted")},L)},Me=C=>{const{type:x,el:L,anchor:j,transition:z}=C;if(x===ke){Ge(L,j);return}if(x===zi){y(C);return}const W=()=>{s(L),z&&!z.persisted&&z.afterLeave&&z.afterLeave()};if(C.shapeFlag&1&&z&&!z.persisted){const{leave:_,delayLeave:S}=z,O=()=>_(L,W);S?S(C.el,W,O):O()}else W()},Ge=(C,x)=>{let L;for(;C!==x;)L=d(C),s(C),C=L;s(x)},Se=(C,x,L)=>{const{bum:j,scope:z,job:W,subTree:_,um:S,m:O,a:M,parent:Q,slots:{__:K}}=C;Pc(O),Pc(M),j&&po(j),Q&&ce(K)&&K.forEach(I=>{Q.renderCache[I]=void 0}),z.stop(),W&&(W.flags|=8,_e(_,C,x,L)),S&&Ct(S,x),Ct(()=>{C.isUnmounted=!0},x),x&&x.pendingBranch&&!x.isUnmounted&&C.asyncDep&&!C.asyncResolved&&C.suspenseId===x.pendingId&&(x.deps--,x.deps===0&&x.resolve())},Ne=(C,x,L,j=!1,z=!1,W=0)=>{for(let _=W;_<C.length;_++)_e(C[_],x,L,j,z)},F=C=>{if(C.shapeFlag&6)return F(C.component.subTree);if(C.shapeFlag&128)return C.suspense.next();const x=d(C.anchor||C.el),L=x&&x[Hd];return L?d(L):x};let q=!1;const Y=(C,x,L)=>{C==null?x._vnode&&_e(x._vnode,null,null,!0):p(x._vnode||null,C,x,null,null,null,L),x._vnode=C,q||(q=!0,yc(),$d(),q=!1)},ie={p,um:_e,m:xe,r:Me,mt:ee,mc:T,pc:re,pbc:H,n:F,o:e};return{render:Y,hydrate:void 0,createApp:Pp(Y)}}function Wi({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function ar({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function $p(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function Ol(e,t,n=!1){const r=e.children,s=t.children;if(ce(r)&&ce(s))for(let o=0;o<r.length;o++){const i=r[o];let a=s[o];a.shapeFlag&1&&!a.dynamicChildren&&((a.patchFlag<=0||a.patchFlag===32)&&(a=s[o]=Bn(s[o]),a.el=i.el),!n&&a.patchFlag!==-2&&Ol(i,a)),a.type===ts&&(a.el=i.el),a.type===Tt&&!a.el&&(a.el=i.el)}}function Bp(e){const t=e.slice(),n=[0];let r,s,o,i,a;const l=e.length;for(r=0;r<l;r++){const u=e[r];if(u!==0){if(s=n[n.length-1],e[s]<u){t[r]=s,n.push(r);continue}for(o=0,i=n.length-1;o<i;)a=o+i>>1,e[n[a]]<u?o=a+1:i=a;u<e[n[o]]&&(o>0&&(t[r]=n[o-1]),n[o]=r)}}for(o=n.length,i=n[o-1];o-- >0;)n[o]=i,i=t[i];return n}function mm(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:mm(t)}function Pc(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const Up=Symbol.for("v-scx"),Hp=()=>Le(Up);function en(e,t){return Rl(e,null,t)}function le(e,t,n){return Rl(e,t,n)}function Rl(e,t,n=Be){const{immediate:r,deep:s,flush:o,once:i}=n,a=lt({},n),l=t&&r||!t&&o!=="post";let u;if(Os){if(o==="sync"){const m=Hp();u=m.__watcherHandles||(m.__watcherHandles=[])}else if(!l){const m=()=>{};return m.stop=fn,m.resume=fn,m.pause=fn,m}}const c=ft;a.call=(m,g,p)=>Jt(m,c,g,p);let f=!1;o==="post"?a.scheduler=m=>{Ct(m,c&&c.suspense)}:o!=="sync"&&(f=!0,a.scheduler=(m,g)=>{g?m():xl(m)}),a.augmentJob=m=>{t&&(m.flags|=4),f&&(m.flags|=2,c&&(m.id=c.uid,m.i=c))};const d=sp(e,t,a);return Os&&(u?u.push(d):l&&d()),d}function jp(e,t,n){const r=this.proxy,s=Xe(e)?e.includes(".")?hm(r,e):()=>r[e]:e.bind(r,r);let o;he(t)?o=t:(o=t.handler,n=t);const i=qs(this),a=Rl(s,o.bind(r),n);return i(),a}function hm(e,t){const n=t.split(".");return()=>{let r=e;for(let s=0;s<n.length&&r;s++)r=r[n[s]];return r}}const Wp=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${It(t)}Modifiers`]||e[`${Yn(t)}Modifiers`];function zp(e,t,...n){if(e.isUnmounted)return;const r=e.vnode.props||Be;let s=n;const o=t.startsWith("update:"),i=o&&Wp(r,t.slice(7));i&&(i.trim&&(s=n.map(c=>Xe(c)?c.trim():c)),i.number&&(s=n.map(pa)));let a,l=r[a=Mi(t)]||r[a=Mi(It(t))];!l&&o&&(l=r[a=Mi(Yn(t))]),l&&Jt(l,e,6,s);const u=r[a+"Once"];if(u){if(!e.emitted)e.emitted={};else if(e.emitted[a])return;e.emitted[a]=!0,Jt(u,e,6,s)}}function gm(e,t,n=!1){const r=t.emitsCache,s=r.get(e);if(s!==void 0)return s;const o=e.emits;let i={},a=!1;if(!he(e)){const l=u=>{const c=gm(u,t,!0);c&&(a=!0,lt(i,c))};!n&&t.mixins.length&&t.mixins.forEach(l),e.extends&&l(e.extends),e.mixins&&e.mixins.forEach(l)}return!o&&!a?(We(e)&&r.set(e,null),null):(ce(o)?o.forEach(l=>i[l]=null):lt(i,o),We(e)&&r.set(e,i),i)}function ui(e,t){return!e||!ei(t)?!1:(t=t.slice(2).replace(/Once$/,""),Fe(e,t[0].toLowerCase()+t.slice(1))||Fe(e,Yn(t))||Fe(e,t))}function Ic(e){const{type:t,vnode:n,proxy:r,withProxy:s,propsOptions:[o],slots:i,attrs:a,emit:l,render:u,renderCache:c,props:f,data:d,setupState:m,ctx:g,inheritAttrs:p}=e,w=Ro(e);let h,v;try{if(n.shapeFlag&4){const y=s||r,E=y;h=cn(u.call(E,y,c,f,m,d,g)),v=a}else{const y=t;h=cn(y.length>1?y(f,{attrs:a,slots:i,emit:l}):y(f,null)),v=t.props?a:Kp(a)}}catch(y){ws.length=0,ai(y,e,1),h=R(Tt)}let b=h;if(v&&p!==!1){const y=Object.keys(v),{shapeFlag:E}=b;y.length&&E&7&&(o&&y.some(ml)&&(v=Gp(v,o)),b=Kn(b,v,!1,!0))}return n.dirs&&(b=Kn(b,null,!1,!0),b.dirs=b.dirs?b.dirs.concat(n.dirs):n.dirs),n.transition&&Sr(b,n.transition),h=b,Ro(w),h}const Kp=e=>{let t;for(const n in e)(n==="class"||n==="style"||ei(n))&&((t||(t={}))[n]=e[n]);return t},Gp=(e,t)=>{const n={};for(const r in e)(!ml(r)||!(r.slice(9)in t))&&(n[r]=e[r]);return n};function qp(e,t,n){const{props:r,children:s,component:o}=e,{props:i,children:a,patchFlag:l}=t,u=o.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&l>=0){if(l&1024)return!0;if(l&16)return r?Oc(r,i,u):!!i;if(l&8){const c=t.dynamicProps;for(let f=0;f<c.length;f++){const d=c[f];if(i[d]!==r[d]&&!ui(u,d))return!0}}}else return(s||a)&&(!a||!a.$stable)?!0:r===i?!1:r?i?Oc(r,i,u):!0:!!i;return!1}function Oc(e,t,n){const r=Object.keys(t);if(r.length!==Object.keys(e).length)return!0;for(let s=0;s<r.length;s++){const o=r[s];if(t[o]!==e[o]&&!ui(n,o))return!0}return!1}function Yp({vnode:e,parent:t},n){for(;t;){const r=t.subTree;if(r.suspense&&r.suspense.activeBranch===e&&(r.el=e.el),r===e)(e=t.vnode).el=n,t=t.parent;else break}}const vm=e=>e.__isSuspense;function Xp(e,t){t&&t.pendingBranch?ce(e)?t.effects.push(...e):t.effects.push(e):ap(e)}const ke=Symbol.for("v-fgt"),ts=Symbol.for("v-txt"),Tt=Symbol.for("v-cmt"),zi=Symbol.for("v-stc"),ws=[];let Nt=null;function qe(e=!1){ws.push(Nt=e?null:[])}function Jp(){ws.pop(),Nt=ws[ws.length-1]||null}let Is=1;function Rc(e,t=!1){Is+=e,e<0&&Nt&&t&&(Nt.hasOnce=!0)}function pm(e){return e.dynamicChildren=Is>0?Nt||jr:null,Jp(),Is>0&&Nt&&Nt.push(e),e}function rt(e,t,n,r,s,o){return pm(N(e,t,n,r,s,o,!0))}function fi(e,t,n,r,s){return pm(R(e,t,n,r,s,!0))}function Do(e){return e?e.__v_isVNode===!0:!1}function dr(e,t){return e.type===t.type&&e.key===t.key}const ym=({key:e})=>e??null,bo=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?Xe(e)||je(e)||he(e)?{i:kt,r:e,k:t,f:!!n}:e:null);function N(e,t=null,n=null,r=0,s=null,o=e===ke?0:1,i=!1,a=!1){const l={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&ym(t),ref:t&&bo(t),scopeId:Ud,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:o,patchFlag:r,dynamicProps:s,dynamicChildren:null,appContext:null,ctx:kt};return a?(Ll(l,n),o&128&&e.normalize(l)):n&&(l.shapeFlag|=Xe(n)?8:16),Is>0&&!i&&Nt&&(l.patchFlag>0||o&6)&&l.patchFlag!==32&&Nt.push(l),l}const R=Zp;function Zp(e,t=null,n=null,r=0,s=null,o=!1){if((!e||e===_p)&&(e=Tt),Do(e)){const a=Kn(e,t,!0);return n&&Ll(a,n),Is>0&&!o&&Nt&&(a.shapeFlag&6?Nt[Nt.indexOf(e)]=a:Nt.push(a)),a.patchFlag=-2,a}if(ly(e)&&(e=e.__vccOpts),t){t=Qp(t);let{class:a,style:l}=t;a&&!Xe(a)&&(t.class=de(a)),We(l)&&(El(l)&&!ce(l)&&(l=lt({},l)),t.style=be(l))}const i=Xe(e)?1:vm(e)?128:jd(e)?64:We(e)?4:he(e)?2:0;return N(e,t,n,r,s,i,o,!0)}function Qp(e){return e?El(e)||am(e)?lt({},e):e:null}function Kn(e,t,n=!1,r=!1){const{props:s,ref:o,patchFlag:i,children:a,transition:l}=e,u=t?Pe(s||{},t):s,c={__v_isVNode:!0,__v_skip:!0,type:e.type,props:u,key:u&&ym(u),ref:t&&t.ref?n&&o?ce(o)?o.concat(bo(t)):[o,bo(t)]:bo(t):o,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:a,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==ke?i===-1?16:i|16:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:l,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Kn(e.ssContent),ssFallback:e.ssFallback&&Kn(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return l&&r&&Sr(c,l.clone(c)),c}function ut(e=" ",t=0){return R(ts,null,e,t)}function xn(e="",t=!1){return t?(qe(),fi(Tt,null,e)):R(Tt,null,e)}function cn(e){return e==null||typeof e=="boolean"?R(Tt):ce(e)?R(ke,null,e.slice()):Do(e)?Bn(e):R(ts,null,String(e))}function Bn(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Kn(e)}function Ll(e,t){let n=0;const{shapeFlag:r}=e;if(t==null)t=null;else if(ce(t))n=16;else if(typeof t=="object")if(r&65){const s=t.default;s&&(s._c&&(s._d=!1),Ll(e,s()),s._c&&(s._d=!0));return}else{n=32;const s=t._;!s&&!am(t)?t._ctx=kt:s===3&&kt&&(kt.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else he(t)?(t={default:t,_ctx:kt},n=32):(t=String(t),r&64?(n=16,t=[ut(t)]):n=8);e.children=t,e.shapeFlag|=n}function Pe(...e){const t={};for(let n=0;n<e.length;n++){const r=e[n];for(const s in r)if(s==="class")t.class!==r.class&&(t.class=de([t.class,r.class]));else if(s==="style")t.style=be([t.style,r.style]);else if(ei(s)){const o=t[s],i=r[s];i&&o!==i&&!(ce(o)&&o.includes(i))&&(t[s]=o?[].concat(o,i):i)}else s!==""&&(t[s]=r[s])}return t}function rn(e,t,n,r=null){Jt(e,t,7,[n,r])}const ey=sm();let ty=0;function ny(e,t,n){const r=e.type,s=(t?t.appContext:e.appContext)||ey,o={uid:ty++,vnode:e,type:r,parent:t,appContext:s,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new yd(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(s.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:cm(r,s),emitsOptions:gm(r,s),emit:null,emitted:null,propsDefaults:Be,inheritAttrs:r.inheritAttrs,ctx:Be,data:Be,props:Be,attrs:Be,slots:Be,refs:Be,setupState:Be,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return o.ctx={_:o},o.root=t?t.root:o,o.emit=zp.bind(null,o),e.ce&&e.ce(o),o}let ft=null;const On=()=>ft||kt;let Fo,Ta;{const e=ri(),t=(n,r)=>{let s;return(s=e[n])||(s=e[n]=[]),s.push(r),o=>{s.length>1?s.forEach(i=>i(o)):s[0](o)}};Fo=t("__VUE_INSTANCE_SETTERS__",n=>ft=n),Ta=t("__VUE_SSR_SETTERS__",n=>Os=n)}const qs=e=>{const t=ft;return Fo(e),e.scope.on(),()=>{e.scope.off(),Fo(t)}},Lc=()=>{ft&&ft.scope.off(),Fo(null)};function bm(e){return e.vnode.shapeFlag&4}let Os=!1;function ry(e,t=!1,n=!1){t&&Ta(t);const{props:r,children:s}=e.vnode,o=bm(e);Op(e,r,o,t),Dp(e,s,n||t);const i=o?sy(e,t):void 0;return t&&Ta(!1),i}function sy(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,wp);const{setup:r}=n;if(r){Pn();const s=e.setupContext=r.length>1?iy(e):null,o=qs(e),i=zs(r,e,0,[e.props,s]),a=dd(i);if(In(),o(),(a||e.sp)&&!_s(e)&&Jd(e),a){if(i.then(Lc,Lc),t)return i.then(l=>{Nc(e,l)}).catch(l=>{ai(l,e,0)});e.asyncDep=i}else Nc(e,i)}else _m(e)}function Nc(e,t,n){he(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:We(t)&&(e.setupState=Dd(t)),_m(e)}function _m(e,t,n){const r=e.type;e.render||(e.render=r.render||fn);{const s=qs(e);Pn();try{Ep(e)}finally{In(),s()}}}const oy={get(e,t){return pt(e,"get",""),e[t]}};function iy(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,oy),slots:e.slots,emit:e.emit,expose:t}}function di(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Dd(ii(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in Ss)return Ss[n](e)},has(t,n){return n in t||n in Ss}})):e.proxy}function ay(e,t=!0){return he(e)?e.displayName||e.name:e.name||t&&e.__name}function ly(e){return he(e)&&"__vccOpts"in e}const U=(e,t)=>np(e,t,Os);function vn(e,t,n){const r=arguments.length;return r===2?We(t)&&!ce(t)?Do(t)?R(e,null,[t]):R(e,t):R(e,null,t):(r>3?n=Array.prototype.slice.call(arguments,2):r===3&&Do(n)&&(n=[n]),R(e,t,n))}const cy="3.5.17";/**
* @vue/runtime-dom v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let ka;const Dc=typeof window<"u"&&window.trustedTypes;if(Dc)try{ka=Dc.createPolicy("vue",{createHTML:e=>e})}catch{}const Sm=ka?e=>ka.createHTML(e):e=>e,uy="http://www.w3.org/2000/svg",fy="http://www.w3.org/1998/Math/MathML",wn=typeof document<"u"?document:null,Fc=wn&&wn.createElement("template"),dy={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,r)=>{const s=t==="svg"?wn.createElementNS(uy,e):t==="mathml"?wn.createElementNS(fy,e):n?wn.createElement(e,{is:n}):wn.createElement(e);return e==="select"&&r&&r.multiple!=null&&s.setAttribute("multiple",r.multiple),s},createText:e=>wn.createTextNode(e),createComment:e=>wn.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>wn.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,r,s,o){const i=n?n.previousSibling:t.lastChild;if(s&&(s===o||s.nextSibling))for(;t.insertBefore(s.cloneNode(!0),n),!(s===o||!(s=s.nextSibling)););else{Fc.innerHTML=Sm(r==="svg"?`<svg>${e}</svg>`:r==="mathml"?`<math>${e}</math>`:e);const a=Fc.content;if(r==="svg"||r==="mathml"){const l=a.firstChild;for(;l.firstChild;)a.appendChild(l.firstChild);a.removeChild(l)}t.insertBefore(a,n)}return[i?i.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},Ln="transition",ls="animation",Kr=Symbol("_vtc"),wm={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},Em=lt({},Gd,wm),my=e=>(e.displayName="Transition",e.props=Em,e),wr=my((e,{slots:t})=>vn(fp,Cm(e),t)),lr=(e,t=[])=>{ce(e)?e.forEach(n=>n(...t)):e&&e(...t)},Mc=e=>e?ce(e)?e.some(t=>t.length>1):e.length>1:!1;function Cm(e){const t={};for(const D in e)D in wm||(t[D]=e[D]);if(e.css===!1)return t;const{name:n="v",type:r,duration:s,enterFromClass:o=`${n}-enter-from`,enterActiveClass:i=`${n}-enter-active`,enterToClass:a=`${n}-enter-to`,appearFromClass:l=o,appearActiveClass:u=i,appearToClass:c=a,leaveFromClass:f=`${n}-leave-from`,leaveActiveClass:d=`${n}-leave-active`,leaveToClass:m=`${n}-leave-to`}=e,g=hy(s),p=g&&g[0],w=g&&g[1],{onBeforeEnter:h,onEnter:v,onEnterCancelled:b,onLeave:y,onLeaveCancelled:E,onBeforeAppear:P=h,onAppear:k=v,onAppearCancelled:T=b}=t,A=(D,G,ee,ne)=>{D._enterCancelled=ne,Fn(D,G?c:a),Fn(D,G?u:i),ee&&ee()},H=(D,G)=>{D._isLeaving=!1,Fn(D,f),Fn(D,m),Fn(D,d),G&&G()},V=D=>(G,ee)=>{const ne=D?k:v,oe=()=>A(G,D,ee);lr(ne,[G,oe]),Vc(()=>{Fn(G,D?l:o),an(G,D?c:a),Mc(ne)||$c(G,r,p,oe)})};return lt(t,{onBeforeEnter(D){lr(h,[D]),an(D,o),an(D,i)},onBeforeAppear(D){lr(P,[D]),an(D,l),an(D,u)},onEnter:V(!1),onAppear:V(!0),onLeave(D,G){D._isLeaving=!0;const ee=()=>H(D,G);an(D,f),D._enterCancelled?(an(D,d),Aa()):(Aa(),an(D,d)),Vc(()=>{D._isLeaving&&(Fn(D,f),an(D,m),Mc(y)||$c(D,r,w,ee))}),lr(y,[D,ee])},onEnterCancelled(D){A(D,!1,void 0,!0),lr(b,[D])},onAppearCancelled(D){A(D,!0,void 0,!0),lr(T,[D])},onLeaveCancelled(D){H(D),lr(E,[D])}})}function hy(e){if(e==null)return null;if(We(e))return[Ki(e.enter),Ki(e.leave)];{const t=Ki(e);return[t,t]}}function Ki(e){return xv(e)}function an(e,t){t.split(/\s+/).forEach(n=>n&&e.classList.add(n)),(e[Kr]||(e[Kr]=new Set)).add(t)}function Fn(e,t){t.split(/\s+/).forEach(r=>r&&e.classList.remove(r));const n=e[Kr];n&&(n.delete(t),n.size||(e[Kr]=void 0))}function Vc(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let gy=0;function $c(e,t,n,r){const s=e._endId=++gy,o=()=>{s===e._endId&&r()};if(n!=null)return setTimeout(o,n);const{type:i,timeout:a,propCount:l}=xm(e,t);if(!i)return r();const u=i+"end";let c=0;const f=()=>{e.removeEventListener(u,d),o()},d=m=>{m.target===e&&++c>=l&&f()};setTimeout(()=>{c<l&&f()},a+1),e.addEventListener(u,d)}function xm(e,t){const n=window.getComputedStyle(e),r=g=>(n[g]||"").split(", "),s=r(`${Ln}Delay`),o=r(`${Ln}Duration`),i=Bc(s,o),a=r(`${ls}Delay`),l=r(`${ls}Duration`),u=Bc(a,l);let c=null,f=0,d=0;t===Ln?i>0&&(c=Ln,f=i,d=o.length):t===ls?u>0&&(c=ls,f=u,d=l.length):(f=Math.max(i,u),c=f>0?i>u?Ln:ls:null,d=c?c===Ln?o.length:l.length:0);const m=c===Ln&&/\b(transform|all)(,|$)/.test(r(`${Ln}Property`).toString());return{type:c,timeout:f,propCount:d,hasTransform:m}}function Bc(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((n,r)=>Uc(n)+Uc(e[r])))}function Uc(e){return e==="auto"?0:Number(e.slice(0,-1).replace(",","."))*1e3}function Aa(){return document.body.offsetHeight}function vy(e,t,n){const r=e[Kr];r&&(t=(t?[t,...r]:[...r]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const Mo=Symbol("_vod"),Tm=Symbol("_vsh"),Ys={beforeMount(e,{value:t},{transition:n}){e[Mo]=e.style.display==="none"?"":e.style.display,n&&t?n.beforeEnter(e):cs(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:r}){!t!=!n&&(r?t?(r.beforeEnter(e),cs(e,!0),r.enter(e)):r.leave(e,()=>{cs(e,!1)}):cs(e,t))},beforeUnmount(e,{value:t}){cs(e,t)}};function cs(e,t){e.style.display=t?e[Mo]:"none",e[Tm]=!t}const py=Symbol(""),yy=/(^|;)\s*display\s*:/;function by(e,t,n){const r=e.style,s=Xe(n);let o=!1;if(n&&!s){if(t)if(Xe(t))for(const i of t.split(";")){const a=i.slice(0,i.indexOf(":")).trim();n[a]==null&&_o(r,a,"")}else for(const i in t)n[i]==null&&_o(r,i,"");for(const i in n)i==="display"&&(o=!0),_o(r,i,n[i])}else if(s){if(t!==n){const i=r[py];i&&(n+=";"+i),r.cssText=n,o=yy.test(n)}}else t&&e.removeAttribute("style");Mo in e&&(e[Mo]=o?r.display:"",e[Tm]&&(r.display="none"))}const Hc=/\s*!important$/;function _o(e,t,n){if(ce(n))n.forEach(r=>_o(e,t,r));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const r=_y(e,t);Hc.test(n)?e.setProperty(Yn(r),n.replace(Hc,""),"important"):e[r]=n}}const jc=["Webkit","Moz","ms"],Gi={};function _y(e,t){const n=Gi[t];if(n)return n;let r=It(t);if(r!=="filter"&&r in e)return Gi[t]=r;r=Qr(r);for(let s=0;s<jc.length;s++){const o=jc[s]+r;if(o in e)return Gi[t]=o}return t}const Wc="http://www.w3.org/1999/xlink";function zc(e,t,n,r,s,o=Ov(t)){r&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(Wc,t.slice(6,t.length)):e.setAttributeNS(Wc,t,n):n==null||o&&!gd(n)?e.removeAttribute(t):e.setAttribute(t,o?"":qn(n)?String(n):n)}function Kc(e,t,n,r,s){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?Sm(n):n);return}const o=e.tagName;if(t==="value"&&o!=="PROGRESS"&&!o.includes("-")){const a=o==="OPTION"?e.getAttribute("value")||"":e.value,l=n==null?e.type==="checkbox"?"on":"":String(n);(a!==l||!("_value"in e))&&(e.value=l),n==null&&e.removeAttribute(t),e._value=n;return}let i=!1;if(n===""||n==null){const a=typeof e[t];a==="boolean"?n=gd(n):n==null&&a==="string"?(n="",i=!0):a==="number"&&(n=0,i=!0)}try{e[t]=n}catch{}i&&e.removeAttribute(s||t)}function Vr(e,t,n,r){e.addEventListener(t,n,r)}function Sy(e,t,n,r){e.removeEventListener(t,n,r)}const Gc=Symbol("_vei");function wy(e,t,n,r,s=null){const o=e[Gc]||(e[Gc]={}),i=o[t];if(r&&i)i.value=r;else{const[a,l]=Ey(t);if(r){const u=o[t]=Ty(r,s);Vr(e,a,u,l)}else i&&(Sy(e,a,i,l),o[t]=void 0)}}const qc=/(?:Once|Passive|Capture)$/;function Ey(e){let t;if(qc.test(e)){t={};let r;for(;r=e.match(qc);)e=e.slice(0,e.length-r[0].length),t[r[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Yn(e.slice(2)),t]}let qi=0;const Cy=Promise.resolve(),xy=()=>qi||(Cy.then(()=>qi=0),qi=Date.now());function Ty(e,t){const n=r=>{if(!r._vts)r._vts=Date.now();else if(r._vts<=n.attached)return;Jt(ky(r,n.value),t,5,[r])};return n.value=e,n.attached=xy(),n}function ky(e,t){if(ce(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(r=>s=>!s._stopped&&r&&r(s))}else return t}const Yc=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,Ay=(e,t,n,r,s,o)=>{const i=s==="svg";t==="class"?vy(e,r,i):t==="style"?by(e,n,r):ei(t)?ml(t)||wy(e,t,n,r,o):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):Py(e,t,r,i))?(Kc(e,t,r),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&zc(e,t,r,i,o,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!Xe(r))?Kc(e,It(t),r,o,t):(t==="true-value"?e._trueValue=r:t==="false-value"&&(e._falseValue=r),zc(e,t,r,i))};function Py(e,t,n,r){if(r)return!!(t==="innerHTML"||t==="textContent"||t in e&&Yc(t)&&he(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const s=e.tagName;if(s==="IMG"||s==="VIDEO"||s==="CANVAS"||s==="SOURCE")return!1}return Yc(t)&&Xe(n)?!1:t in e}const km=new WeakMap,Am=new WeakMap,Vo=Symbol("_moveCb"),Xc=Symbol("_enterCb"),Iy=e=>(delete e.props.mode,e),Oy=Iy({name:"TransitionGroup",props:lt({},Em,{tag:String,moveClass:String}),setup(e,{slots:t}){const n=On(),r=Kd();let s,o;return kl(()=>{if(!s.length)return;const i=e.moveClass||`${e.name||"v"}-move`;if(!Dy(s[0].el,n.vnode.el,i)){s=[];return}s.forEach(Ry),s.forEach(Ly);const a=s.filter(Ny);Aa(),a.forEach(l=>{const u=l.el,c=u.style;an(u,i),c.transform=c.webkitTransform=c.transitionDuration="";const f=u[Vo]=d=>{d&&d.target!==u||(!d||/transform$/.test(d.propertyName))&&(u.removeEventListener("transitionend",f),u[Vo]=null,Fn(u,i))};u.addEventListener("transitionend",f)}),s=[]}),()=>{const i=Ee(e),a=Cm(i);let l=i.tag||ke;if(s=[],o)for(let u=0;u<o.length;u++){const c=o[u];c.el&&c.el instanceof Element&&(s.push(c),Sr(c,Ps(c,a,r,n)),km.set(c,c.el.getBoundingClientRect()))}o=t.default?Tl(t.default()):[];for(let u=0;u<o.length;u++){const c=o[u];c.key!=null&&Sr(c,Ps(c,a,r,n))}return R(l,null,o)}}}),Nl=Oy;function Ry(e){const t=e.el;t[Vo]&&t[Vo](),t[Xc]&&t[Xc]()}function Ly(e){Am.set(e,e.el.getBoundingClientRect())}function Ny(e){const t=km.get(e),n=Am.get(e),r=t.left-n.left,s=t.top-n.top;if(r||s){const o=e.el.style;return o.transform=o.webkitTransform=`translate(${r}px,${s}px)`,o.transitionDuration="0s",e}}function Dy(e,t,n){const r=e.cloneNode(),s=e[Kr];s&&s.forEach(a=>{a.split(/\s+/).forEach(l=>l&&r.classList.remove(l))}),n.split(/\s+/).forEach(a=>a&&r.classList.add(a)),r.style.display="none";const o=t.nodeType===1?t:t.parentNode;o.appendChild(r);const{hasTransform:i}=xm(r);return o.removeChild(r),i}const Jc=e=>{const t=e.props["onUpdate:modelValue"]||!1;return ce(t)?n=>po(t,n):t};function Fy(e){e.target.composing=!0}function Zc(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const Yi=Symbol("_assign"),My={created(e,{modifiers:{lazy:t,trim:n,number:r}},s){e[Yi]=Jc(s);const o=r||s.props&&s.props.type==="number";Vr(e,t?"change":"input",i=>{if(i.target.composing)return;let a=e.value;n&&(a=a.trim()),o&&(a=pa(a)),e[Yi](a)}),n&&Vr(e,"change",()=>{e.value=e.value.trim()}),t||(Vr(e,"compositionstart",Fy),Vr(e,"compositionend",Zc),Vr(e,"change",Zc))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:n,modifiers:{lazy:r,trim:s,number:o}},i){if(e[Yi]=Jc(i),e.composing)return;const a=(o||e.type==="number")&&!/^0\d/.test(e.value)?pa(e.value):e.value,l=t??"";a!==l&&(document.activeElement===e&&e.type!=="range"&&(r&&t===n||s&&e.value.trim()===l)||(e.value=l))}},Vy=["ctrl","shift","alt","meta"],$y={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>Vy.some(n=>e[`${n}Key`]&&!t.includes(n))},So=(e,t)=>{const n=e._withMods||(e._withMods={}),r=t.join(".");return n[r]||(n[r]=(s,...o)=>{for(let i=0;i<t.length;i++){const a=$y[t[i]];if(a&&a(s,t))return}return e(s,...o)})},By={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},Pm=(e,t)=>{const n=e._withKeys||(e._withKeys={}),r=t.join(".");return n[r]||(n[r]=s=>{if(!("key"in s))return;const o=Yn(s.key);if(t.some(i=>i===o||By[i]===o))return e(s)})},Uy=lt({patchProp:Ay},dy);let Qc;function Hy(){return Qc||(Qc=Mp(Uy))}const jy=(...e)=>{const t=Hy().createApp(...e),{mount:n}=t;return t.mount=r=>{const s=zy(r);if(!s)return;const o=t._component;!he(o)&&!o.render&&!o.template&&(o.template=s.innerHTML),s.nodeType===1&&(s.textContent="");const i=n(s,!1,Wy(s));return s instanceof Element&&(s.removeAttribute("v-cloak"),s.setAttribute("data-v-app","")),i},t};function Wy(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function zy(e){return Xe(e)?document.querySelector(e):e}/*!
 * pinia v3.0.3
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */let Im;const mi=e=>Im=e,Om=Symbol();function Pa(e){return e&&typeof e=="object"&&Object.prototype.toString.call(e)==="[object Object]"&&typeof e.toJSON!="function"}var Es;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(Es||(Es={}));function Ky(){const e=Wn(!0),t=e.run(()=>J({}));let n=[],r=[];const s=ii({install(o){mi(s),s._a=o,o.provide(Om,s),o.config.globalProperties.$pinia=s,r.forEach(i=>n.push(i)),r=[]},use(o){return this._a?n.push(o):r.push(o),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return s}const Rm=()=>{};function eu(e,t,n,r=Rm){e.push(t);const s=()=>{const o=e.indexOf(t);o>-1&&(e.splice(o,1),r())};return!n&&vl()&&St(s),s}function Lr(e,...t){e.slice().forEach(n=>{n(...t)})}const Gy=e=>e(),tu=Symbol(),Xi=Symbol();function Ia(e,t){e instanceof Map&&t instanceof Map?t.forEach((n,r)=>e.set(r,n)):e instanceof Set&&t instanceof Set&&t.forEach(e.add,e);for(const n in t){if(!t.hasOwnProperty(n))continue;const r=t[n],s=e[n];Pa(s)&&Pa(r)&&e.hasOwnProperty(n)&&!je(r)&&!jn(r)?e[n]=Ia(s,r):e[n]=r}return e}const qy=Symbol();function Yy(e){return!Pa(e)||!Object.prototype.hasOwnProperty.call(e,qy)}const{assign:Mn}=Object;function Xy(e){return!!(je(e)&&e.effect)}function Jy(e,t,n,r){const{state:s,actions:o,getters:i}=t,a=n.state.value[e];let l;function u(){a||(n.state.value[e]=s?s():{});const c=Cl(n.state.value[e]);return Mn(c,o,Object.keys(i||{}).reduce((f,d)=>(f[d]=ii(U(()=>{mi(n);const m=n._s.get(e);return i[d].call(m,m)})),f),{}))}return l=Lm(e,u,t,n,r,!0),l}function Lm(e,t,n={},r,s,o){let i;const a=Mn({actions:{}},n),l={deep:!0};let u,c,f=[],d=[],m;const g=r.state.value[e];!o&&!g&&(r.state.value[e]={}),J({});let p;function w(T){let A;u=c=!1,typeof T=="function"?(T(r.state.value[e]),A={type:Es.patchFunction,storeId:e,events:m}):(Ia(r.state.value[e],T),A={type:Es.patchObject,payload:T,storeId:e,events:m});const H=p=Symbol();bt().then(()=>{p===H&&(u=!0)}),c=!0,Lr(f,A,r.state.value[e])}const h=o?function(){const{state:A}=n,H=A?A():{};this.$patch(V=>{Mn(V,H)})}:Rm;function v(){i.stop(),f=[],d=[],r._s.delete(e)}const b=(T,A="")=>{if(tu in T)return T[Xi]=A,T;const H=function(){mi(r);const V=Array.from(arguments),D=[],G=[];function ee(Z){D.push(Z)}function ne(Z){G.push(Z)}Lr(d,{args:V,name:H[Xi],store:E,after:ee,onError:ne});let oe;try{oe=T.apply(this&&this.$id===e?this:E,V)}catch(Z){throw Lr(G,Z),Z}return oe instanceof Promise?oe.then(Z=>(Lr(D,Z),Z)).catch(Z=>(Lr(G,Z),Promise.reject(Z))):(Lr(D,oe),oe)};return H[tu]=!0,H[Xi]=A,H},y={_p:r,$id:e,$onAction:eu.bind(null,d),$patch:w,$reset:h,$subscribe(T,A={}){const H=eu(f,T,A.detached,()=>V()),V=i.run(()=>le(()=>r.state.value[e],D=>{(A.flush==="sync"?c:u)&&T({storeId:e,type:Es.direct,events:m},D)},Mn({},l,A)));return H},$dispose:v},E=st(y);r._s.set(e,E);const k=(r._a&&r._a.runWithContext||Gy)(()=>r._e.run(()=>(i=Wn()).run(()=>t({action:b}))));for(const T in k){const A=k[T];if(je(A)&&!Xy(A)||jn(A))o||(g&&Yy(A)&&(je(A)?A.value=g[T]:Ia(A,g[T])),r.state.value[e][T]=A);else if(typeof A=="function"){const H=b(A,T);k[T]=H,a.actions[T]=A}}return Mn(E,k),Mn(Ee(E),k),Object.defineProperty(E,"$state",{get:()=>r.state.value[e],set:T=>{w(A=>{Mn(A,T)})}}),r._p.forEach(T=>{Mn(E,i.run(()=>T({store:E,app:r._a,pinia:r,options:a})))}),g&&o&&n.hydrate&&n.hydrate(E.$state,g),u=!0,c=!0,E}/*! #__NO_SIDE_EFFECTS__ */function Nm(e,t,n){let r;const s=typeof t=="function";r=s?n:t;function o(i,a){const l=Ip();return i=i||(l?Le(Om,null):null),i&&mi(i),i=Im,i._s.has(e)||(s?Lm(e,t,r,i):Jy(e,r,i)),i._s.get(e)}return o.$id=e,o}const hi=Nm("snackbar",{state:()=>({message:"",type:"",visible:!1,timeout:null}),actions:{showMessage(e,t="info"){this.message=e,this.type=t,this.visible=!0,clearTimeout(this.timeout),this.timeout=setTimeout(()=>{this.visible=!1},5e3)},hideMessage(){this.visible=!1,clearTimeout(this.timeout)}}});function te(e,t){return n=>Object.keys(e).reduce((r,s)=>{const i=typeof e[s]=="object"&&e[s]!=null&&!Array.isArray(e[s])?e[s]:{type:e[s]};return n&&s in n?r[s]={...i,default:n[s]}:r[s]=i,t&&!r[s].source&&(r[s].source=t),r},{})}const Ie=te({class:[String,Array,Object],style:{type:[String,Array,Object],default:null}},"component"),Ke=typeof window<"u",Dl=Ke&&"IntersectionObserver"in window,Zy=Ke&&("ontouchstart"in window||window.navigator.maxTouchPoints>0),Qy=Ke&&"matchMedia"in window&&typeof window.matchMedia=="function";function nu(e,t,n){eb(e,t),t.set(e,n)}function eb(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")}function ru(e,t,n){return e.set(Dm(e,t),n),n}function bn(e,t){return e.get(Dm(e,t))}function Dm(e,t,n){if(typeof e=="function"?e===t:e.has(t))return arguments.length<3?t:n;throw new TypeError("Private element is not present on this object")}function tb(e,t,n){const r=t.length-1;if(r<0)return e===void 0?n:e;for(let s=0;s<r;s++){if(e==null)return n;e=e[t[s]]}return e==null||e[t[r]]===void 0?n:e[t[r]]}function Rs(e,t){if(e===t)return!0;if(e instanceof Date&&t instanceof Date&&e.getTime()!==t.getTime()||e!==Object(e)||t!==Object(t))return!1;const n=Object.keys(e);return n.length!==Object.keys(t).length?!1:n.every(r=>Rs(e[r],t[r]))}function su(e,t,n){return e==null||!t||typeof t!="string"?n:e[t]!==void 0?e[t]:(t=t.replace(/\[(\w+)\]/g,".$1"),t=t.replace(/^\./,""),tb(e,t.split("."),n))}function Fm(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return Array.from({length:e},(n,r)=>t+r)}function me(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"px";if(e==null||e==="")return;const n=Number(e);return isNaN(n)?String(e):isFinite(n)?`${n}${t}`:void 0}function Oa(e){return e!==null&&typeof e=="object"&&!Array.isArray(e)}function ou(e){let t;return e!==null&&typeof e=="object"&&((t=Object.getPrototypeOf(e))===Object.prototype||t===null)}function Mm(e){if(e&&"$el"in e){const t=e.$el;return t?.nodeType===Node.TEXT_NODE?t.nextElementSibling:t}return e}const Ra=Object.freeze({enter:13,tab:9,delete:46,esc:27,space:32,up:38,down:40,left:37,right:39,end:35,home:36,del:46,backspace:8,insert:45,pageup:33,pagedown:34,shift:16});function Ji(e,t){return t.every(n=>e.hasOwnProperty(n))}function gi(e,t){const n={};for(const r of t)Object.prototype.hasOwnProperty.call(e,r)&&(n[r]=e[r]);return n}function iu(e,t,n){const r=Object.create(null),s=Object.create(null);for(const o in e)t.some(i=>i instanceof RegExp?i.test(o):i===o)?r[o]=e[o]:s[o]=e[o];return[r,s]}function Fl(e,t){const n={...e};return t.forEach(r=>delete n[r]),n}const Vm=/^on[^a-z]/,$m=e=>Vm.test(e),nb=["onAfterscriptexecute","onAnimationcancel","onAnimationend","onAnimationiteration","onAnimationstart","onAuxclick","onBeforeinput","onBeforescriptexecute","onChange","onClick","onCompositionend","onCompositionstart","onCompositionupdate","onContextmenu","onCopy","onCut","onDblclick","onFocusin","onFocusout","onFullscreenchange","onFullscreenerror","onGesturechange","onGestureend","onGesturestart","onGotpointercapture","onInput","onKeydown","onKeypress","onKeyup","onLostpointercapture","onMousedown","onMousemove","onMouseout","onMouseover","onMouseup","onMousewheel","onPaste","onPointercancel","onPointerdown","onPointerenter","onPointerleave","onPointermove","onPointerout","onPointerover","onPointerup","onReset","onSelect","onSubmit","onTouchcancel","onTouchend","onTouchmove","onTouchstart","onTransitioncancel","onTransitionend","onTransitionrun","onTransitionstart","onWheel"];function Bm(e){const[t,n]=iu(e,[Vm]),r=Fl(t,nb),[s,o]=iu(n,["class","style","id",/^data-/]);return Object.assign(s,t),Object.assign(o,r),[s,o]}function yr(e){return e==null?[]:Array.isArray(e)?e:[e]}function Er(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:1;return Math.max(t,Math.min(n,e))}function au(e,t){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:"0";return e+n.repeat(Math.max(0,t-e.length))}function lu(e,t){return(arguments.length>2&&arguments[2]!==void 0?arguments[2]:"0").repeat(Math.max(0,t-e.length))+e}function rb(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1;const n=[];let r=0;for(;r<e.length;)n.push(e.substr(r,t)),r+=t;return n}function sb(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1e3;if(e<t)return`${e} B`;const n=t===1024?["Ki","Mi","Gi"]:["k","M","G"];let r=-1;for(;Math.abs(e)>=t&&r<n.length-1;)e/=t,++r;return`${e.toFixed(1)} ${n[r]}B`}function Dt(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0;const r={};for(const s in e)r[s]=e[s];for(const s in t){const o=e[s],i=t[s];if(ou(o)&&ou(i)){r[s]=Dt(o,i,n);continue}if(n&&Array.isArray(o)&&Array.isArray(i)){r[s]=n(o,i);continue}r[s]=i}return r}function Um(e){return e.map(t=>t.type===ke?Um(t.children):t).flat()}function br(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"";if(br.cache.has(e))return br.cache.get(e);const t=e.replace(/[^a-z]/gi,"-").replace(/\B([A-Z])/g,"-$1").toLowerCase();return br.cache.set(e,t),t}br.cache=new Map;function Ur(e,t){if(!t||typeof t!="object")return[];if(Array.isArray(t))return t.map(n=>Ur(e,n)).flat(1);if(t.suspense)return Ur(e,t.ssContent);if(Array.isArray(t.children))return t.children.map(n=>Ur(e,n)).flat(1);if(t.component){if(Object.getOwnPropertySymbols(t.component.provides).includes(e))return[t.component];if(t.component.subTree)return Ur(e,t.component.subTree).flat(1)}return[]}var Nr=new WeakMap,cr=new WeakMap;class ob{constructor(t){nu(this,Nr,[]),nu(this,cr,0),this.size=t}get isFull(){return bn(Nr,this).length===this.size}push(t){bn(Nr,this)[bn(cr,this)]=t,ru(cr,this,(bn(cr,this)+1)%this.size)}values(){return bn(Nr,this).slice(bn(cr,this)).concat(bn(Nr,this).slice(0,bn(cr,this)))}clear(){bn(Nr,this).length=0,ru(cr,this,0)}}function Ml(e){const t=st({});en(()=>{const r=e();for(const s in r)t[s]=r[s]},{flush:"sync"});const n={};for(const r in t)n[r]=X(()=>t[r]);return n}function $o(e,t){return e.includes(t)}function Hm(e){return e[2].toLowerCase()+e.slice(3)}const An=()=>[Function,Array];function cu(e,t){return t="on"+Qr(t),!!(e[t]||e[`${t}Once`]||e[`${t}Capture`]||e[`${t}OnceCapture`]||e[`${t}CaptureOnce`])}function jm(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];if(Array.isArray(e))for(const s of e)s(...n);else typeof e=="function"&&e(...n)}function ib(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;const n=["button","[href]",'input:not([type="hidden"])',"select","textarea","[tabindex]"].map(r=>`${r}${t?':not([tabindex="-1"])':""}:not([disabled])`).join(", ");return[...e.querySelectorAll(n)]}function ab(e,t){if(!(Ke&&typeof CSS<"u"&&typeof CSS.supports<"u"&&CSS.supports(`selector(${t})`)))return null;try{return!!e&&e.matches(t)}catch{return null}}function lb(e,t){if(!Ke||e===0)return t(),()=>{};const n=window.setTimeout(t,e);return()=>window.clearTimeout(n)}function La(){const e=Ae(),t=n=>{e.value=n};return Object.defineProperty(t,"value",{enumerable:!0,get:()=>e.value,set:n=>e.value=n}),Object.defineProperty(t,"el",{enumerable:!0,get:()=>Mm(e.value)}),t}function cb(e){const t=["checked","disabled"];return Object.fromEntries(Object.entries(e).filter(n=>{let[r,s]=n;return t.includes(r)?!!s:s!==void 0}))}const Wm=["top","bottom"],ub=["start","end","left","right"];function Na(e,t){let[n,r]=e.split(" ");return r||(r=$o(Wm,n)?"start":$o(ub,n)?"top":"center"),{side:uu(n,t),align:uu(r,t)}}function uu(e,t){return e==="start"?t?"right":"left":e==="end"?t?"left":"right":e}function Zi(e){return{side:{center:"center",top:"bottom",bottom:"top",left:"right",right:"left"}[e.side],align:e.align}}function Qi(e){return{side:e.side,align:{center:"center",top:"bottom",bottom:"top",left:"right",right:"left"}[e.align]}}function fu(e){return{side:e.align,align:e.side}}function du(e){return $o(Wm,e.side)?"y":"x"}class dn{constructor(t){let{x:n,y:r,width:s,height:o}=t;this.x=n,this.y=r,this.width=s,this.height=o}get top(){return this.y}get bottom(){return this.y+this.height}get left(){return this.x}get right(){return this.x+this.width}}function mu(e,t){return{x:{before:Math.max(0,t.left-e.left),after:Math.max(0,e.right-t.right)},y:{before:Math.max(0,t.top-e.top),after:Math.max(0,e.bottom-t.bottom)}}}function zm(e){return Array.isArray(e)?new dn({x:e[0],y:e[1],width:0,height:0}):e.getBoundingClientRect()}function fb(e){if(e===document.documentElement)return visualViewport?new dn({x:visualViewport.scale>1?0:visualViewport.offsetLeft,y:visualViewport.scale>1?0:visualViewport.offsetTop,width:visualViewport.width*visualViewport.scale,height:visualViewport.height*visualViewport.scale}):new dn({x:0,y:0,width:document.documentElement.clientWidth,height:document.documentElement.clientHeight});{const t=e.getBoundingClientRect();return new dn({x:t.x,y:t.y,width:e.clientWidth,height:e.clientHeight})}}function Vl(e){const t=e.getBoundingClientRect(),n=getComputedStyle(e),r=n.transform;if(r){let s,o,i,a,l;if(r.startsWith("matrix3d("))s=r.slice(9,-1).split(/, /),o=Number(s[0]),i=Number(s[5]),a=Number(s[12]),l=Number(s[13]);else if(r.startsWith("matrix("))s=r.slice(7,-1).split(/, /),o=Number(s[0]),i=Number(s[3]),a=Number(s[4]),l=Number(s[5]);else return new dn(t);const u=n.transformOrigin,c=t.x-a-(1-o)*parseFloat(u),f=t.y-l-(1-i)*parseFloat(u.slice(u.indexOf(" ")+1)),d=o?t.width/o:e.offsetWidth+1,m=i?t.height/i:e.offsetHeight+1;return new dn({x:c,y:f,width:d,height:m})}else return new dn(t)}function Hr(e,t,n){if(typeof e.animate>"u")return{finished:Promise.resolve()};let r;try{r=e.animate(t,n)}catch{return{finished:Promise.resolve()}}return typeof r.finished>"u"&&(r.finished=new Promise(s=>{r.onfinish=()=>{s(r)}})),r}const wo=new WeakMap;function db(e,t){Object.keys(t).forEach(n=>{if($m(n)){const r=Hm(n),s=wo.get(e);if(t[n]==null)s?.forEach(o=>{const[i,a]=o;i===r&&(e.removeEventListener(r,a),s.delete(o))});else if(!s||![...s].some(o=>o[0]===r&&o[1]===t[n])){e.addEventListener(r,t[n]);const o=s||new Set;o.add([r,t[n]]),wo.has(e)||wo.set(e,o)}}else t[n]==null?e.removeAttribute(n):e.setAttribute(n,t[n])})}function mb(e,t){Object.keys(t).forEach(n=>{if($m(n)){const r=Hm(n),s=wo.get(e);s?.forEach(o=>{const[i,a]=o;i===r&&(e.removeEventListener(r,a),s.delete(o))})}else e.removeAttribute(n)})}const Dr=2.4,hu=.2126729,gu=.7151522,vu=.072175,hb=.55,gb=.58,vb=.57,pb=.62,co=.03,pu=1.45,yb=5e-4,bb=1.25,_b=1.25,yu=.078,bu=12.82051282051282,uo=.06,_u=.001;function Su(e,t){const n=(e.r/255)**Dr,r=(e.g/255)**Dr,s=(e.b/255)**Dr,o=(t.r/255)**Dr,i=(t.g/255)**Dr,a=(t.b/255)**Dr;let l=n*hu+r*gu+s*vu,u=o*hu+i*gu+a*vu;if(l<=co&&(l+=(co-l)**pu),u<=co&&(u+=(co-u)**pu),Math.abs(u-l)<yb)return 0;let c;if(u>l){const f=(u**hb-l**gb)*bb;c=f<_u?0:f<yu?f-f*bu*uo:f-uo}else{const f=(u**pb-l**vb)*_b;c=f>-_u?0:f>-yu?f-f*bu*uo:f+uo}return c*100}function Km(e,t){t=Array.isArray(t)?t.slice(0,-1).map(n=>`'${n}'`).join(", ")+` or '${t.at(-1)}'`:`'${t}'`}const Bo=.20689655172413793,Sb=e=>e>Bo**3?Math.cbrt(e):e/(3*Bo**2)+4/29,wb=e=>e>Bo?e**3:3*Bo**2*(e-4/29);function Gm(e){const t=Sb,n=t(e[1]);return[116*n-16,500*(t(e[0]/.95047)-n),200*(n-t(e[2]/1.08883))]}function qm(e){const t=wb,n=(e[0]+16)/116;return[t(n+e[1]/500)*.95047,t(n),t(n-e[2]/200)*1.08883]}const Eb=[[3.2406,-1.5372,-.4986],[-.9689,1.8758,.0415],[.0557,-.204,1.057]],Cb=e=>e<=.0031308?e*12.92:1.055*e**(1/2.4)-.055,xb=[[.4124,.3576,.1805],[.2126,.7152,.0722],[.0193,.1192,.9505]],Tb=e=>e<=.04045?e/12.92:((e+.055)/1.055)**2.4;function Ym(e){const t=Array(3),n=Cb,r=Eb;for(let s=0;s<3;++s)t[s]=Math.round(Er(n(r[s][0]*e[0]+r[s][1]*e[1]+r[s][2]*e[2]))*255);return{r:t[0],g:t[1],b:t[2]}}function $l(e){let{r:t,g:n,b:r}=e;const s=[0,0,0],o=Tb,i=xb;t=o(t/255),n=o(n/255),r=o(r/255);for(let a=0;a<3;++a)s[a]=i[a][0]*t+i[a][1]*n+i[a][2]*r;return s}function Da(e){return!!e&&/^(#|var\(--|(rgb|hsl)a?\()/.test(e)}function kb(e){return Da(e)&&!/^((rgb|hsl)a?\()?var\(--/.test(e)}const wu=/^(?<fn>(?:rgb|hsl)a?)\((?<values>.+)\)/,Ab={rgb:(e,t,n,r)=>({r:e,g:t,b:n,a:r}),rgba:(e,t,n,r)=>({r:e,g:t,b:n,a:r}),hsl:(e,t,n,r)=>Eu({h:e,s:t,l:n,a:r}),hsla:(e,t,n,r)=>Eu({h:e,s:t,l:n,a:r}),hsv:(e,t,n,r)=>Ls({h:e,s:t,v:n,a:r}),hsva:(e,t,n,r)=>Ls({h:e,s:t,v:n,a:r})};function un(e){if(typeof e=="number")return{r:(e&16711680)>>16,g:(e&65280)>>8,b:e&255};if(typeof e=="string"&&wu.test(e)){const{groups:t}=e.match(wu),{fn:n,values:r}=t,s=r.split(/,\s*|\s*\/\s*|\s+/).map((o,i)=>o.endsWith("%")||i>0&&i<3&&["hsl","hsla","hsv","hsva"].includes(n)?parseFloat(o)/100:parseFloat(o));return Ab[n](...s)}else if(typeof e=="string"){let t=e.startsWith("#")?e.slice(1):e;return[3,4].includes(t.length)?t=t.split("").map(n=>n+n).join(""):[6,8].includes(t.length),Ib(t)}else if(typeof e=="object"){if(Ji(e,["r","g","b"]))return e;if(Ji(e,["h","s","l"]))return Ls(Xm(e));if(Ji(e,["h","s","v"]))return Ls(e)}throw new TypeError(`Invalid color: ${e==null?e:String(e)||e.constructor.name}
Expected #hex, #hexa, rgb(), rgba(), hsl(), hsla(), object or number`)}function Ls(e){const{h:t,s:n,v:r,a:s}=e,o=a=>{const l=(a+t/60)%6;return r-r*n*Math.max(Math.min(l,4-l,1),0)},i=[o(5),o(3),o(1)].map(a=>Math.round(a*255));return{r:i[0],g:i[1],b:i[2],a:s}}function Eu(e){return Ls(Xm(e))}function Xm(e){const{h:t,s:n,l:r,a:s}=e,o=r+n*Math.min(r,1-r),i=o===0?0:2-2*r/o;return{h:t,s:i,v:o,a:s}}function fo(e){const t=Math.round(e).toString(16);return("00".substr(0,2-t.length)+t).toUpperCase()}function Pb(e){let{r:t,g:n,b:r,a:s}=e;return`#${[fo(t),fo(n),fo(r),s!==void 0?fo(Math.round(s*255)):""].join("")}`}function Ib(e){e=Ob(e);let[t,n,r,s]=rb(e,2).map(o=>parseInt(o,16));return s=s===void 0?s:s/255,{r:t,g:n,b:r,a:s}}function Ob(e){return e.startsWith("#")&&(e=e.slice(1)),e=e.replace(/([^0-9a-f])/gi,"F"),(e.length===3||e.length===4)&&(e=e.split("").map(t=>t+t).join("")),e.length!==6&&(e=au(au(e,6),8,"F")),e}function Rb(e,t){const n=Gm($l(e));return n[0]=n[0]+t*10,Ym(qm(n))}function Lb(e,t){const n=Gm($l(e));return n[0]=n[0]-t*10,Ym(qm(n))}function Nb(e){const t=un(e);return $l(t)[1]}function Jm(e){const t=Math.abs(Su(un(0),un(e)));return Math.abs(Su(un(16777215),un(e)))>Math.min(t,50)?"#fff":"#000"}function dt(e,t){const n=On();if(!n)throw new Error(`[Vuetify] ${e} must be called from inside a setup function`);return n}function pn(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"composables";const t=dt(e).type;return br(t?.aliasName||t?.name)}function Db(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:dt("injectSelf");const{provides:n}=t;if(n&&e in n)return n[e]}const Gr=Symbol.for("vuetify:defaults");function Fb(e){return J(e)}function Bl(){const e=Le(Gr);if(!e)throw new Error("[Vuetify] Could not find defaults instance");return e}function Xs(e,t){const n=Bl(),r=J(e),s=U(()=>{if(ot(t?.disabled))return n.value;const i=ot(t?.scoped),a=ot(t?.reset),l=ot(t?.root);if(r.value==null&&!(i||a||l))return n.value;let u=Dt(r.value,{prev:n.value});if(i)return u;if(a||l){const c=Number(a||1/0);for(let f=0;f<=c&&!(!u||!("prev"in u));f++)u=u.prev;return u&&typeof l=="string"&&l in u&&(u=Dt(Dt(u,{prev:u}),u[l])),u}return u.prev?Dt(u.prev,u):u});return Ft(Gr,s),s}function Mb(e,t){return e.props&&(typeof e.props[t]<"u"||typeof e.props[br(t)]<"u")}function Vb(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0,n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:Bl();const r=dt("useDefaults");if(t=t??r.type.name??r.type.__name,!t)throw new Error("[Vuetify] Could not determine component name");const s=U(()=>n.value?.[e._as??t]),o=new Proxy(e,{get(l,u){const c=Reflect.get(l,u);if(u==="class"||u==="style")return[s.value?.[u],c].filter(m=>m!=null);if(Mb(r.vnode,u))return c;const f=s.value?.[u];if(f!==void 0)return f;const d=n.value?.global?.[u];return d!==void 0?d:c}}),i=Ae();en(()=>{if(s.value){const l=Object.entries(s.value).filter(u=>{let[c]=u;return c.startsWith(c[0].toUpperCase())});i.value=l.length?Object.fromEntries(l):void 0}else i.value=void 0});function a(){const l=Db(Gr,r);Ft(Gr,U(()=>i.value?Dt(l?.value??{},i.value):l?.value))}return{props:o,provideSubDefaults:a}}function Js(e){if(e._setup=e._setup??e.setup,!e.name)return e;if(e._setup){e.props=te(e.props??{},e.name)();const t=Object.keys(e.props).filter(n=>n!=="class"&&n!=="style");e.filterProps=function(r){return gi(r,t)},e.props._as=String,e.setup=function(r,s){const o=Bl();if(!o.value)return e._setup(r,s);const{props:i,provideSubDefaults:a}=Vb(r,r._as??e.name,o),l=e._setup(i,s);return a(),l}}return e}function ge(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;return t=>(e?Js:es)(t)}function Ul(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"div",n=arguments.length>2?arguments[2]:void 0;return ge()({name:n??Qr(It(e.replace(/__/g,"-"))),props:{tag:{type:String,default:t},...Ie()},setup(r,s){let{slots:o}=s;return()=>vn(r.tag,{class:[e,r.class],style:r.style},o.default?.())}})}function Zm(e){if(typeof e.getRootNode!="function"){for(;e.parentNode;)e=e.parentNode;return e!==document?null:document}const t=e.getRootNode();return t!==document&&t.getRootNode({composed:!0})!==document?null:t}const Uo="cubic-bezier(0.4, 0, 0.2, 1)",$b="cubic-bezier(0.0, 0, 0.2, 1)",Bb="cubic-bezier(0.4, 0, 1, 1)";function Ub(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;for(;e;){if(t?Hb(e):Hl(e))return e;e=e.parentElement}return document.scrollingElement}function Ho(e,t){const n=[];if(t&&e&&!t.contains(e))return n;for(;e&&(Hl(e)&&n.push(e),e!==t);)e=e.parentElement;return n}function Hl(e){if(!e||e.nodeType!==Node.ELEMENT_NODE)return!1;const t=window.getComputedStyle(e);return t.overflowY==="scroll"||t.overflowY==="auto"&&e.scrollHeight>e.clientHeight}function Hb(e){if(!e||e.nodeType!==Node.ELEMENT_NODE)return!1;const t=window.getComputedStyle(e);return["scroll","auto"].includes(t.overflowY)}function jb(e){for(;e;){if(window.getComputedStyle(e).position==="fixed")return!0;e=e.offsetParent}return!1}function ye(e){const t=dt("useRender");t.render=e}function Qm(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"content";const n=La(),r=J();if(Ke){const s=new ResizeObserver(o=>{o.length&&(t==="content"?r.value=o[0].contentRect:r.value=o[0].target.getBoundingClientRect())});Qt(()=>{s.disconnect()}),le(()=>n.el,(o,i)=>{i&&(s.unobserve(i),r.value=void 0),o&&s.observe(o)},{flush:"post"})}return{resizeRef:n,contentRect:oi(r)}}const jo=Symbol.for("vuetify:layout"),Wb=Symbol.for("vuetify:layout-item"),Cu=1e3,zb=te({overlaps:{type:Array,default:()=>[]},fullHeight:Boolean},"layout");function Kb(){const e=Le(jo);if(!e)throw new Error("[Vuetify] Could not find injected layout");return{getLayoutItem:e.getLayoutItem,mainRect:e.mainRect,mainStyles:e.mainStyles}}const Gb=(e,t,n,r)=>{let s={top:0,left:0,right:0,bottom:0};const o=[{id:"",layer:{...s}}];for(const i of e){const a=t.get(i),l=n.get(i),u=r.get(i);if(!a||!l||!u)continue;const c={...s,[a.value]:parseInt(s[a.value],10)+(u.value?parseInt(l.value,10):0)};o.push({id:i,layer:c}),s=c}return o};function qb(e){const t=Le(jo,null),n=U(()=>t?t.rootZIndex.value-100:Cu),r=J([]),s=st(new Map),o=st(new Map),i=st(new Map),a=st(new Map),l=st(new Map),{resizeRef:u,contentRect:c}=Qm(),f=U(()=>{const P=new Map,k=e.overlaps??[];for(const T of k.filter(A=>A.includes(":"))){const[A,H]=T.split(":");if(!r.value.includes(A)||!r.value.includes(H))continue;const V=s.get(A),D=s.get(H),G=o.get(A),ee=o.get(H);!V||!D||!G||!ee||(P.set(H,{position:V.value,amount:parseInt(G.value,10)}),P.set(A,{position:D.value,amount:-parseInt(ee.value,10)}))}return P}),d=U(()=>{const P=[...new Set([...i.values()].map(T=>T.value))].sort((T,A)=>T-A),k=[];for(const T of P){const A=r.value.filter(H=>i.get(H)?.value===T);k.push(...A)}return Gb(k,s,o,a)}),m=U(()=>!Array.from(l.values()).some(P=>P.value)),g=U(()=>d.value[d.value.length-1].layer),p=X(()=>({"--v-layout-left":me(g.value.left),"--v-layout-right":me(g.value.right),"--v-layout-top":me(g.value.top),"--v-layout-bottom":me(g.value.bottom),...m.value?void 0:{transition:"none"}})),w=U(()=>d.value.slice(1).map((P,k)=>{let{id:T}=P;const{layer:A}=d.value[k],H=o.get(T),V=s.get(T);return{id:T,...A,size:Number(H.value),position:V.value}})),h=P=>w.value.find(k=>k.id===P),v=dt("createLayout"),b=Ae(!1);Gt(()=>{b.value=!0}),Ft(jo,{register:(P,k)=>{let{id:T,order:A,position:H,layoutSize:V,elementSize:D,active:G,disableTransitions:ee,absolute:ne}=k;i.set(T,A),s.set(T,H),o.set(T,V),a.set(T,G),ee&&l.set(T,ee);const Z=Ur(Wb,v?.vnode).indexOf(P);Z>-1?r.value.splice(Z,0,T):r.value.push(T);const re=U(()=>w.value.findIndex(_e=>_e.id===T)),pe=U(()=>n.value+d.value.length*2-re.value*2),ue=U(()=>{const _e=H.value==="left"||H.value==="right",Me=H.value==="right",Ge=H.value==="bottom",Se=D.value??V.value,Ne=Se===0?"%":"px",F={[H.value]:0,zIndex:pe.value,transform:`translate${_e?"X":"Y"}(${(G.value?0:-(Se===0?100:Se))*(Me||Ge?-1:1)}${Ne})`,position:ne.value||n.value!==Cu?"absolute":"fixed",...m.value?void 0:{transition:"none"}};if(!b.value)return F;const q=w.value[re.value];if(!q)throw new Error(`[Vuetify] Could not find layout item "${T}"`);const Y=f.value.get(T);return Y&&(q[Y.position]+=Y.amount),{...F,height:_e?`calc(100% - ${q.top}px - ${q.bottom}px)`:D.value?`${D.value}px`:void 0,left:Me?void 0:`${q.left}px`,right:Me?`${q.right}px`:void 0,top:H.value!=="bottom"?`${q.top}px`:void 0,bottom:H.value!=="top"?`${q.bottom}px`:void 0,width:_e?D.value?`${D.value}px`:void 0:`calc(100% - ${q.left}px - ${q.right}px)`}}),xe=U(()=>({zIndex:pe.value-1}));return{layoutItemStyles:ue,layoutItemScrimStyles:xe,zIndex:pe}},unregister:P=>{i.delete(P),s.delete(P),o.delete(P),a.delete(P),l.delete(P),r.value=r.value.filter(k=>k!==P)},mainRect:g,mainStyles:p,getLayoutItem:h,items:w,layoutRect:c,rootZIndex:n});const y=X(()=>["v-layout",{"v-layout--full-height":e.fullHeight}]),E=X(()=>({zIndex:t?n.value:void 0,position:t?"relative":void 0,overflow:t?"hidden":void 0}));return{layoutClasses:y,layoutStyles:E,getLayoutItem:h,items:w,layoutRect:c,layoutRef:u}}function Cr(e,t){let n;function r(){n=Wn(),n.run(()=>t.length?t(()=>{n?.stop(),r()}):t())}le(e,s=>{s&&!n?r():s||(n?.stop(),n=void 0)},{immediate:!0}),St(()=>{n?.stop()})}function Vt(e,t,n){let r=arguments.length>3&&arguments[3]!==void 0?arguments[3]:f=>f,s=arguments.length>4&&arguments[4]!==void 0?arguments[4]:f=>f;const o=dt("useProxiedModel"),i=J(e[t]!==void 0?e[t]:n),a=br(t),u=U(a!==t?()=>(e[t],!!((o.vnode.props?.hasOwnProperty(t)||o.vnode.props?.hasOwnProperty(a))&&(o.vnode.props?.hasOwnProperty(`onUpdate:${t}`)||o.vnode.props?.hasOwnProperty(`onUpdate:${a}`)))):()=>(e[t],!!(o.vnode.props?.hasOwnProperty(t)&&o.vnode.props?.hasOwnProperty(`onUpdate:${t}`))));Cr(()=>!u.value,()=>{le(()=>e[t],f=>{i.value=f})});const c=U({get(){const f=e[t];return r(u.value?f:i.value)},set(f){const d=s(f),m=Ee(u.value?e[t]:i.value);m===d||r(m)===f||(i.value=d,o?.emit(`update:${t}`,d))}});return Object.defineProperty(c,"externalValue",{get:()=>u.value?e[t]:i.value}),c}const Yb={badge:"Badge",open:"Open",close:"Close",dismiss:"Dismiss",confirmEdit:{ok:"OK",cancel:"Cancel"},dataIterator:{noResultsText:"No matching records found",loadingText:"Loading items..."},dataTable:{itemsPerPageText:"Rows per page:",ariaLabel:{sortDescending:"Sorted descending.",sortAscending:"Sorted ascending.",sortNone:"Not sorted.",activateNone:"Activate to remove sorting.",activateDescending:"Activate to sort descending.",activateAscending:"Activate to sort ascending."},sortBy:"Sort by"},dataFooter:{itemsPerPageText:"Items per page:",itemsPerPageAll:"All",nextPage:"Next page",prevPage:"Previous page",firstPage:"First page",lastPage:"Last page",pageText:"{0}-{1} of {2}"},dateRangeInput:{divider:"to"},datePicker:{itemsSelected:"{0} selected",range:{title:"Select dates",header:"Enter dates"},title:"Select date",header:"Enter date",input:{placeholder:"Enter date"}},noDataText:"No data available",carousel:{prev:"Previous visual",next:"Next visual",ariaLabel:{delimiter:"Carousel slide {0} of {1}"}},calendar:{moreEvents:"{0} more",today:"Today"},input:{clear:"Clear {0}",prependAction:"{0} prepended action",appendAction:"{0} appended action",otp:"Please enter OTP character {0}"},fileInput:{counter:"{0} files",counterSize:"{0} files ({1} in total)"},fileUpload:{title:"Drag and drop files here",divider:"or",browse:"Browse Files"},timePicker:{am:"AM",pm:"PM",title:"Select Time"},pagination:{ariaLabel:{root:"Pagination Navigation",next:"Next page",previous:"Previous page",page:"Go to page {0}",currentPage:"Page {0}, Current page",first:"First page",last:"Last page"}},stepper:{next:"Next",prev:"Previous"},rating:{ariaLabel:{item:"Rating {0} of {1}"}},loading:"Loading...",infiniteScroll:{loadMore:"Load more",empty:"No more"},rules:{required:"This field is required",email:"Please enter a valid email",number:"This field can only contain numbers",integer:"This field can only contain integer values",capital:"This field can only contain uppercase letters",maxLength:"You must enter a maximum of {0} characters",minLength:"You must enter a minimum of {0} characters",strictLength:"The length of the entered field is invalid",exclude:"The {0} character is not allowed",notEmpty:"Please choose at least one value",pattern:"Invalid format"},hotkey:{then:"then",ctrl:"Ctrl",command:"Command",space:"Space",shift:"Shift",alt:"Alt",enter:"Enter",escape:"Escape",upArrow:"Up Arrow",downArrow:"Down Arrow",leftArrow:"Left Arrow",rightArrow:"Right Arrow",backspace:"Backspace",option:"Option",plus:"plus",shortcut:"Keyboard shortcut: {0}"}},xu="$vuetify.",Tu=(e,t)=>e.replace(/\{(\d+)\}/g,(n,r)=>String(t[Number(r)])),eh=(e,t,n)=>function(r){for(var s=arguments.length,o=new Array(s>1?s-1:0),i=1;i<s;i++)o[i-1]=arguments[i];if(!r.startsWith(xu))return Tu(r,o);const a=r.replace(xu,""),l=e.value&&n.value[e.value],u=t.value&&n.value[t.value];let c=su(l,a,null);return c||(`${r}${e.value}`,c=su(u,a,null)),c||(c=r),typeof c!="string"&&(c=r),Tu(c,o)};function jl(e,t){return(n,r)=>new Intl.NumberFormat([e.value,t.value],r).format(n)}function th(e,t){return jl(e,t)(.1).includes(",")?",":"."}function ea(e,t,n){const r=Vt(e,t,e[t]??n.value);return r.value=e[t]??n.value,le(n,s=>{e[t]==null&&(r.value=n.value)}),r}function nh(e){return t=>{const n=ea(t,"locale",e.current),r=ea(t,"fallback",e.fallback),s=ea(t,"messages",e.messages);return{name:"vuetify",current:n,fallback:r,messages:s,decimalSeparator:X(()=>th(n,r)),t:eh(n,r,s),n:jl(n,r),provide:nh({current:n,fallback:r,messages:s})}}}function Xb(e){const t=Ae(e?.locale??"en"),n=Ae(e?.fallback??"en"),r=J({en:Yb,...e?.messages});return{name:"vuetify",current:t,fallback:n,messages:r,decimalSeparator:X(()=>e?.decimalSeparator??th(t,n)),t:eh(t,n,r),n:jl(t,n),provide:nh({current:t,fallback:n,messages:r})}}const Wo=Symbol.for("vuetify:locale");function Jb(e){return e.name!=null}function Zb(e){const t=e?.adapter&&Jb(e?.adapter)?e?.adapter:Xb(e),n=e_(t,e);return{...t,...n}}function rh(){const e=Le(Wo);if(!e)throw new Error("[Vuetify] Could not find injected locale instance");return e}function Qb(){return{af:!1,ar:!0,bg:!1,ca:!1,ckb:!1,cs:!1,de:!1,el:!1,en:!1,es:!1,et:!1,fa:!0,fi:!1,fr:!1,hr:!1,hu:!1,he:!0,id:!1,it:!1,ja:!1,km:!1,ko:!1,lv:!1,lt:!1,nl:!1,no:!1,pl:!1,pt:!1,ro:!1,ru:!1,sk:!1,sl:!1,srCyrl:!1,srLatn:!1,sv:!1,th:!1,tr:!1,az:!1,uk:!1,vi:!1,zhHans:!1,zhHant:!1}}function e_(e,t){const n=J(t?.rtl??Qb()),r=U(()=>n.value[e.current.value]??!1);return{isRtl:r,rtl:n,rtlClasses:X(()=>`v-locale--is-${r.value?"rtl":"ltr"}`)}}function Pr(){const e=Le(Wo);if(!e)throw new Error("[Vuetify] Could not find injected rtl instance");return{isRtl:e.isRtl,rtlClasses:e.rtlClasses}}const Ns=Symbol.for("vuetify:theme"),ct=te({theme:String},"theme");function ku(){return{defaultTheme:"light",prefix:"v-",variations:{colors:[],lighten:0,darken:0},themes:{light:{dark:!1,colors:{background:"#FFFFFF",surface:"#FFFFFF","surface-bright":"#FFFFFF","surface-light":"#EEEEEE","surface-variant":"#424242","on-surface-variant":"#EEEEEE",primary:"#1867C0","primary-darken-1":"#1F5592",secondary:"#48A9A6","secondary-darken-1":"#018786",error:"#B00020",info:"#2196F3",success:"#4CAF50",warning:"#FB8C00"},variables:{"border-color":"#000000","border-opacity":.12,"high-emphasis-opacity":.87,"medium-emphasis-opacity":.6,"disabled-opacity":.38,"idle-opacity":.04,"hover-opacity":.04,"focus-opacity":.12,"selected-opacity":.08,"activated-opacity":.12,"pressed-opacity":.12,"dragged-opacity":.08,"theme-kbd":"#EEEEEE","theme-on-kbd":"#000000","theme-code":"#F5F5F5","theme-on-code":"#000000"}},dark:{dark:!0,colors:{background:"#121212",surface:"#212121","surface-bright":"#ccbfd6","surface-light":"#424242","surface-variant":"#c8c8c8","on-surface-variant":"#000000",primary:"#2196F3","primary-darken-1":"#277CC1",secondary:"#54B6B2","secondary-darken-1":"#48A9A6",error:"#CF6679",info:"#2196F3",success:"#4CAF50",warning:"#FB8C00"},variables:{"border-color":"#FFFFFF","border-opacity":.12,"high-emphasis-opacity":1,"medium-emphasis-opacity":.7,"disabled-opacity":.5,"idle-opacity":.1,"hover-opacity":.04,"focus-opacity":.12,"selected-opacity":.08,"activated-opacity":.12,"pressed-opacity":.16,"dragged-opacity":.08,"theme-kbd":"#424242","theme-on-kbd":"#FFFFFF","theme-code":"#343434","theme-on-code":"#CCCCCC"}}},stylesheetId:"vuetify-theme-stylesheet",scoped:!1,unimportant:!1,utilities:!0}}function t_(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:ku();const t=ku();if(!e)return{...t,isDisabled:!0};const n={};for(const[r,s]of Object.entries(e.themes??{})){const o=s.dark||r==="dark"?t.themes?.dark:t.themes?.light;n[r]=Dt(o,s)}return Dt(t,{...e,themes:n})}function ur(e,t,n,r){e.push(`${o_(t,r)} {
`,...n.map(s=>`  ${s};
`),`}
`)}function Au(e,t){const n=e.dark?2:1,r=e.dark?1:2,s=[];for(const[o,i]of Object.entries(e.colors)){const a=un(i);s.push(`--${t}theme-${o}: ${a.r},${a.g},${a.b}`),o.startsWith("on-")||s.push(`--${t}theme-${o}-overlay-multiplier: ${Nb(i)>.18?n:r}`)}for(const[o,i]of Object.entries(e.variables)){const a=typeof i=="string"&&i.startsWith("#")?un(i):void 0,l=a?`${a.r}, ${a.g}, ${a.b}`:void 0;s.push(`--${t}${o}: ${l??i}`)}return s}function n_(e,t,n){const r={};if(n)for(const s of["lighten","darken"]){const o=s==="lighten"?Rb:Lb;for(const i of Fm(n[s],1))r[`${e}-${s}-${i}`]=Pb(o(un(t),i))}return r}function r_(e,t){if(!t)return{};let n={};for(const r of t.colors){const s=e[r];s&&(n={...n,...n_(r,s,t)})}return n}function s_(e){const t={};for(const n of Object.keys(e)){if(n.startsWith("on-")||e[`on-${n}`])continue;const r=`on-${n}`,s=un(e[n]);t[r]=Jm(s)}return t}function o_(e,t){if(!t)return e;const n=`:where(${t})`;return e===":root"?n:`${n} ${e}`}function i_(e,t,n){const r=a_(e,t);r&&(r.innerHTML=n)}function a_(e,t){if(!Ke)return null;let n=document.getElementById(e);return n||(n=document.createElement("style"),n.id=e,n.type="text/css",t&&n.setAttribute("nonce",t),document.head.appendChild(n)),n}function l_(e){const t=t_(e),n=Ae(t.defaultTheme),r=J(t.themes),s=Ae("light"),o=U({get(){return n.value==="system"?s.value:n.value},set(h){n.value=h}}),i=U(()=>{const h={};for(const[v,b]of Object.entries(r.value)){const y={...b.colors,...r_(b.colors,t.variations)};h[v]={...b,colors:{...y,...s_(y)}}}return h}),a=X(()=>i.value[o.value]),l=U(()=>{const h=[],v=t.unimportant?"":" !important",b=t.scoped?t.prefix:"";a.value?.dark&&ur(h,":root",["color-scheme: dark"],t.scope),ur(h,":root",Au(a.value,t.prefix),t.scope);for(const[y,E]of Object.entries(i.value))ur(h,`.${t.prefix}theme--${y}`,[`color-scheme: ${E.dark?"dark":"normal"}`,...Au(E,t.prefix)],t.scope);if(t.utilities){const y=[],E=[],P=new Set(Object.values(i.value).flatMap(k=>Object.keys(k.colors)));for(const k of P)k.startsWith("on-")?ur(E,`.${k}`,[`color: rgb(var(--${t.prefix}theme-${k}))${v}`],t.scope):(ur(y,`.${b}bg-${k}`,[`--${t.prefix}theme-overlay-multiplier: var(--${t.prefix}theme-${k}-overlay-multiplier)`,`background-color: rgb(var(--${t.prefix}theme-${k}))${v}`,`color: rgb(var(--${t.prefix}theme-on-${k}))${v}`],t.scope),ur(E,`.${b}text-${k}`,[`color: rgb(var(--${t.prefix}theme-${k}))${v}`],t.scope),ur(E,`.${b}border-${k}`,[`--${t.prefix}border-color: var(--${t.prefix}theme-${k})`],t.scope));h.push(...y,...E)}return h.map((y,E)=>E===0?y:`    ${y}`).join("")}),u=X(()=>t.isDisabled?void 0:`${t.prefix}theme--${o.value}`),c=X(()=>Object.keys(i.value));if(Qy){let v=function(){s.value=h.matches?"dark":"light"};var w=v;const h=window.matchMedia("(prefers-color-scheme: dark)");v(),h.addEventListener("change",v,{passive:!0}),vl()&&St(()=>{h.removeEventListener("change",v)})}function f(h){if(t.isDisabled)return;const v=h._context.provides.usehead;if(v){let E=function(){return{style:[{textContent:l.value,id:t.stylesheetId,nonce:t.cspNonce||!1}]}};var b=E;if(v.push){const P=v.push(E);Ke&&le(l,()=>{P.patch(E)})}else Ke?(v.addHeadObjs(X(E)),en(()=>v.updateDOM())):v.addHeadObjs(E())}else{let E=function(){i_(t.stylesheetId,t.cspNonce,l.value)};var y=E;Ke?le(l,E,{immediate:!0}):E()}}function d(h){c.value.includes(h)&&(o.value=h)}function m(){let h=arguments.length>0&&arguments[0]!==void 0?arguments[0]:c.value;const v=h.indexOf(o.value),b=v===-1?0:(v+1)%h.length;d(h[b])}function g(){let h=arguments.length>0&&arguments[0]!==void 0?arguments[0]:["light","dark"];m(h)}const p=new Proxy(o,{get(h,v){return Reflect.get(h,v)},set(h,v,b){return v==="value"&&Km(`theme.global.name.value = ${b}`,`theme.change('${b}')`),Reflect.set(h,v,b)}});return{install:f,change:d,cycle:m,toggle:g,isDisabled:t.isDisabled,name:o,themes:r,current:a,computedThemes:i,prefix:t.prefix,themeClasses:u,styles:l,global:{name:p,current:a}}}function ht(e){dt("provideTheme");const t=Le(Ns,null);if(!t)throw new Error("Could not find Vuetify theme injection");const n=X(()=>e.theme??t.name.value),r=X(()=>t.themes.value[n.value]),s=X(()=>t.isDisabled?void 0:`${t.prefix}theme--${n.value}`),o={...t,name:n,current:r,themeClasses:s};return Ft(Ns,o),o}function c_(){dt("useTheme");const e=Le(Ns,null);if(!e)throw new Error("Could not find Vuetify theme injection");return e}const u_=te({...Ie(),...zb({fullHeight:!0}),...ct()},"VApp"),f_=ge()({name:"VApp",props:u_(),setup(e,t){let{slots:n}=t;const r=ht(e),{layoutClasses:s,getLayoutItem:o,items:i,layoutRef:a}=qb(e),{rtlClasses:l}=Pr();return ye(()=>N("div",{ref:a,class:de(["v-application",r.themeClasses.value,s.value,l.value,e.class]),style:be([e.style])},[N("div",{class:"v-application__wrap"},[n.default?.()])])),{getLayoutItem:o,items:i,theme:r}}}),Xn=te({border:[Boolean,Number,String]},"border");function Jn(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:pn();return{borderClasses:U(()=>{const r=e.border;return r===!0||r===""?`${t}--border`:typeof r=="string"||r===0?String(r).split(" ").map(s=>`border-${s}`):[]})}}const d_=[null,"default","comfortable","compact"],Zn=te({density:{type:String,default:"default",validator:e=>d_.includes(e)}},"density");function Ir(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:pn();return{densityClasses:X(()=>`${t}--density-${e.density}`)}}const Qn=te({elevation:{type:[Number,String],validator(e){const t=parseInt(e);return!isNaN(t)&&t>=0&&t<=24}}},"elevation");function er(e){return{elevationClasses:X(()=>{const n=je(e)?e.value:e.elevation;return n==null?[]:[`elevation-${n}`]})}}const $t=te({rounded:{type:[Boolean,Number,String],default:void 0},tile:Boolean},"rounded");function Bt(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:pn();return{roundedClasses:U(()=>{const r=je(e)?e.value:e.rounded,s=je(e)?e.value:e.tile,o=[];if(r===!0||r==="")o.push(`${t}--rounded`);else if(typeof r=="string"||r===0)for(const i of String(r).split(" "))o.push(`rounded-${i}`);else(s||r===!1)&&o.push("rounded-0");return o})}}const gt=te({tag:{type:[String,Object,Function],default:"div"}},"tag");function Wl(e){return Ml(()=>{const t=gn(e),n=[],r={};if(t.background)if(Da(t.background)){if(r.backgroundColor=t.background,!t.text&&kb(t.background)){const s=un(t.background);if(s.a==null||s.a===1){const o=Jm(s);r.color=o,r.caretColor=o}}}else n.push(`bg-${t.background}`);return t.text&&(Da(t.text)?(r.color=t.text,r.caretColor=t.text):n.push(`text-${t.text}`)),{colorClasses:n,colorStyles:r}})}function xr(e){const{colorClasses:t,colorStyles:n}=Wl(()=>({text:gn(e)}));return{textColorClasses:t,textColorStyles:n}}function mn(e){const{colorClasses:t,colorStyles:n}=Wl(()=>({background:gn(e)}));return{backgroundColorClasses:t,backgroundColorStyles:n}}const m_=["elevated","flat","tonal","outlined","text","plain"];function ns(e,t){return N(ke,null,[e&&N("span",{key:"overlay",class:de(`${t}__overlay`)},null),N("span",{key:"underlay",class:de(`${t}__underlay`)},null)])}const Or=te({color:String,variant:{type:String,default:"elevated",validator:e=>m_.includes(e)}},"variant");function rs(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:pn();const n=X(()=>{const{variant:o}=gn(e);return`${t}--variant-${o}`}),{colorClasses:r,colorStyles:s}=Wl(()=>{const{variant:o,color:i}=gn(e);return{[["elevated","flat"].includes(o)?"background":"text"]:i}});return{colorClasses:r,colorStyles:s,variantClasses:n}}const sh=te({baseColor:String,divided:Boolean,direction:{type:String,default:"horizontal"},...Xn(),...Ie(),...Zn(),...Qn(),...$t(),...gt(),...ct(),...Or()},"VBtnGroup"),Pu=ge()({name:"VBtnGroup",props:sh(),setup(e,t){let{slots:n}=t;const{themeClasses:r}=ht(e),{densityClasses:s}=Ir(e),{borderClasses:o}=Jn(e),{elevationClasses:i}=er(e),{roundedClasses:a}=Bt(e);Xs({VBtn:{height:X(()=>e.direction==="horizontal"?"auto":null),baseColor:X(()=>e.baseColor),color:X(()=>e.color),density:X(()=>e.density),flat:!0,variant:X(()=>e.variant)}}),ye(()=>R(e.tag,{class:de(["v-btn-group",`v-btn-group--${e.direction}`,{"v-btn-group--divided":e.divided},r.value,o.value,s.value,i.value,a.value,e.class]),style:be(e.style)},n))}}),oh=te({modelValue:{type:null,default:void 0},multiple:Boolean,mandatory:[Boolean,String],max:Number,selectedClass:String,disabled:Boolean},"group"),ih=te({value:null,disabled:Boolean,selectedClass:String},"group-item");function ah(e,t){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;const r=dt("useGroupItem");if(!r)throw new Error("[Vuetify] useGroupItem composable must be used inside a component setup function");const s=Ks();Ft(Symbol.for(`${t.description}:id`),s);const o=Le(t,null);if(!o){if(!n)return o;throw new Error(`[Vuetify] Could not find useGroup injection with symbol ${t.description}`)}const i=X(()=>e.value),a=U(()=>!!(o.disabled.value||e.disabled));o.register({id:s,value:i,disabled:a},r),Qt(()=>{o.unregister(s)});const l=U(()=>o.isSelected(s)),u=U(()=>o.items.value[0].id===s),c=U(()=>o.items.value[o.items.value.length-1].id===s),f=U(()=>l.value&&[o.selectedClass.value,e.selectedClass]);return le(l,d=>{r.emit("group:selected",{value:d})},{flush:"sync"}),{id:s,isSelected:l,isFirst:u,isLast:c,toggle:()=>o.select(s,!l.value),select:d=>o.select(s,d),selectedClass:f,value:i,disabled:a,group:o}}function lh(e,t){let n=!1;const r=st([]),s=Vt(e,"modelValue",[],d=>d==null?[]:ch(r,yr(d)),d=>{const m=g_(r,d);return e.multiple?m:m[0]}),o=dt("useGroup");function i(d,m){const g=d,p=Symbol.for(`${t.description}:id`),h=Ur(p,o?.vnode).indexOf(m);ot(g.value)==null&&(g.value=h,g.useIndexAsValue=!0),h>-1?r.splice(h,0,g):r.push(g)}function a(d){if(n)return;l();const m=r.findIndex(g=>g.id===d);r.splice(m,1)}function l(){const d=r.find(m=>!m.disabled);d&&e.mandatory==="force"&&!s.value.length&&(s.value=[d.id])}Gt(()=>{l()}),Qt(()=>{n=!0}),kl(()=>{for(let d=0;d<r.length;d++)r[d].useIndexAsValue&&(r[d].value=d)});function u(d,m){const g=r.find(p=>p.id===d);if(!(m&&g?.disabled))if(e.multiple){const p=s.value.slice(),w=p.findIndex(v=>v===d),h=~w;if(m=m??!h,h&&e.mandatory&&p.length<=1||!h&&e.max!=null&&p.length+1>e.max)return;w<0&&m?p.push(d):w>=0&&!m&&p.splice(w,1),s.value=p}else{const p=s.value.includes(d);if(e.mandatory&&p||!p&&!m)return;s.value=m??!p?[d]:[]}}function c(d){if(e.multiple,s.value.length){const m=s.value[0],g=r.findIndex(h=>h.id===m);let p=(g+d)%r.length,w=r[p];for(;w.disabled&&p!==g;)p=(p+d)%r.length,w=r[p];if(w.disabled)return;s.value=[r[p].id]}else{const m=r.find(g=>!g.disabled);m&&(s.value=[m.id])}}const f={register:i,unregister:a,selected:s,select:u,disabled:X(()=>e.disabled),prev:()=>c(r.length-1),next:()=>c(1),isSelected:d=>s.value.includes(d),selectedClass:X(()=>e.selectedClass),items:X(()=>r),getItemIndex:d=>h_(r,d)};return Ft(t,f),f}function h_(e,t){const n=ch(e,[t]);return n.length?e.findIndex(r=>r.id===n[0]):-1}function ch(e,t){const n=[];return t.forEach(r=>{const s=e.find(i=>Rs(r,i.value)),o=e[r];s?.value!=null?n.push(s.id):o!=null&&n.push(o.id)}),n}function g_(e,t){const n=[];return t.forEach(r=>{const s=e.findIndex(o=>o.id===r);if(~s){const o=e[s];n.push(o.value!=null?o.value:s)}}),n}const uh=Symbol.for("vuetify:v-btn-toggle"),v_=te({...sh(),...oh()},"VBtnToggle");ge()({name:"VBtnToggle",props:v_(),emits:{"update:modelValue":e=>!0},setup(e,t){let{slots:n}=t;const{isSelected:r,next:s,prev:o,select:i,selected:a}=lh(e,uh);return ye(()=>{const l=Pu.filterProps(e);return R(Pu,Pe({class:["v-btn-toggle",e.class]},l,{style:e.style}),{default:()=>[n.default?.({isSelected:r,next:s,prev:o,select:i,selected:a})]})}),{next:s,prev:o,select:i}}});const p_=te({defaults:Object,disabled:Boolean,reset:[Number,String],root:[Boolean,String],scoped:Boolean},"VDefaultsProvider"),Ye=ge(!1)({name:"VDefaultsProvider",props:p_(),setup(e,t){let{slots:n}=t;const{defaults:r,disabled:s,reset:o,root:i,scoped:a}=Cl(e);return Xs(r,{reset:o,root:i,scoped:a,disabled:s}),()=>n.default?.()}}),y_={collapse:"mdi-chevron-up",complete:"mdi-check",cancel:"mdi-close-circle",close:"mdi-close",delete:"mdi-close-circle",clear:"mdi-close-circle",success:"mdi-check-circle",info:"mdi-information",warning:"mdi-alert-circle",error:"mdi-close-circle",prev:"mdi-chevron-left",next:"mdi-chevron-right",checkboxOn:"mdi-checkbox-marked",checkboxOff:"mdi-checkbox-blank-outline",checkboxIndeterminate:"mdi-minus-box",delimiter:"mdi-circle",sortAsc:"mdi-arrow-up",sortDesc:"mdi-arrow-down",expand:"mdi-chevron-down",menu:"mdi-menu",subgroup:"mdi-menu-down",dropdown:"mdi-menu-down",radioOn:"mdi-radiobox-marked",radioOff:"mdi-radiobox-blank",edit:"mdi-pencil",ratingEmpty:"mdi-star-outline",ratingFull:"mdi-star",ratingHalf:"mdi-star-half-full",loading:"mdi-cached",first:"mdi-page-first",last:"mdi-page-last",unfold:"mdi-unfold-more-horizontal",file:"mdi-paperclip",plus:"mdi-plus",minus:"mdi-minus",calendar:"mdi-calendar",treeviewCollapse:"mdi-menu-down",treeviewExpand:"mdi-menu-right",eyeDropper:"mdi-eyedropper",upload:"mdi-cloud-upload",color:"mdi-palette",command:"mdi-apple-keyboard-command",ctrl:"mdi-apple-keyboard-control",space:"mdi-keyboard-space",shift:"mdi-apple-keyboard-shift",alt:"mdi-apple-keyboard-option",enter:"mdi-keyboard-return",arrowup:"mdi-arrow-up",arrowdown:"mdi-arrow-down",arrowleft:"mdi-arrow-left",arrowright:"mdi-arrow-right",backspace:"mdi-backspace"},fh={component:e=>vn(mh,{...e,class:"mdi"})},it=[String,Function,Object,Array],Fa=Symbol.for("vuetify:icons"),vi=te({icon:{type:it},tag:{type:[String,Object,Function],required:!0}},"icon"),Iu=ge()({name:"VComponentIcon",props:vi(),setup(e,t){let{slots:n}=t;return()=>{const r=e.icon;return R(e.tag,null,{default:()=>[e.icon?R(r,null,null):n.default?.()]})}}}),dh=Js({name:"VSvgIcon",inheritAttrs:!1,props:vi(),setup(e,t){let{attrs:n}=t;return()=>R(e.tag,Pe(n,{style:null}),{default:()=>[N("svg",{class:"v-icon__svg",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",role:"img","aria-hidden":"true"},[Array.isArray(e.icon)?e.icon.map(r=>Array.isArray(r)?N("path",{d:r[0],"fill-opacity":r[1]},null):N("path",{d:r},null)):N("path",{d:e.icon},null)])]})}});Js({name:"VLigatureIcon",props:vi(),setup(e){return()=>R(e.tag,null,{default:()=>[e.icon]})}});const mh=Js({name:"VClassIcon",props:vi(),setup(e){return()=>R(e.tag,{class:de(e.icon)},null)}});function b_(){return{svg:{component:dh},class:{component:mh}}}function __(e){const t=b_(),n=e?.defaultSet??"mdi";return n==="mdi"&&!t.mdi&&(t.mdi=fh),Dt({defaultSet:n,sets:t,aliases:{...y_,vuetify:["M8.2241 14.2009L12 21L22 3H14.4459L8.2241 14.2009Z",["M7.26303 12.4733L7.00113 12L2 3H12.5261C12.5261 3 12.5261 3 12.5261 3L7.26303 12.4733Z",.6]],"vuetify-outline":"svg:M7.26 12.47 12.53 3H2L7.26 12.47ZM14.45 3 8.22 14.2 12 21 22 3H14.45ZM18.6 5 12 16.88 10.51 14.2 15.62 5ZM7.26 8.35 5.4 5H9.13L7.26 8.35Z","vuetify-play":["m6.376 13.184-4.11-7.192C1.505 4.66 2.467 3 4.003 3h8.532l-.953 1.576-.006.01-.396.677c-.429.732-.214 1.507.194 2.015.404.503 1.092.878 1.869.806a3.72 3.72 0 0 1 1.005.022c.276.053.434.143.523.237.138.146.38.635-.25 2.09-.893 1.63-1.553 1.722-1.847 1.677-.213-.033-.468-.158-.756-.406a4.95 4.95 0 0 1-.8-.927c-.39-.564-1.04-.84-1.66-.846-.625-.006-1.316.27-1.693.921l-.478.826-.911 1.506Z",["M9.093 11.552c.046-.079.144-.15.32-.148a.53.53 0 0 1 .43.207c.285.414.636.847 1.046 1.2.405.35.914.662 1.516.754 1.334.205 2.502-.698 3.48-2.495l.014-.028.013-.03c.687-1.574.774-2.852-.005-3.675-.37-.391-.861-.586-1.333-.676a5.243 5.243 0 0 0-1.447-.044c-.173.016-.393-.073-.54-.257-.145-.18-.127-.316-.082-.392l.393-.672L14.287 3h5.71c1.536 0 2.499 1.659 1.737 2.992l-7.997 13.996c-.768 1.344-2.706 1.344-3.473 0l-3.037-5.314 1.377-2.278.004-.006.004-.007.481-.831Z",.6]]}},e)}const S_=e=>{const t=Le(Fa);if(!t)throw new Error("Missing Vuetify Icons provide!");return{iconData:U(()=>{const r=gn(e);if(!r)return{component:Iu};let s=r;if(typeof s=="string"&&(s=s.trim(),s.startsWith("$")&&(s=t.aliases?.[s.slice(1)])),Array.isArray(s))return{component:dh,icon:s};if(typeof s!="string")return{component:Iu,icon:s};const o=Object.keys(t.sets).find(l=>typeof s=="string"&&s.startsWith(`${l}:`)),i=o?s.slice(o.length+1):s;return{component:t.sets[o??t.defaultSet].component,icon:i}})}},w_=["x-small","small","default","large","x-large"],pi=te({size:{type:[String,Number],default:"default"}},"size");function yi(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:pn();return Ml(()=>{const n=e.size;let r,s;return $o(w_,n)?r=`${t}--size-${n}`:n&&(s={width:me(n),height:me(n)}),{sizeClasses:r,sizeStyles:s}})}const E_=te({color:String,disabled:Boolean,start:Boolean,end:Boolean,icon:it,opacity:[String,Number],...Ie(),...pi(),...gt({tag:"i"}),...ct()},"VIcon"),_t=ge()({name:"VIcon",props:E_(),setup(e,t){let{attrs:n,slots:r}=t;const s=Ae(),{themeClasses:o}=c_(),{iconData:i}=S_(()=>s.value||e.icon),{sizeClasses:a}=yi(e),{textColorClasses:l,textColorStyles:u}=xr(()=>e.color);return ye(()=>{const c=r.default?.();c&&(s.value=Um(c).filter(d=>d.type===ts&&d.children&&typeof d.children=="string")[0]?.children);const f=!!(n.onClick||n.onClickOnce);return R(i.value.component,{tag:e.tag,icon:i.value.icon,class:de(["v-icon","notranslate",o.value,a.value,l.value,{"v-icon--clickable":f,"v-icon--disabled":e.disabled,"v-icon--start":e.start,"v-icon--end":e.end},e.class]),style:be([{"--v-icon-opacity":e.opacity},a.value?void 0:{fontSize:me(e.size),height:me(e.size),width:me(e.size)},u.value,e.style]),role:f?"button":void 0,"aria-hidden":!f,tabindex:f?e.disabled?-1:0:void 0},{default:()=>[c]})}),{}}});function hh(e,t){const n=J(),r=Ae(!1);if(Dl){const s=new IntersectionObserver(o=>{r.value=!!o.find(i=>i.isIntersecting)},t);St(()=>{s.disconnect()}),le(n,(o,i)=>{i&&(s.unobserve(i),r.value=!1),o&&s.observe(o)},{flush:"post"})}return{intersectionRef:n,isIntersecting:r}}const C_=te({bgColor:String,color:String,indeterminate:[Boolean,String],modelValue:{type:[Number,String],default:0},rotate:{type:[Number,String],default:0},width:{type:[Number,String],default:4},...Ie(),...pi(),...gt({tag:"div"}),...ct()},"VProgressCircular"),gh=ge()({name:"VProgressCircular",props:C_(),setup(e,t){let{slots:n}=t;const r=20,s=2*Math.PI*r,o=J(),{themeClasses:i}=ht(e),{sizeClasses:a,sizeStyles:l}=yi(e),{textColorClasses:u,textColorStyles:c}=xr(()=>e.color),{textColorClasses:f,textColorStyles:d}=xr(()=>e.bgColor),{intersectionRef:m,isIntersecting:g}=hh(),{resizeRef:p,contentRect:w}=Qm(),h=X(()=>Er(parseFloat(e.modelValue),0,100)),v=X(()=>Number(e.width)),b=X(()=>l.value?Number(e.size):w.value?w.value.width:Math.max(v.value,32)),y=X(()=>r/(1-v.value/b.value)*2),E=X(()=>v.value/b.value*y.value),P=X(()=>me((100-h.value)/100*s));return en(()=>{m.value=o.value,p.value=o.value}),ye(()=>R(e.tag,{ref:o,class:de(["v-progress-circular",{"v-progress-circular--indeterminate":!!e.indeterminate,"v-progress-circular--visible":g.value,"v-progress-circular--disable-shrink":e.indeterminate==="disable-shrink"},i.value,a.value,u.value,e.class]),style:be([l.value,c.value,e.style]),role:"progressbar","aria-valuemin":"0","aria-valuemax":"100","aria-valuenow":e.indeterminate?void 0:h.value},{default:()=>[N("svg",{style:{transform:`rotate(calc(-90deg + ${Number(e.rotate)}deg))`},xmlns:"http://www.w3.org/2000/svg",viewBox:`0 0 ${y.value} ${y.value}`},[N("circle",{class:de(["v-progress-circular__underlay",f.value]),style:be(d.value),fill:"transparent",cx:"50%",cy:"50%",r,"stroke-width":E.value,"stroke-dasharray":s,"stroke-dashoffset":0},null),N("circle",{class:"v-progress-circular__overlay",fill:"transparent",cx:"50%",cy:"50%",r,"stroke-width":E.value,"stroke-dasharray":s,"stroke-dashoffset":P.value},null)]),n.default&&N("div",{class:"v-progress-circular__content"},[n.default({value:h.value})])]})),{}}}),tr=te({height:[Number,String],maxHeight:[Number,String],maxWidth:[Number,String],minHeight:[Number,String],minWidth:[Number,String],width:[Number,String]},"dimension");function nr(e){return{dimensionStyles:U(()=>{const n={},r=me(e.height),s=me(e.maxHeight),o=me(e.maxWidth),i=me(e.minHeight),a=me(e.minWidth),l=me(e.width);return r!=null&&(n.height=r),s!=null&&(n.maxHeight=s),o!=null&&(n.maxWidth=o),i!=null&&(n.minHeight=i),a!=null&&(n.minWidth=a),l!=null&&(n.width=l),n})}}const Ou={center:"center",top:"bottom",bottom:"top",left:"right",right:"left"},Zs=te({location:String},"location");function bi(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,n=arguments.length>2?arguments[2]:void 0;const{isRtl:r}=Pr();return{locationStyles:U(()=>{if(!e.location)return{};const{side:o,align:i}=Na(e.location.split(" ").length>1?e.location:`${e.location} center`,r.value);function a(u){return n?n(u):0}const l={};return o!=="center"&&(t?l[Ou[o]]=`calc(100% - ${a(o)}px)`:l[o]=0),i!=="center"?t?l[Ou[i]]=`calc(100% - ${a(i)}px)`:l[i]=0:(o==="center"?l.top=l.left="50%":l[{top:"left",bottom:"left",left:"top",right:"top"}[o]]="50%",l.transform={top:"translateX(-50%)",bottom:"translateX(-50%)",left:"translateY(-50%)",right:"translateY(-50%)",center:"translate(-50%, -50%)"}[o]),l})}}const x_=te({absolute:Boolean,active:{type:Boolean,default:!0},bgColor:String,bgOpacity:[Number,String],bufferValue:{type:[Number,String],default:0},bufferColor:String,bufferOpacity:[Number,String],clickable:Boolean,color:String,height:{type:[Number,String],default:4},indeterminate:Boolean,max:{type:[Number,String],default:100},modelValue:{type:[Number,String],default:0},opacity:[Number,String],reverse:Boolean,stream:Boolean,striped:Boolean,roundedBar:Boolean,...Ie(),...Zs({location:"top"}),...$t(),...gt(),...ct()},"VProgressLinear"),vh=ge()({name:"VProgressLinear",props:x_(),emits:{"update:modelValue":e=>!0},setup(e,t){let{slots:n}=t;const r=Vt(e,"modelValue"),{isRtl:s,rtlClasses:o}=Pr(),{themeClasses:i}=ht(e),{locationStyles:a}=bi(e),{textColorClasses:l,textColorStyles:u}=xr(()=>e.color),{backgroundColorClasses:c,backgroundColorStyles:f}=mn(()=>e.bgColor||e.color),{backgroundColorClasses:d,backgroundColorStyles:m}=mn(()=>e.bufferColor||e.bgColor||e.color),{backgroundColorClasses:g,backgroundColorStyles:p}=mn(()=>e.color),{roundedClasses:w}=Bt(e),{intersectionRef:h,isIntersecting:v}=hh(),b=U(()=>parseFloat(e.max)),y=U(()=>parseFloat(e.height)),E=U(()=>Er(parseFloat(e.bufferValue)/b.value*100,0,100)),P=U(()=>Er(parseFloat(r.value)/b.value*100,0,100)),k=U(()=>s.value!==e.reverse),T=U(()=>e.indeterminate?"fade-transition":"slide-x-transition"),A=Ke&&window.matchMedia?.("(forced-colors: active)").matches;function H(V){if(!h.value)return;const{left:D,right:G,width:ee}=h.value.getBoundingClientRect(),ne=k.value?ee-V.clientX+(G-ee):V.clientX-D;r.value=Math.round(ne/ee*b.value)}return ye(()=>R(e.tag,{ref:h,class:de(["v-progress-linear",{"v-progress-linear--absolute":e.absolute,"v-progress-linear--active":e.active&&v.value,"v-progress-linear--reverse":k.value,"v-progress-linear--rounded":e.rounded,"v-progress-linear--rounded-bar":e.roundedBar,"v-progress-linear--striped":e.striped},w.value,i.value,o.value,e.class]),style:be([{bottom:e.location==="bottom"?0:void 0,top:e.location==="top"?0:void 0,height:e.active?me(y.value):0,"--v-progress-linear-height":me(y.value),...e.absolute?a.value:{}},e.style]),role:"progressbar","aria-hidden":e.active?"false":"true","aria-valuemin":"0","aria-valuemax":e.max,"aria-valuenow":e.indeterminate?void 0:Math.min(parseFloat(r.value),b.value),onClick:e.clickable&&H},{default:()=>[e.stream&&N("div",{key:"stream",class:de(["v-progress-linear__stream",l.value]),style:{...u.value,[k.value?"left":"right"]:me(-y.value),borderTop:`${me(y.value/2)} dotted`,opacity:parseFloat(e.bufferOpacity),top:`calc(50% - ${me(y.value/4)})`,width:me(100-E.value,"%"),"--v-progress-linear-stream-to":me(y.value*(k.value?1:-1))}},null),N("div",{class:de(["v-progress-linear__background",A?void 0:c.value]),style:be([f.value,{opacity:parseFloat(e.bgOpacity),width:e.stream?0:void 0}])},null),N("div",{class:de(["v-progress-linear__buffer",A?void 0:d.value]),style:be([m.value,{opacity:parseFloat(e.bufferOpacity),width:me(E.value,"%")}])},null),R(wr,{name:T.value},{default:()=>[e.indeterminate?N("div",{class:"v-progress-linear__indeterminate"},[["long","short"].map(V=>N("div",{key:V,class:de(["v-progress-linear__indeterminate",V,A?void 0:g.value]),style:be(p.value)},null))]):N("div",{class:de(["v-progress-linear__determinate",A?void 0:g.value]),style:be([p.value,{width:me(P.value,"%")}])},null)]}),n.default&&N("div",{class:"v-progress-linear__content"},[n.default({value:P.value,buffer:E.value})])]})),{}}}),zl=te({loading:[Boolean,String]},"loader");function Kl(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:pn();return{loaderClasses:X(()=>({[`${t}--loading`]:e.loading}))}}function ph(e,t){let{slots:n}=t;return N("div",{class:de(`${e.name}__loader`)},[n.default?.({color:e.color,isActive:e.active})||R(vh,{absolute:e.absolute,active:e.active,color:e.color,height:"2",indeterminate:!0},null)])}const T_=["static","relative","fixed","absolute","sticky"],_i=te({position:{type:String,validator:e=>T_.includes(e)}},"position");function Si(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:pn();return{positionClasses:X(()=>e.position?`${t}--${e.position}`:void 0)}}function k_(){const e=dt("useRoute");return U(()=>e?.proxy?.$route)}function A_(){return dt("useRouter")?.proxy?.$router}function Gl(e,t){const n=Sp("RouterLink"),r=X(()=>!!(e.href||e.to)),s=U(()=>r?.value||cu(t,"click")||cu(e,"click"));if(typeof n=="string"||!("useLink"in n)){const c=X(()=>e.href);return{isLink:r,isClickable:s,href:c,linkProps:st({href:c})}}const o=n.useLink({to:X(()=>e.to||""),replace:X(()=>e.replace)}),i=U(()=>e.to?o:void 0),a=k_(),l=U(()=>i.value?e.exact?a.value?i.value.isExactActive?.value&&Rs(i.value.route.value.query,a.value.query):i.value.isExactActive?.value??!1:i.value.isActive?.value??!1:!1),u=U(()=>e.to?i.value?.route.value.href:e.href);return{isLink:r,isClickable:s,isActive:l,route:i.value?.route,navigate:i.value?.navigate,href:u,linkProps:st({href:u,"aria-current":X(()=>l.value?"page":void 0)})}}const ql=te({href:String,replace:Boolean,to:[String,Object],exact:Boolean},"router");let ta=!1;function P_(e,t){let n=!1,r,s;Ke&&e?.beforeEach&&(bt(()=>{window.addEventListener("popstate",o),r=e.beforeEach((i,a,l)=>{ta?n?t(l):l():setTimeout(()=>n?t(l):l()),ta=!0}),s=e?.afterEach(()=>{ta=!1})}),St(()=>{window.removeEventListener("popstate",o),r?.(),s?.()}));function o(i){i.state?.replaced||(n=!0,setTimeout(()=>n=!1))}}function I_(e,t){le(()=>e.isActive?.value,n=>{e.isLink.value&&n!=null&&t&&bt(()=>{t(n)})},{immediate:!0})}const Ma=Symbol("rippleStop"),O_=80;function Ru(e,t){e.style.transform=t,e.style.webkitTransform=t}function Va(e){return e.constructor.name==="TouchEvent"}function yh(e){return e.constructor.name==="KeyboardEvent"}const R_=function(e,t){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},r=0,s=0;if(!yh(e)){const f=t.getBoundingClientRect(),d=Va(e)?e.touches[e.touches.length-1]:e;r=d.clientX-f.left,s=d.clientY-f.top}let o=0,i=.3;t._ripple?.circle?(i=.15,o=t.clientWidth/2,o=n.center?o:o+Math.sqrt((r-o)**2+(s-o)**2)/4):o=Math.sqrt(t.clientWidth**2+t.clientHeight**2)/2;const a=`${(t.clientWidth-o*2)/2}px`,l=`${(t.clientHeight-o*2)/2}px`,u=n.center?a:`${r-o}px`,c=n.center?l:`${s-o}px`;return{radius:o,scale:i,x:u,y:c,centerX:a,centerY:l}},zo={show(e,t){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};if(!t?._ripple?.enabled)return;const r=document.createElement("span"),s=document.createElement("span");r.appendChild(s),r.className="v-ripple__container",n.class&&(r.className+=` ${n.class}`);const{radius:o,scale:i,x:a,y:l,centerX:u,centerY:c}=R_(e,t,n),f=`${o*2}px`;s.className="v-ripple__animation",s.style.width=f,s.style.height=f,t.appendChild(r);const d=window.getComputedStyle(t);d&&d.position==="static"&&(t.style.position="relative",t.dataset.previousPosition="static"),s.classList.add("v-ripple__animation--enter"),s.classList.add("v-ripple__animation--visible"),Ru(s,`translate(${a}, ${l}) scale3d(${i},${i},${i})`),s.dataset.activated=String(performance.now()),requestAnimationFrame(()=>{requestAnimationFrame(()=>{s.classList.remove("v-ripple__animation--enter"),s.classList.add("v-ripple__animation--in"),Ru(s,`translate(${u}, ${c}) scale3d(1,1,1)`)})})},hide(e){if(!e?._ripple?.enabled)return;const t=e.getElementsByClassName("v-ripple__animation");if(t.length===0)return;const n=t[t.length-1];if(n.dataset.isHiding)return;n.dataset.isHiding="true";const r=performance.now()-Number(n.dataset.activated),s=Math.max(250-r,0);setTimeout(()=>{n.classList.remove("v-ripple__animation--in"),n.classList.add("v-ripple__animation--out"),setTimeout(()=>{e.getElementsByClassName("v-ripple__animation").length===1&&e.dataset.previousPosition&&(e.style.position=e.dataset.previousPosition,delete e.dataset.previousPosition),n.parentNode?.parentNode===e&&e.removeChild(n.parentNode)},300)},s)}};function bh(e){return typeof e>"u"||!!e}function Ds(e){const t={},n=e.currentTarget;if(!(!n?._ripple||n._ripple.touched||e[Ma])){if(e[Ma]=!0,Va(e))n._ripple.touched=!0,n._ripple.isTouch=!0;else if(n._ripple.isTouch)return;if(t.center=n._ripple.centered||yh(e),n._ripple.class&&(t.class=n._ripple.class),Va(e)){if(n._ripple.showTimerCommit)return;n._ripple.showTimerCommit=()=>{zo.show(e,n,t)},n._ripple.showTimer=window.setTimeout(()=>{n?._ripple?.showTimerCommit&&(n._ripple.showTimerCommit(),n._ripple.showTimerCommit=null)},O_)}else zo.show(e,n,t)}}function Lu(e){e[Ma]=!0}function Lt(e){const t=e.currentTarget;if(t?._ripple){if(window.clearTimeout(t._ripple.showTimer),e.type==="touchend"&&t._ripple.showTimerCommit){t._ripple.showTimerCommit(),t._ripple.showTimerCommit=null,t._ripple.showTimer=window.setTimeout(()=>{Lt(e)});return}window.setTimeout(()=>{t._ripple&&(t._ripple.touched=!1)}),zo.hide(t)}}function _h(e){const t=e.currentTarget;t?._ripple&&(t._ripple.showTimerCommit&&(t._ripple.showTimerCommit=null),window.clearTimeout(t._ripple.showTimer))}let Fs=!1;function Nu(e,t){!Fs&&t.includes(e.keyCode)&&(Fs=!0,Ds(e))}function Sh(e){Fs=!1,Lt(e)}function wh(e){Fs&&(Fs=!1,Lt(e))}function Eh(e,t,n){const{value:r,modifiers:s}=t,o=bh(r);o||zo.hide(e),e._ripple=e._ripple??{},e._ripple.enabled=o,e._ripple.centered=s.center,e._ripple.circle=s.circle;const i=Oa(r)?r:{};i.class&&(e._ripple.class=i.class);const a=i.keys??[Ra.enter,Ra.space];if(e._ripple.keyDownHandler=l=>Nu(l,a),o&&!n){if(s.stop){e.addEventListener("touchstart",Lu,{passive:!0}),e.addEventListener("mousedown",Lu);return}e.addEventListener("touchstart",Ds,{passive:!0}),e.addEventListener("touchend",Lt,{passive:!0}),e.addEventListener("touchmove",_h,{passive:!0}),e.addEventListener("touchcancel",Lt),e.addEventListener("mousedown",Ds),e.addEventListener("mouseup",Lt),e.addEventListener("mouseleave",Lt),e.addEventListener("keydown",l=>Nu(l,a)),e.addEventListener("keyup",Sh),e.addEventListener("blur",wh),e.addEventListener("dragstart",Lt,{passive:!0})}else!o&&n&&Ch(e)}function Ch(e){e.removeEventListener("mousedown",Ds),e.removeEventListener("touchstart",Ds),e.removeEventListener("touchend",Lt),e.removeEventListener("touchmove",_h),e.removeEventListener("touchcancel",Lt),e.removeEventListener("mouseup",Lt),e.removeEventListener("mouseleave",Lt),e._ripple?.keyDownHandler&&e.removeEventListener("keydown",e._ripple.keyDownHandler),e.removeEventListener("keyup",Sh),e.removeEventListener("dragstart",Lt),e.removeEventListener("blur",wh)}function L_(e,t){Eh(e,t,!1)}function N_(e){Ch(e),delete e._ripple}function D_(e,t){if(t.value===t.oldValue)return;const n=bh(t.oldValue);Eh(e,t,n)}const Tr={mounted:L_,unmounted:N_,updated:D_},F_=te({active:{type:Boolean,default:void 0},activeColor:String,baseColor:String,symbol:{type:null,default:uh},flat:Boolean,icon:[Boolean,String,Function,Object],prependIcon:it,appendIcon:it,block:Boolean,readonly:Boolean,slim:Boolean,stacked:Boolean,ripple:{type:[Boolean,Object],default:!0},text:{type:[String,Number,Boolean],default:void 0},...Xn(),...Ie(),...Zn(),...tr(),...Qn(),...ih(),...zl(),...Zs(),..._i(),...$t(),...ql(),...pi(),...gt({tag:"button"}),...ct(),...Or({variant:"elevated"})},"VBtn"),Gn=ge()({name:"VBtn",props:F_(),emits:{"group:selected":e=>!0},setup(e,t){let{attrs:n,slots:r}=t;const{themeClasses:s}=ht(e),{borderClasses:o}=Jn(e),{densityClasses:i}=Ir(e),{dimensionStyles:a}=nr(e),{elevationClasses:l}=er(e),{loaderClasses:u}=Kl(e),{locationStyles:c}=bi(e),{positionClasses:f}=Si(e),{roundedClasses:d}=Bt(e),{sizeClasses:m,sizeStyles:g}=yi(e),p=ah(e,e.symbol,!1),w=Gl(e,n),h=U(()=>e.active!==void 0?e.active:w.isLink.value?w.isActive?.value:p?.isSelected.value),v=X(()=>h.value?e.activeColor??e.color:e.color),b=U(()=>({color:p?.isSelected.value&&(!w.isLink.value||w.isActive?.value)||!p||w.isActive?.value?v.value??e.baseColor:e.baseColor,variant:e.variant})),{colorClasses:y,colorStyles:E,variantClasses:P}=rs(b),k=U(()=>p?.disabled.value||e.disabled),T=X(()=>e.variant==="elevated"&&!(e.disabled||e.flat||e.border)),A=U(()=>{if(!(e.value===void 0||typeof e.value=="symbol"))return Object(e.value)===e.value?JSON.stringify(e.value,null,0):e.value});function H(V){k.value||w.isLink.value&&(V.metaKey||V.ctrlKey||V.shiftKey||V.button!==0||n.target==="_blank")||(w.navigate?.(V),p?.toggle())}return I_(w,p?.select),ye(()=>{const V=w.isLink.value?"a":e.tag,D=!!(e.prependIcon||r.prepend),G=!!(e.appendIcon||r.append),ee=!!(e.icon&&e.icon!==!0);return Kt(R(V,Pe({type:V==="a"?void 0:"button",class:["v-btn",p?.selectedClass.value,{"v-btn--active":h.value,"v-btn--block":e.block,"v-btn--disabled":k.value,"v-btn--elevated":T.value,"v-btn--flat":e.flat,"v-btn--icon":!!e.icon,"v-btn--loading":e.loading,"v-btn--readonly":e.readonly,"v-btn--slim":e.slim,"v-btn--stacked":e.stacked},s.value,o.value,y.value,i.value,l.value,u.value,f.value,d.value,m.value,P.value,e.class],style:[E.value,a.value,c.value,g.value,e.style],"aria-busy":e.loading?!0:void 0,disabled:k.value||void 0,tabindex:e.loading||e.readonly?-1:void 0,onClick:H,value:A.value},w.linkProps),{default:()=>[ns(!0,"v-btn"),!e.icon&&D&&N("span",{key:"prepend",class:"v-btn__prepend"},[r.prepend?R(Ye,{key:"prepend-defaults",disabled:!e.prependIcon,defaults:{VIcon:{icon:e.prependIcon}}},r.prepend):R(_t,{key:"prepend-icon",icon:e.prependIcon},null)]),N("span",{class:"v-btn__content","data-no-activator":""},[!r.default&&ee?R(_t,{key:"content-icon",icon:e.icon},null):R(Ye,{key:"content-defaults",disabled:!ee,defaults:{VIcon:{icon:e.icon}}},{default:()=>[r.default?.()??tt(e.text)]})]),!e.icon&&G&&N("span",{key:"append",class:"v-btn__append"},[r.append?R(Ye,{key:"append-defaults",disabled:!e.appendIcon,defaults:{VIcon:{icon:e.appendIcon}}},r.append):R(_t,{key:"append-icon",icon:e.appendIcon},null)]),!!e.loading&&N("span",{key:"loader",class:"v-btn__loader"},[r.loader?.()??R(gh,{color:typeof e.loading=="boolean"?void 0:e.loading,indeterminate:!0,width:"2"},null)])]}),[[Tr,!k.value&&e.ripple,"",{center:!!e.icon}]])}),{group:p}}});function na(e,t){return{x:e.x+t.x,y:e.y+t.y}}function M_(e,t){return{x:e.x-t.x,y:e.y-t.y}}function Du(e,t){if(e.side==="top"||e.side==="bottom"){const{side:n,align:r}=e,s=r==="left"?0:r==="center"?t.width/2:r==="right"?t.width:r,o=n==="top"?0:n==="bottom"?t.height:n;return na({x:s,y:o},t)}else if(e.side==="left"||e.side==="right"){const{side:n,align:r}=e,s=n==="left"?0:n==="right"?t.width:n,o=r==="top"?0:r==="center"?t.height/2:r==="bottom"?t.height:r;return na({x:s,y:o},t)}return na({x:t.width/2,y:t.height/2},t)}const xh={static:B_,connected:H_},V_=te({locationStrategy:{type:[String,Function],default:"static",validator:e=>typeof e=="function"||e in xh},location:{type:String,default:"bottom"},origin:{type:String,default:"auto"},offset:[Number,String,Array]},"VOverlay-location-strategies");function $_(e,t){const n=J({}),r=J();Ke&&Cr(()=>!!(t.isActive.value&&e.locationStrategy),a=>{le(()=>e.locationStrategy,a),St(()=>{window.removeEventListener("resize",s),visualViewport?.removeEventListener("resize",o),visualViewport?.removeEventListener("scroll",i),r.value=void 0}),window.addEventListener("resize",s,{passive:!0}),visualViewport?.addEventListener("resize",o,{passive:!0}),visualViewport?.addEventListener("scroll",i,{passive:!0}),typeof e.locationStrategy=="function"?r.value=e.locationStrategy(t,e,n)?.updateLocation:r.value=xh[e.locationStrategy](t,e,n)?.updateLocation});function s(a){r.value?.(a)}function o(a){r.value?.(a)}function i(a){r.value?.(a)}return{contentStyles:n,updateLocation:r}}function B_(){}function U_(e,t){const n=Vl(e);return t?n.x+=parseFloat(e.style.right||0):n.x-=parseFloat(e.style.left||0),n.y-=parseFloat(e.style.top||0),n}function H_(e,t,n){(Array.isArray(e.target.value)||jb(e.target.value))&&Object.assign(n.value,{position:"fixed",top:0,[e.isRtl.value?"right":"left"]:0});const{preferredAnchor:s,preferredOrigin:o}=Ml(()=>{const h=Na(t.location,e.isRtl.value),v=t.origin==="overlap"?h:t.origin==="auto"?Zi(h):Na(t.origin,e.isRtl.value);return h.side===v.side&&h.align===Qi(v).align?{preferredAnchor:fu(h),preferredOrigin:fu(v)}:{preferredAnchor:h,preferredOrigin:v}}),[i,a,l,u]=["minWidth","minHeight","maxWidth","maxHeight"].map(h=>U(()=>{const v=parseFloat(t[h]);return isNaN(v)?1/0:v})),c=U(()=>{if(Array.isArray(t.offset))return t.offset;if(typeof t.offset=="string"){const h=t.offset.split(" ").map(parseFloat);return h.length<2&&h.push(0),h}return typeof t.offset=="number"?[t.offset,0]:[0,0]});let f=!1,d=-1;const m=new ob(4),g=new ResizeObserver(()=>{if(!f)return;if(requestAnimationFrame(v=>{v!==d&&m.clear(),requestAnimationFrame(b=>{d=b})}),m.isFull){const v=m.values();if(Rs(v.at(-1),v.at(-3))&&!Rs(v.at(-1),v.at(-2)))return}const h=w();h&&m.push(h.flipped)});le([e.target,e.contentEl],(h,v)=>{let[b,y]=h,[E,P]=v;E&&!Array.isArray(E)&&g.unobserve(E),b&&!Array.isArray(b)&&g.observe(b),P&&g.unobserve(P),y&&g.observe(y)},{immediate:!0}),St(()=>{g.disconnect()});let p=new dn({x:0,y:0,width:0,height:0});function w(){if(f=!1,requestAnimationFrame(()=>f=!0),!e.target.value||!e.contentEl.value)return;(Array.isArray(e.target.value)||e.target.value.offsetParent||e.target.value.getClientRects().length)&&(p=zm(e.target.value));const h=U_(e.contentEl.value,e.isRtl.value),v=Ho(e.contentEl.value),b=12;v.length||(v.push(document.documentElement),e.contentEl.value.style.top&&e.contentEl.value.style.left||(h.x-=parseFloat(document.documentElement.style.getPropertyValue("--v-body-scroll-x")||0),h.y-=parseFloat(document.documentElement.style.getPropertyValue("--v-body-scroll-y")||0)));const y=v.reduce((G,ee)=>{const ne=fb(ee);return G?new dn({x:Math.max(G.left,ne.left),y:Math.max(G.top,ne.top),width:Math.min(G.right,ne.right)-Math.max(G.left,ne.left),height:Math.min(G.bottom,ne.bottom)-Math.max(G.top,ne.top)}):ne},void 0);y.x+=b,y.y+=b,y.width-=b*2,y.height-=b*2;let E={anchor:s.value,origin:o.value};function P(G){const ee=new dn(h),ne=Du(G.anchor,p),oe=Du(G.origin,ee);let{x:Z,y:re}=M_(ne,oe);switch(G.anchor.side){case"top":re-=c.value[0];break;case"bottom":re+=c.value[0];break;case"left":Z-=c.value[0];break;case"right":Z+=c.value[0];break}switch(G.anchor.align){case"top":re-=c.value[1];break;case"bottom":re+=c.value[1];break;case"left":Z-=c.value[1];break;case"right":Z+=c.value[1];break}return ee.x+=Z,ee.y+=re,ee.width=Math.min(ee.width,l.value),ee.height=Math.min(ee.height,u.value),{overflows:mu(ee,y),x:Z,y:re}}let k=0,T=0;const A={x:0,y:0},H={x:!1,y:!1};let V=-1;for(;!(V++>10);){const{x:G,y:ee,overflows:ne}=P(E);k+=G,T+=ee,h.x+=G,h.y+=ee;{const oe=du(E.anchor),Z=ne.x.before||ne.x.after,re=ne.y.before||ne.y.after;let pe=!1;if(["x","y"].forEach(ue=>{if(ue==="x"&&Z&&!H.x||ue==="y"&&re&&!H.y){const xe={anchor:{...E.anchor},origin:{...E.origin}},_e=ue==="x"?oe==="y"?Qi:Zi:oe==="y"?Zi:Qi;xe.anchor=_e(xe.anchor),xe.origin=_e(xe.origin);const{overflows:Me}=P(xe);(Me[ue].before<=ne[ue].before&&Me[ue].after<=ne[ue].after||Me[ue].before+Me[ue].after<(ne[ue].before+ne[ue].after)/2)&&(E=xe,pe=H[ue]=!0)}}),pe)continue}ne.x.before&&(k+=ne.x.before,h.x+=ne.x.before),ne.x.after&&(k-=ne.x.after,h.x-=ne.x.after),ne.y.before&&(T+=ne.y.before,h.y+=ne.y.before),ne.y.after&&(T-=ne.y.after,h.y-=ne.y.after);{const oe=mu(h,y);A.x=y.width-oe.x.before-oe.x.after,A.y=y.height-oe.y.before-oe.y.after,k+=oe.x.before,h.x+=oe.x.before,T+=oe.y.before,h.y+=oe.y.before}break}const D=du(E.anchor);return Object.assign(n.value,{"--v-overlay-anchor-origin":`${E.anchor.side} ${E.anchor.align}`,transformOrigin:`${E.origin.side} ${E.origin.align}`,top:me(ra(T)),left:e.isRtl.value?void 0:me(ra(k)),right:e.isRtl.value?me(ra(-k)):void 0,minWidth:me(D==="y"?Math.min(i.value,p.width):i.value),maxWidth:me(Fu(Er(A.x,i.value===1/0?0:i.value,l.value))),maxHeight:me(Fu(Er(A.y,a.value===1/0?0:a.value,u.value)))}),{available:A,contentBox:h,flipped:H}}return le(()=>[s.value,o.value,t.offset,t.minWidth,t.minHeight,t.maxWidth,t.maxHeight],()=>w()),bt(()=>{const h=w();if(!h)return;const{available:v,contentBox:b}=h;b.height>v.y&&requestAnimationFrame(()=>{w(),requestAnimationFrame(()=>{w()})})}),{updateLocation:w}}function ra(e){return Math.round(e*devicePixelRatio)/devicePixelRatio}function Fu(e){return Math.ceil(e*devicePixelRatio)/devicePixelRatio}let $a=!0;const Ko=[];function j_(e){!$a||Ko.length?(Ko.push(e),Ba()):($a=!1,e(),Ba())}let Mu=-1;function Ba(){cancelAnimationFrame(Mu),Mu=requestAnimationFrame(()=>{const e=Ko.shift();e&&e(),Ko.length?Ba():$a=!0})}const Th={none:null,close:K_,block:G_,reposition:q_},W_=te({scrollStrategy:{type:[String,Function],default:"block",validator:e=>typeof e=="function"||e in Th}},"VOverlay-scroll-strategies");function z_(e,t){if(!Ke)return;let n;en(async()=>{n?.stop(),t.isActive.value&&e.scrollStrategy&&(n=Wn(),await new Promise(r=>setTimeout(r)),n.active&&n.run(()=>{typeof e.scrollStrategy=="function"?e.scrollStrategy(t,e,n):Th[e.scrollStrategy]?.(t,e,n)}))}),St(()=>{n?.stop()})}function K_(e){function t(n){e.isActive.value=!1}kh(e.targetEl.value??e.contentEl.value,t)}function G_(e,t){const n=e.root.value?.offsetParent,r=[...new Set([...Ho(e.targetEl.value,t.contained?n:void 0),...Ho(e.contentEl.value,t.contained?n:void 0)])].filter(i=>!i.classList.contains("v-overlay-scroll-blocked")),s=window.innerWidth-document.documentElement.offsetWidth,o=(i=>Hl(i)&&i)(n||document.documentElement);o&&e.root.value.classList.add("v-overlay--scroll-blocked"),r.forEach((i,a)=>{i.style.setProperty("--v-body-scroll-x",me(-i.scrollLeft)),i.style.setProperty("--v-body-scroll-y",me(-i.scrollTop)),i!==document.documentElement&&i.style.setProperty("--v-scrollbar-offset",me(s)),i.classList.add("v-overlay-scroll-blocked")}),St(()=>{r.forEach((i,a)=>{const l=parseFloat(i.style.getPropertyValue("--v-body-scroll-x")),u=parseFloat(i.style.getPropertyValue("--v-body-scroll-y")),c=i.style.scrollBehavior;i.style.scrollBehavior="auto",i.style.removeProperty("--v-body-scroll-x"),i.style.removeProperty("--v-body-scroll-y"),i.style.removeProperty("--v-scrollbar-offset"),i.classList.remove("v-overlay-scroll-blocked"),i.scrollLeft=-l,i.scrollTop=-u,i.style.scrollBehavior=c}),o&&e.root.value.classList.remove("v-overlay--scroll-blocked")})}function q_(e,t,n){let r=!1,s=-1,o=-1;function i(a){j_(()=>{const l=performance.now();e.updateLocation.value?.(a),r=(performance.now()-l)/(1e3/60)>2})}o=(typeof requestIdleCallback>"u"?a=>a():requestIdleCallback)(()=>{n.run(()=>{kh(e.targetEl.value??e.contentEl.value,a=>{r?(cancelAnimationFrame(s),s=requestAnimationFrame(()=>{s=requestAnimationFrame(()=>{i(a)})})):i(a)})})}),St(()=>{typeof cancelIdleCallback<"u"&&cancelIdleCallback(o),cancelAnimationFrame(s)})}function kh(e,t){const n=[document,...Ho(e)];n.forEach(r=>{r.addEventListener("scroll",t,{passive:!0})}),St(()=>{n.forEach(r=>{r.removeEventListener("scroll",t)})})}const Y_=Symbol.for("vuetify:v-menu"),Ah=te({closeDelay:[Number,String],openDelay:[Number,String]},"delay");function X_(e,t){let n=()=>{};function r(i){n?.();const a=Number(i?e.openDelay:e.closeDelay);return new Promise(l=>{n=lb(a,()=>{t?.(i),l(i)})})}function s(){return r(!0)}function o(){return r(!1)}return{clearDelay:n,runOpenDelay:s,runCloseDelay:o}}const J_=te({target:[String,Object],activator:[String,Object],activatorProps:{type:Object,default:()=>({})},openOnClick:{type:Boolean,default:void 0},openOnHover:Boolean,openOnFocus:{type:Boolean,default:void 0},closeOnContentClick:Boolean,...Ah()},"VOverlay-activator");function Z_(e,t){let{isActive:n,isTop:r,contentEl:s}=t;const o=dt("useActivator"),i=J();let a=!1,l=!1,u=!0;const c=U(()=>e.openOnFocus||e.openOnFocus==null&&e.openOnHover),f=U(()=>e.openOnClick||e.openOnClick==null&&!e.openOnHover&&!c.value),{runOpenDelay:d,runCloseDelay:m}=X_(e,T=>{T===(e.openOnHover&&a||c.value&&l)&&!(e.openOnHover&&n.value&&!r.value)&&(n.value!==T&&(u=!0),n.value=T)}),g=J(),p={onClick:T=>{T.stopPropagation(),i.value=T.currentTarget||T.target,n.value||(g.value=[T.clientX,T.clientY]),n.value=!n.value},onMouseenter:T=>{T.sourceCapabilities?.firesTouchEvents||(a=!0,i.value=T.currentTarget||T.target,d())},onMouseleave:T=>{a=!1,m()},onFocus:T=>{ab(T.target,":focus-visible")!==!1&&(l=!0,T.stopPropagation(),i.value=T.currentTarget||T.target,d())},onBlur:T=>{l=!1,T.stopPropagation(),m()}},w=U(()=>{const T={};return f.value&&(T.onClick=p.onClick),e.openOnHover&&(T.onMouseenter=p.onMouseenter,T.onMouseleave=p.onMouseleave),c.value&&(T.onFocus=p.onFocus,T.onBlur=p.onBlur),T}),h=U(()=>{const T={};if(e.openOnHover&&(T.onMouseenter=()=>{a=!0,d()},T.onMouseleave=()=>{a=!1,m()}),c.value&&(T.onFocusin=()=>{l=!0,d()},T.onFocusout=()=>{l=!1,m()}),e.closeOnContentClick){const A=Le(Y_,null);T.onClick=()=>{n.value=!1,A?.closeParents()}}return T}),v=U(()=>{const T={};return e.openOnHover&&(T.onMouseenter=()=>{u&&(a=!0,u=!1,d())},T.onMouseleave=()=>{a=!1,m()}),T});le(r,T=>{T&&(e.openOnHover&&!a&&(!c.value||!l)||c.value&&!l&&(!e.openOnHover||!a))&&!s.value?.contains(document.activeElement)&&(n.value=!1)}),le(n,T=>{T||setTimeout(()=>{g.value=void 0})},{flush:"post"});const b=La();en(()=>{b.value&&bt(()=>{i.value=b.el})});const y=La(),E=U(()=>e.target==="cursor"&&g.value?g.value:y.value?y.el:Ph(e.target,o)||i.value),P=U(()=>Array.isArray(E.value)?void 0:E.value);let k;return le(()=>!!e.activator,T=>{T&&Ke?(k=Wn(),k.run(()=>{Q_(e,o,{activatorEl:i,activatorEvents:w})})):k&&k.stop()},{flush:"post",immediate:!0}),St(()=>{k?.stop()}),{activatorEl:i,activatorRef:b,target:E,targetEl:P,targetRef:y,activatorEvents:w,contentEvents:h,scrimEvents:v}}function Q_(e,t,n){let{activatorEl:r,activatorEvents:s}=n;le(()=>e.activator,(l,u)=>{if(u&&l!==u){const c=a(u);c&&i(c)}l&&bt(()=>o())},{immediate:!0}),le(()=>e.activatorProps,()=>{o()}),St(()=>{i()});function o(){let l=arguments.length>0&&arguments[0]!==void 0?arguments[0]:a(),u=arguments.length>1&&arguments[1]!==void 0?arguments[1]:e.activatorProps;l&&db(l,Pe(s.value,u))}function i(){let l=arguments.length>0&&arguments[0]!==void 0?arguments[0]:a(),u=arguments.length>1&&arguments[1]!==void 0?arguments[1]:e.activatorProps;l&&mb(l,Pe(s.value,u))}function a(){let l=arguments.length>0&&arguments[0]!==void 0?arguments[0]:e.activator;const u=Ph(l,t);return r.value=u?.nodeType===Node.ELEMENT_NODE?u:void 0,r.value}}function Ph(e,t){if(!e)return;let n;if(e==="parent"){let r=t?.proxy?.$el?.parentNode;for(;r?.hasAttribute("data-no-activator");)r=r.parentNode;n=r}else typeof e=="string"?n=document.querySelector(e):"$el"in e?n=e.$el:n=e;return n}const Ua=Symbol.for("vuetify:display"),Vu={mobileBreakpoint:"lg",thresholds:{xs:0,sm:600,md:960,lg:1280,xl:1920,xxl:2560}},e0=function(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:Vu;return Dt(Vu,e)};function $u(e){return Ke&&!e?window.innerWidth:typeof e=="object"&&e.clientWidth||0}function Bu(e){return Ke&&!e?window.innerHeight:typeof e=="object"&&e.clientHeight||0}function Uu(e){const t=Ke&&!e?window.navigator.userAgent:"ssr";function n(g){return!!t.match(g)}const r=n(/android/i),s=n(/iphone|ipad|ipod/i),o=n(/cordova/i),i=n(/electron/i),a=n(/chrome/i),l=n(/edge/i),u=n(/firefox/i),c=n(/opera/i),f=n(/win/i),d=n(/mac/i),m=n(/linux/i);return{android:r,ios:s,cordova:o,electron:i,chrome:a,edge:l,firefox:u,opera:c,win:f,mac:d,linux:m,touch:Zy,ssr:t==="ssr"}}function t0(e,t){const{thresholds:n,mobileBreakpoint:r}=e0(e),s=Ae(Bu(t)),o=Ae(Uu(t)),i=st({}),a=Ae($u(t));function l(){s.value=Bu(),a.value=$u()}function u(){l(),o.value=Uu()}return en(()=>{const c=a.value<n.sm,f=a.value<n.md&&!c,d=a.value<n.lg&&!(f||c),m=a.value<n.xl&&!(d||f||c),g=a.value<n.xxl&&!(m||d||f||c),p=a.value>=n.xxl,w=c?"xs":f?"sm":d?"md":m?"lg":g?"xl":"xxl",h=typeof r=="number"?r:n[r],v=a.value<h;i.xs=c,i.sm=f,i.md=d,i.lg=m,i.xl=g,i.xxl=p,i.smAndUp=!c,i.mdAndUp=!(c||f),i.lgAndUp=!(c||f||d),i.xlAndUp=!(c||f||d||m),i.smAndDown=!(d||m||g||p),i.mdAndDown=!(m||g||p),i.lgAndDown=!(g||p),i.xlAndDown=!p,i.name=w,i.height=s.value,i.width=a.value,i.mobile=v,i.mobileBreakpoint=r,i.platform=o.value,i.thresholds=n}),Ke&&(window.addEventListener("resize",l,{passive:!0}),St(()=>{window.removeEventListener("resize",l)},!0)),{...Cl(i),update:u,ssr:!!t}}function n0(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{mobile:null},t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:pn();const n=Le(Ua);if(!n)throw new Error("Could not find Vuetify display injection");const r=U(()=>e.mobile?!0:typeof e.mobileBreakpoint=="number"?n.width.value<e.mobileBreakpoint:e.mobileBreakpoint?n.width.value<n.thresholds.value[e.mobileBreakpoint]:e.mobile===null?n.mobile.value:!1),s=X(()=>t?{[`${t}--mobile`]:r.value}:{});return{...n,displayClasses:s,mobile:r}}function r0(){if(!Ke)return Ae(!1);const{ssr:e}=n0();if(e){const t=Ae(!1);return Gt(()=>{t.value=!0}),t}else return Ae(!0)}const Ih=te({eager:Boolean},"lazy");function Oh(e,t){const n=Ae(!1),r=X(()=>n.value||e.eager||t.value);le(t,()=>n.value=!0);function s(){e.eager||(n.value=!1)}return{isBooted:n,hasContent:r,onAfterLeave:s}}function wi(){const t=dt("useScopeId").vnode.scopeId;return{scopeId:t?{[t]:""}:void 0}}const Hu=Symbol.for("vuetify:stack"),us=st([]);function s0(e,t,n){const r=dt("useStack"),s=!n,o=Le(Hu,void 0),i=st({activeChildren:new Set});Ft(Hu,i);const a=Ae(Number(gn(t)));Cr(e,()=>{const c=us.at(-1)?.[1];a.value=c?c+10:Number(gn(t)),s&&us.push([r.uid,a.value]),o?.activeChildren.add(r.uid),St(()=>{if(s){const f=Ee(us).findIndex(d=>d[0]===r.uid);us.splice(f,1)}o?.activeChildren.delete(r.uid)})});const l=Ae(!0);s&&en(()=>{const c=us.at(-1)?.[0]===r.uid;setTimeout(()=>l.value=c)});const u=X(()=>!i.activeChildren.size);return{globalTop:oi(l),localTop:u,stackStyles:X(()=>({zIndex:a.value}))}}function o0(e){return{teleportTarget:U(()=>{const n=e();if(n===!0||!Ke)return;const r=n===!1?document.body:typeof n=="string"?document.querySelector(n):n;if(r==null)return;let s=[...r.children].find(o=>o.matches(".v-overlay-container"));return s||(s=document.createElement("div"),s.className="v-overlay-container",r.appendChild(s)),s})}}const Ei=te({transition:{type:null,default:"fade-transition",validator:e=>e!==!0}},"transition"),mr=(e,t)=>{let{slots:n}=t;const{transition:r,disabled:s,group:o,...i}=e,{component:a=o?Nl:wr,...l}=Oa(r)?r:{};let u;return Oa(r)?u=Pe(l,cb({disabled:s,group:o}),i):u=Pe({name:s||!r?"":r},i),vn(a,u,n)};function i0(){return!0}function Rh(e,t,n){if(!e||Lh(e,n)===!1)return!1;const r=Zm(t);if(typeof ShadowRoot<"u"&&r instanceof ShadowRoot&&r.host===e.target)return!1;const s=(typeof n.value=="object"&&n.value.include||(()=>[]))();return s.push(t),!s.some(o=>o?.contains(e.target))}function Lh(e,t){return(typeof t.value=="object"&&t.value.closeConditional||i0)(e)}function a0(e,t,n){const r=typeof n.value=="function"?n.value:n.value.handler;e.shadowTarget=e.target,t._clickOutside.lastMousedownWasOutside&&Rh(e,t,n)&&setTimeout(()=>{Lh(e,n)&&r&&r(e)},0)}function ju(e,t){const n=Zm(e);t(document),typeof ShadowRoot<"u"&&n instanceof ShadowRoot&&t(n)}const Wu={mounted(e,t){const n=s=>a0(s,e,t),r=s=>{e._clickOutside.lastMousedownWasOutside=Rh(s,e,t)};ju(e,s=>{s.addEventListener("click",n,!0),s.addEventListener("mousedown",r,!0)}),e._clickOutside||(e._clickOutside={lastMousedownWasOutside:!1}),e._clickOutside[t.instance.$.uid]={onClick:n,onMousedown:r}},beforeUnmount(e,t){e._clickOutside&&(ju(e,n=>{if(!n||!e._clickOutside?.[t.instance.$.uid])return;const{onClick:r,onMousedown:s}=e._clickOutside[t.instance.$.uid];n.removeEventListener("click",r,!0),n.removeEventListener("mousedown",s,!0)}),delete e._clickOutside[t.instance.$.uid])}};function l0(e){const{modelValue:t,color:n,...r}=e;return R(wr,{name:"fade-transition",appear:!0},{default:()=>[e.modelValue&&N("div",Pe({class:["v-overlay__scrim",e.color.backgroundColorClasses.value],style:e.color.backgroundColorStyles.value},r),null)]})}const Ci=te({absolute:Boolean,attach:[Boolean,String,Object],closeOnBack:{type:Boolean,default:!0},contained:Boolean,contentClass:null,contentProps:null,disabled:Boolean,opacity:[Number,String],noClickAnimation:Boolean,modelValue:Boolean,persistent:Boolean,scrim:{type:[Boolean,String],default:!0},zIndex:{type:[Number,String],default:2e3},...J_(),...Ie(),...tr(),...Ih(),...V_(),...W_(),...ct(),...Ei()},"VOverlay"),kr=ge()({name:"VOverlay",directives:{vClickOutside:Wu},inheritAttrs:!1,props:{_disableGlobalStack:Boolean,...Ci()},emits:{"click:outside":e=>!0,"update:modelValue":e=>!0,keydown:e=>!0,afterEnter:()=>!0,afterLeave:()=>!0},setup(e,t){let{slots:n,attrs:r,emit:s}=t;const o=dt("VOverlay"),i=J(),a=J(),l=J(),u=Vt(e,"modelValue"),c=U({get:()=>u.value,set:F=>{F&&e.disabled||(u.value=F)}}),{themeClasses:f}=ht(e),{rtlClasses:d,isRtl:m}=Pr(),{hasContent:g,onAfterLeave:p}=Oh(e,c),w=mn(()=>typeof e.scrim=="string"?e.scrim:null),{globalTop:h,localTop:v,stackStyles:b}=s0(c,()=>e.zIndex,e._disableGlobalStack),{activatorEl:y,activatorRef:E,target:P,targetEl:k,targetRef:T,activatorEvents:A,contentEvents:H,scrimEvents:V}=Z_(e,{isActive:c,isTop:v,contentEl:l}),{teleportTarget:D}=o0(()=>{const F=e.attach||e.contained;if(F)return F;const q=y?.value?.getRootNode()||o.proxy?.$el?.getRootNode();return q instanceof ShadowRoot?q:!1}),{dimensionStyles:G}=nr(e),ee=r0(),{scopeId:ne}=wi();le(()=>e.disabled,F=>{F&&(c.value=!1)});const{contentStyles:oe,updateLocation:Z}=$_(e,{isRtl:m,contentEl:l,target:P,isActive:c});z_(e,{root:i,contentEl:l,targetEl:k,isActive:c,updateLocation:Z});function re(F){s("click:outside",F),e.persistent?Ge():c.value=!1}function pe(F){return c.value&&h.value&&(!e.scrim||F.target===a.value||F instanceof MouseEvent&&F.shadowTarget===a.value)}Ke&&le(c,F=>{F?window.addEventListener("keydown",ue):window.removeEventListener("keydown",ue)},{immediate:!0}),Qt(()=>{Ke&&window.removeEventListener("keydown",ue)});function ue(F){F.key==="Escape"&&h.value&&(l.value?.contains(document.activeElement)||s("keydown",F),e.persistent?Ge():(c.value=!1,l.value?.contains(document.activeElement)&&y.value?.focus()))}function xe(F){F.key==="Escape"&&!h.value||s("keydown",F)}const _e=A_();Cr(()=>e.closeOnBack,()=>{P_(_e,F=>{h.value&&c.value?(F(!1),e.persistent?Ge():c.value=!1):F()})});const Me=J();le(()=>c.value&&(e.absolute||e.contained)&&D.value==null,F=>{if(F){const q=Ub(i.value);q&&q!==document.scrollingElement&&(Me.value=q.scrollTop)}});function Ge(){e.noClickAnimation||l.value&&Hr(l.value,[{transformOrigin:"center"},{transform:"scale(1.03)"},{transformOrigin:"center"}],{duration:150,easing:Uo})}function Se(){s("afterEnter")}function Ne(){p(),s("afterLeave")}return ye(()=>N(ke,null,[n.activator?.({isActive:c.value,targetRef:T,props:Pe({ref:E},A.value,e.activatorProps)}),ee.value&&g.value&&R(cp,{disabled:!D.value,to:D.value},{default:()=>[N("div",Pe({class:["v-overlay",{"v-overlay--absolute":e.absolute||e.contained,"v-overlay--active":c.value,"v-overlay--contained":e.contained},f.value,d.value,e.class],style:[b.value,{"--v-overlay-opacity":e.opacity,top:me(Me.value)},e.style],ref:i,onKeydown:xe},ne,r),[R(l0,Pe({color:w,modelValue:c.value&&!!e.scrim,ref:a},V.value),null),R(mr,{appear:!0,persisted:!0,transition:e.transition,target:P.value,onAfterEnter:Se,onAfterLeave:Ne},{default:()=>[Kt(N("div",Pe({ref:l,class:["v-overlay__content",e.contentClass],style:[G.value,oe.value]},H.value,e.contentProps),[n.default?.({isActive:c})]),[[Ys,c.value],[Wu,{handler:re,closeConditional:pe,include:()=>[y.value]}]])]})])]})])),{activatorEl:y,scrimEl:a,target:P,animateClick:Ge,contentEl:l,globalTop:h,localTop:v,updateLocation:Z}}});function Qs(e){const t=e.slice(-2).toUpperCase();switch(!0){case e==="GB-alt-variant":return{firstDay:0,firstWeekSize:4};case e==="001":return{firstDay:1,firstWeekSize:1};case`AG AS BD BR BS BT BW BZ CA CO DM DO ET GT GU HK HN ID IL IN JM JP KE
    KH KR LA MH MM MO MT MX MZ NI NP PA PE PH PK PR PY SA SG SV TH TT TW UM US
    VE VI WS YE ZA ZW`.includes(t):return{firstDay:0,firstWeekSize:1};case`AI AL AM AR AU AZ BA BM BN BY CL CM CN CR CY EC GE HR KG KZ LB LK LV
    MD ME MK MN MY NZ RO RS SI TJ TM TR UA UY UZ VN XK`.includes(t):return{firstDay:1,firstWeekSize:1};case`AD AN AT AX BE BG CH CZ DE DK EE ES FI FJ FO FR GB GF GP GR HU IE IS
    IT LI LT LU MC MQ NL NO PL RE RU SE SK SM VA`.includes(t):return{firstDay:1,firstWeekSize:4};case"AE AF BH DJ DZ EG IQ IR JO KW LY OM QA SD SY".includes(t):return{firstDay:6,firstWeekSize:1};case t==="MV":return{firstDay:5,firstWeekSize:1};case t==="PT":return{firstDay:0,firstWeekSize:4};default:return null}}function c0(e,t,n){const r=[];let s=[];const o=Nh(e),i=Dh(e),a=n??Qs(t)?.firstDay??0,l=(o.getDay()-a+7)%7,u=(i.getDay()-a+7)%7;for(let c=0;c<l;c++){const f=new Date(o);f.setDate(f.getDate()-(l-c)),s.push(f)}for(let c=1;c<=i.getDate();c++){const f=new Date(e.getFullYear(),e.getMonth(),c);s.push(f),s.length===7&&(r.push(s),s=[])}for(let c=1;c<7-u;c++){const f=new Date(i);f.setDate(f.getDate()+c),s.push(f)}return s.length>0&&r.push(s),r}function Ha(e,t,n){const r=n??Qs(t)?.firstDay??0,s=new Date(e);for(;s.getDay()!==r;)s.setDate(s.getDate()-1);return s}function u0(e,t){const n=new Date(e),r=((Qs(t)?.firstDay??0)+6)%7;for(;n.getDay()!==r;)n.setDate(n.getDate()+1);return n}function Nh(e){return new Date(e.getFullYear(),e.getMonth(),1)}function Dh(e){return new Date(e.getFullYear(),e.getMonth()+1,0)}function f0(e){const t=e.split("-").map(Number);return new Date(t[0],t[1]-1,t[2])}const d0=/^([12]\d{3}-([1-9]|0[1-9]|1[0-2])-([1-9]|0[1-9]|[12]\d|3[01]))$/;function Fh(e){if(e==null)return new Date;if(e instanceof Date)return e;if(typeof e=="string"){let t;if(d0.test(e))return f0(e);if(t=Date.parse(e),!isNaN(t))return new Date(t)}return null}const zu=new Date(2e3,0,2);function m0(e,t,n){const r=t??Qs(e)?.firstDay??0;return Fm(7).map(s=>{const o=new Date(zu);return o.setDate(zu.getDate()+r+s),new Intl.DateTimeFormat(e,{weekday:n??"narrow"}).format(o)})}function h0(e,t,n,r){const s=Fh(e)??new Date,o=r?.[t];if(typeof o=="function")return o(s,t,n);let i={};switch(t){case"fullDate":i={year:"numeric",month:"short",day:"numeric"};break;case"fullDateWithWeekday":i={weekday:"long",year:"numeric",month:"long",day:"numeric"};break;case"normalDate":const a=s.getDate(),l=new Intl.DateTimeFormat(n,{month:"long"}).format(s);return`${a} ${l}`;case"normalDateWithWeekday":i={weekday:"short",day:"numeric",month:"short"};break;case"shortDate":i={month:"short",day:"numeric"};break;case"year":i={year:"numeric"};break;case"month":i={month:"long"};break;case"monthShort":i={month:"short"};break;case"monthAndYear":i={month:"long",year:"numeric"};break;case"monthAndDate":i={month:"long",day:"numeric"};break;case"weekday":i={weekday:"long"};break;case"weekdayShort":i={weekday:"short"};break;case"dayOfMonth":return new Intl.NumberFormat(n).format(s.getDate());case"hours12h":i={hour:"numeric",hour12:!0};break;case"hours24h":i={hour:"numeric",hour12:!1};break;case"minutes":i={minute:"numeric"};break;case"seconds":i={second:"numeric"};break;case"fullTime":i={hour:"numeric",minute:"numeric"};break;case"fullTime12h":i={hour:"numeric",minute:"numeric",hour12:!0};break;case"fullTime24h":i={hour:"numeric",minute:"numeric",hour12:!1};break;case"fullDateTime":i={year:"numeric",month:"short",day:"numeric",hour:"numeric",minute:"numeric"};break;case"fullDateTime12h":i={year:"numeric",month:"short",day:"numeric",hour:"numeric",minute:"numeric",hour12:!0};break;case"fullDateTime24h":i={year:"numeric",month:"short",day:"numeric",hour:"numeric",minute:"numeric",hour12:!1};break;case"keyboardDate":i={year:"numeric",month:"2-digit",day:"2-digit"};break;case"keyboardDateTime":return i={year:"numeric",month:"2-digit",day:"2-digit",hour:"numeric",minute:"numeric"},new Intl.DateTimeFormat(n,i).format(s).replace(/, /g," ");case"keyboardDateTime12h":return i={year:"numeric",month:"2-digit",day:"2-digit",hour:"numeric",minute:"numeric",hour12:!0},new Intl.DateTimeFormat(n,i).format(s).replace(/, /g," ");case"keyboardDateTime24h":return i={year:"numeric",month:"2-digit",day:"2-digit",hour:"numeric",minute:"numeric",hour12:!1},new Intl.DateTimeFormat(n,i).format(s).replace(/, /g," ");default:i=o??{timeZone:"UTC",timeZoneName:"short"}}return new Intl.DateTimeFormat(n,i).format(s)}function g0(e,t){const n=e.toJsDate(t),r=n.getFullYear(),s=lu(String(n.getMonth()+1),2,"0"),o=lu(String(n.getDate()),2,"0");return`${r}-${s}-${o}`}function v0(e){const[t,n,r]=e.split("-").map(Number);return new Date(t,n-1,r)}function p0(e,t){const n=new Date(e);return n.setMinutes(n.getMinutes()+t),n}function y0(e,t){const n=new Date(e);return n.setHours(n.getHours()+t),n}function Eo(e,t){const n=new Date(e);return n.setDate(n.getDate()+t),n}function b0(e,t){const n=new Date(e);return n.setDate(n.getDate()+t*7),n}function _0(e,t){const n=new Date(e);return n.setDate(1),n.setMonth(n.getMonth()+t),n}function ja(e){return e.getFullYear()}function S0(e){return e.getMonth()}function w0(e,t,n,r){const s=Qs(t),o=n??s?.firstDay??0,i=r??s?.firstWeekSize??1;function a(m){const g=new Date(m,0,1);return 7-Wa(g,Ha(g,t,o),"days")}let l=ja(e);const u=Eo(Ha(e,t,o),6);l<ja(u)&&a(l+1)>=i&&l++;const c=new Date(l,0,1),f=a(l),d=f>=i?Eo(c,f-7):Eo(c,f);return 1+Wa(Mh(e),qo(d),"weeks")}function E0(e){return e.getDate()}function C0(e){return new Date(e.getFullYear(),e.getMonth()+1,1)}function x0(e){return new Date(e.getFullYear(),e.getMonth()-1,1)}function T0(e){return e.getHours()}function k0(e){return e.getMinutes()}function A0(e){return new Date(e.getFullYear(),0,1)}function P0(e){return new Date(e.getFullYear(),11,31)}function I0(e,t){return Go(e,t[0])&&L0(e,t[1])}function O0(e){const t=new Date(e);return t instanceof Date&&!isNaN(t.getTime())}function Go(e,t){return e.getTime()>t.getTime()}function R0(e,t){return Go(qo(e),qo(t))}function L0(e,t){return e.getTime()<t.getTime()}function Ku(e,t){return e.getTime()===t.getTime()}function N0(e,t){return e.getDate()===t.getDate()&&e.getMonth()===t.getMonth()&&e.getFullYear()===t.getFullYear()}function D0(e,t){return e.getMonth()===t.getMonth()&&e.getFullYear()===t.getFullYear()}function F0(e,t){return e.getFullYear()===t.getFullYear()}function Wa(e,t,n){const r=new Date(e),s=new Date(t);switch(n){case"years":return r.getFullYear()-s.getFullYear();case"quarters":return Math.floor((r.getMonth()-s.getMonth()+(r.getFullYear()-s.getFullYear())*12)/4);case"months":return r.getMonth()-s.getMonth()+(r.getFullYear()-s.getFullYear())*12;case"weeks":return Math.floor((r.getTime()-s.getTime())/(1e3*60*60*24*7));case"days":return Math.floor((r.getTime()-s.getTime())/(1e3*60*60*24));case"hours":return Math.floor((r.getTime()-s.getTime())/(1e3*60*60));case"minutes":return Math.floor((r.getTime()-s.getTime())/(1e3*60));case"seconds":return Math.floor((r.getTime()-s.getTime())/1e3);default:return r.getTime()-s.getTime()}}function M0(e,t){const n=new Date(e);return n.setHours(t),n}function V0(e,t){const n=new Date(e);return n.setMinutes(t),n}function $0(e,t){const n=new Date(e);return n.setMonth(t),n}function B0(e,t){const n=new Date(e);return n.setDate(t),n}function U0(e,t){const n=new Date(e);return n.setFullYear(t),n}function qo(e){return new Date(e.getFullYear(),e.getMonth(),e.getDate(),0,0,0,0)}function Mh(e){return new Date(e.getFullYear(),e.getMonth(),e.getDate(),23,59,59,999)}class H0{constructor(t){this.locale=t.locale,this.formats=t.formats}date(t){return Fh(t)}toJsDate(t){return t}toISO(t){return g0(this,t)}parseISO(t){return v0(t)}addMinutes(t,n){return p0(t,n)}addHours(t,n){return y0(t,n)}addDays(t,n){return Eo(t,n)}addWeeks(t,n){return b0(t,n)}addMonths(t,n){return _0(t,n)}getWeekArray(t,n){const r=n!==void 0?Number(n):void 0;return c0(t,this.locale,r)}startOfWeek(t,n){const r=n!==void 0?Number(n):void 0;return Ha(t,this.locale,r)}endOfWeek(t){return u0(t,this.locale)}startOfMonth(t){return Nh(t)}endOfMonth(t){return Dh(t)}format(t,n){return h0(t,n,this.locale,this.formats)}isEqual(t,n){return Ku(t,n)}isValid(t){return O0(t)}isWithinRange(t,n){return I0(t,n)}isAfter(t,n){return Go(t,n)}isAfterDay(t,n){return R0(t,n)}isBefore(t,n){return!Go(t,n)&&!Ku(t,n)}isSameDay(t,n){return N0(t,n)}isSameMonth(t,n){return D0(t,n)}isSameYear(t,n){return F0(t,n)}setMinutes(t,n){return V0(t,n)}setHours(t,n){return M0(t,n)}setMonth(t,n){return $0(t,n)}setDate(t,n){return B0(t,n)}setYear(t,n){return U0(t,n)}getDiff(t,n,r){return Wa(t,n,r)}getWeekdays(t,n){const r=t!==void 0?Number(t):void 0;return m0(this.locale,r,n)}getYear(t){return ja(t)}getMonth(t){return S0(t)}getWeek(t,n,r){const s=n!==void 0?Number(n):void 0;return w0(t,this.locale,s,r)}getDate(t){return E0(t)}getNextMonth(t){return C0(t)}getPreviousMonth(t){return x0(t)}getHours(t){return T0(t)}getMinutes(t){return k0(t)}startOfDay(t){return qo(t)}endOfDay(t){return Mh(t)}startOfYear(t){return A0(t)}endOfYear(t){return P0(t)}}const j0=Symbol.for("vuetify:date-options"),Gu=Symbol.for("vuetify:date-adapter");function W0(e,t){const n=Dt({adapter:H0,locale:{af:"af-ZA",bg:"bg-BG",ca:"ca-ES",ckb:"",cs:"cs-CZ",de:"de-DE",el:"el-GR",en:"en-US",et:"et-EE",fa:"fa-IR",fi:"fi-FI",hr:"hr-HR",hu:"hu-HU",he:"he-IL",id:"id-ID",it:"it-IT",ja:"ja-JP",ko:"ko-KR",lv:"lv-LV",lt:"lt-LT",nl:"nl-NL",no:"no-NO",pl:"pl-PL",pt:"pt-PT",ro:"ro-RO",ru:"ru-RU",sk:"sk-SK",sl:"sl-SI",srCyrl:"sr-SP",srLatn:"sr-SP",sv:"sv-SE",th:"th-TH",tr:"tr-TR",az:"az-AZ",uk:"uk-UA",vi:"vi-VN",zhHans:"zh-CN",zhHant:"zh-TW"}},e);return{options:n,instance:z0(n,t)}}function z0(e,t){const n=st(typeof e.adapter=="function"?new e.adapter({locale:e.locale[t.current.value]??t.current.value,formats:e.formats}):e.adapter);return le(t.current,r=>{n.locale=e.locale[r]??r??n.locale}),n}const K0=Symbol.for("vuetify:goto");function G0(){return{container:void 0,duration:300,layout:!1,offset:0,easing:"easeInOutCubic",patterns:{linear:e=>e,easeInQuad:e=>e**2,easeOutQuad:e=>e*(2-e),easeInOutQuad:e=>e<.5?2*e**2:-1+(4-2*e)*e,easeInCubic:e=>e**3,easeOutCubic:e=>--e**3+1,easeInOutCubic:e=>e<.5?4*e**3:(e-1)*(2*e-2)*(2*e-2)+1,easeInQuart:e=>e**4,easeOutQuart:e=>1- --e**4,easeInOutQuart:e=>e<.5?8*e**4:1-8*--e**4,easeInQuint:e=>e**5,easeOutQuint:e=>1+--e**5,easeInOutQuint:e=>e<.5?16*e**5:1+16*--e**5}}}function q0(e,t){return{rtl:t.isRtl,options:Dt(G0(),e)}}const sa=Symbol("Forwarded refs");function oa(e,t){let n=e;for(;n;){const r=Reflect.getOwnPropertyDescriptor(n,t);if(r)return r;n=Object.getPrototypeOf(n)}}function eo(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return e[sa]=n,new Proxy(e,{get(s,o){if(Reflect.has(s,o))return Reflect.get(s,o);if(!(typeof o=="symbol"||o.startsWith("$")||o.startsWith("__"))){for(const i of n)if(i.value&&Reflect.has(i.value,o)){const a=Reflect.get(i.value,o);return typeof a=="function"?a.bind(i.value):a}}},has(s,o){if(Reflect.has(s,o))return!0;if(typeof o=="symbol"||o.startsWith("$")||o.startsWith("__"))return!1;for(const i of n)if(i.value&&Reflect.has(i.value,o))return!0;return!1},set(s,o,i){if(Reflect.has(s,o))return Reflect.set(s,o,i);if(typeof o=="symbol"||o.startsWith("$")||o.startsWith("__"))return!1;for(const a of n)if(a.value&&Reflect.has(a.value,o))return Reflect.set(a.value,o,i);return!1},getOwnPropertyDescriptor(s,o){const i=Reflect.getOwnPropertyDescriptor(s,o);if(i)return i;if(!(typeof o=="symbol"||o.startsWith("$")||o.startsWith("__"))){for(const a of n){if(!a.value)continue;const l=oa(a.value,o)??("_"in a.value?oa(a.value._?.setupState,o):void 0);if(l)return l}for(const a of n){const l=a.value&&a.value[sa];if(!l)continue;const u=l.slice();for(;u.length;){const c=u.shift(),f=oa(c.value,o);if(f)return f;const d=c.value&&c.value[sa];d&&u.push(...d)}}}}})}function Y0(e){const t=Ae(e());let n=-1;function r(){clearInterval(n)}function s(){r(),bt(()=>t.value=e())}function o(i){const a=i?getComputedStyle(i):{transitionDuration:.2},l=parseFloat(a.transitionDuration)*1e3||200;if(r(),t.value<=0)return;const u=performance.now();n=window.setInterval(()=>{const c=performance.now()-u+l;t.value=Math.max(e()-c,0),t.value<=0&&r()},l)}return St(r),{clear:r,time:t,start:o,reset:s}}const X0=te({multiLine:Boolean,text:String,timer:[Boolean,String],timeout:{type:[Number,String],default:5e3},vertical:Boolean,...Zs({location:"bottom"}),..._i(),...$t(),...Or(),...ct(),...Fl(Ci({transition:"v-snackbar-transition"}),["persistent","noClickAnimation","scrim","scrollStrategy"])},"VSnackbar"),J0=ge()({name:"VSnackbar",props:X0(),emits:{"update:modelValue":e=>!0},setup(e,t){let{slots:n}=t;const r=Vt(e,"modelValue"),{positionClasses:s}=Si(e),{scopeId:o}=wi(),{themeClasses:i}=ht(e),{colorClasses:a,colorStyles:l,variantClasses:u}=rs(e),{roundedClasses:c}=Bt(e),f=Y0(()=>Number(e.timeout)),d=J(),m=J(),g=Ae(!1),p=Ae(0),w=J(),h=Le(jo,void 0);Cr(()=>!!h,()=>{const V=Kb();en(()=>{w.value=V.mainStyles.value})}),le(r,b),le(()=>e.timeout,b),Gt(()=>{r.value&&b()});let v=-1;function b(){f.reset(),window.clearTimeout(v);const V=Number(e.timeout);if(!r.value||V===-1)return;const D=Mm(m.value);f.start(D),v=window.setTimeout(()=>{r.value=!1},V)}function y(){f.reset(),window.clearTimeout(v)}function E(){g.value=!0,y()}function P(){g.value=!1,b()}function k(V){p.value=V.touches[0].clientY}function T(V){Math.abs(p.value-V.changedTouches[0].clientY)>50&&(r.value=!1)}function A(){g.value&&P()}const H=U(()=>e.location.split(" ").reduce((V,D)=>(V[`v-snackbar--${D}`]=!0,V),{}));return ye(()=>{const V=kr.filterProps(e),D=!!(n.default||n.text||e.text);return R(kr,Pe({ref:d,class:["v-snackbar",{"v-snackbar--active":r.value,"v-snackbar--multi-line":e.multiLine&&!e.vertical,"v-snackbar--timer":!!e.timer,"v-snackbar--vertical":e.vertical},H.value,s.value,e.class],style:[w.value,e.style]},V,{modelValue:r.value,"onUpdate:modelValue":G=>r.value=G,contentProps:Pe({class:["v-snackbar__wrapper",i.value,a.value,c.value,u.value],style:[l.value],onPointerenter:E,onPointerleave:P},V.contentProps),persistent:!0,noClickAnimation:!0,scrim:!1,scrollStrategy:"none",_disableGlobalStack:!0,onTouchstartPassive:k,onTouchend:T,onAfterLeave:A},o),{default:()=>[ns(!1,"v-snackbar"),e.timer&&!g.value&&N("div",{key:"timer",class:"v-snackbar__timer"},[R(vh,{ref:m,color:typeof e.timer=="string"?e.timer:"info",max:e.timeout,"model-value":f.time.value},null)]),D&&N("div",{key:"content",class:"v-snackbar__content",role:"status","aria-live":"polite"},[n.text?.()??e.text,n.default?.()]),n.actions&&R(Ye,{defaults:{VBtn:{variant:"text",ripple:!1,slim:!0}}},{default:()=>[N("div",{class:"v-snackbar__actions"},[n.actions({isActive:r})])]})],activator:n.activator})}),eo({},d)}}),Z0={id:"chat-container"},Q0={__name:"App",setup(e){const t=hi();return(n,r)=>{const s=em("router-view");return qe(),fi(f_,null,{default:Ue(()=>[N("div",Z0,[R(s),R(J0,{modelValue:ot(t).visible,"onUpdate:modelValue":r[1]||(r[1]=o=>ot(t).visible=o),color:ot(t).type,timeout:"-1",top:""},{actions:Ue(()=>[R(Gn,{color:"white",icon:"",size:"small",onClick:r[0]||(r[0]=o=>ot(t).hideMessage())},{default:Ue(()=>[R(_t,null,{default:Ue(()=>r[2]||(r[2]=[ut("mdi-close")])),_:1,__:[2]})]),_:1})]),default:Ue(()=>[ut(tt(ot(t).message)+" ",1)]),_:1},8,["modelValue","color"])])]),_:1})}}};/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const $r=typeof document<"u";function Vh(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function eS(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&Vh(e.default)}const De=Object.assign;function ia(e,t){const n={};for(const r in t){const s=t[r];n[r]=Zt(s)?s.map(e):e(s)}return n}const Cs=()=>{},Zt=Array.isArray,$h=/#/g,tS=/&/g,nS=/\//g,rS=/=/g,sS=/\?/g,Bh=/\+/g,oS=/%5B/g,iS=/%5D/g,Uh=/%5E/g,aS=/%60/g,Hh=/%7B/g,lS=/%7C/g,jh=/%7D/g,cS=/%20/g;function Yl(e){return encodeURI(""+e).replace(lS,"|").replace(oS,"[").replace(iS,"]")}function uS(e){return Yl(e).replace(Hh,"{").replace(jh,"}").replace(Uh,"^")}function za(e){return Yl(e).replace(Bh,"%2B").replace(cS,"+").replace($h,"%23").replace(tS,"%26").replace(aS,"`").replace(Hh,"{").replace(jh,"}").replace(Uh,"^")}function fS(e){return za(e).replace(rS,"%3D")}function dS(e){return Yl(e).replace($h,"%23").replace(sS,"%3F")}function mS(e){return e==null?"":dS(e).replace(nS,"%2F")}function Ms(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const hS=/\/$/,gS=e=>e.replace(hS,"");function aa(e,t,n="/"){let r,s={},o="",i="";const a=t.indexOf("#");let l=t.indexOf("?");return a<l&&a>=0&&(l=-1),l>-1&&(r=t.slice(0,l),o=t.slice(l+1,a>-1?a:t.length),s=e(o)),a>-1&&(r=r||t.slice(0,a),i=t.slice(a,t.length)),r=bS(r??t,n),{fullPath:r+(o&&"?")+o+i,path:r,query:s,hash:Ms(i)}}function vS(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function qu(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function pS(e,t,n){const r=t.matched.length-1,s=n.matched.length-1;return r>-1&&r===s&&qr(t.matched[r],n.matched[s])&&Wh(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function qr(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Wh(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!yS(e[n],t[n]))return!1;return!0}function yS(e,t){return Zt(e)?Yu(e,t):Zt(t)?Yu(t,e):e===t}function Yu(e,t){return Zt(t)?e.length===t.length&&e.every((n,r)=>n===t[r]):e.length===1&&e[0]===t}function bS(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),r=e.split("/"),s=r[r.length-1];(s===".."||s===".")&&r.push("");let o=n.length-1,i,a;for(i=0;i<r.length;i++)if(a=r[i],a!==".")if(a==="..")o>1&&o--;else break;return n.slice(0,o).join("/")+"/"+r.slice(i).join("/")}const Nn={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var Vs;(function(e){e.pop="pop",e.push="push"})(Vs||(Vs={}));var xs;(function(e){e.back="back",e.forward="forward",e.unknown=""})(xs||(xs={}));function _S(e){if(!e)if($r){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),gS(e)}const SS=/^[^#]+#/;function wS(e,t){return e.replace(SS,"#")+t}function ES(e,t){const n=document.documentElement.getBoundingClientRect(),r=e.getBoundingClientRect();return{behavior:t.behavior,left:r.left-n.left-(t.left||0),top:r.top-n.top-(t.top||0)}}const xi=()=>({left:window.scrollX,top:window.scrollY});function CS(e){let t;if("el"in e){const n=e.el,r=typeof n=="string"&&n.startsWith("#"),s=typeof n=="string"?r?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!s)return;t=ES(s,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Xu(e,t){return(history.state?history.state.position-t:-1)+e}const Ka=new Map;function xS(e,t){Ka.set(e,t)}function TS(e){const t=Ka.get(e);return Ka.delete(e),t}let kS=()=>location.protocol+"//"+location.host;function zh(e,t){const{pathname:n,search:r,hash:s}=t,o=e.indexOf("#");if(o>-1){let a=s.includes(e.slice(o))?e.slice(o).length:1,l=s.slice(a);return l[0]!=="/"&&(l="/"+l),qu(l,"")}return qu(n,e)+r+s}function AS(e,t,n,r){let s=[],o=[],i=null;const a=({state:d})=>{const m=zh(e,location),g=n.value,p=t.value;let w=0;if(d){if(n.value=m,t.value=d,i&&i===g){i=null;return}w=p?d.position-p.position:0}else r(m);s.forEach(h=>{h(n.value,g,{delta:w,type:Vs.pop,direction:w?w>0?xs.forward:xs.back:xs.unknown})})};function l(){i=n.value}function u(d){s.push(d);const m=()=>{const g=s.indexOf(d);g>-1&&s.splice(g,1)};return o.push(m),m}function c(){const{history:d}=window;d.state&&d.replaceState(De({},d.state,{scroll:xi()}),"")}function f(){for(const d of o)d();o=[],window.removeEventListener("popstate",a),window.removeEventListener("beforeunload",c)}return window.addEventListener("popstate",a),window.addEventListener("beforeunload",c,{passive:!0}),{pauseListeners:l,listen:u,destroy:f}}function Ju(e,t,n,r=!1,s=!1){return{back:e,current:t,forward:n,replaced:r,position:window.history.length,scroll:s?xi():null}}function PS(e){const{history:t,location:n}=window,r={value:zh(e,n)},s={value:t.state};s.value||o(r.value,{back:null,current:r.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function o(l,u,c){const f=e.indexOf("#"),d=f>-1?(n.host&&document.querySelector("base")?e:e.slice(f))+l:kS()+e+l;try{t[c?"replaceState":"pushState"](u,"",d),s.value=u}catch(m){console.error(m),n[c?"replace":"assign"](d)}}function i(l,u){const c=De({},t.state,Ju(s.value.back,l,s.value.forward,!0),u,{position:s.value.position});o(l,c,!0),r.value=l}function a(l,u){const c=De({},s.value,t.state,{forward:l,scroll:xi()});o(c.current,c,!0);const f=De({},Ju(r.value,l,null),{position:c.position+1},u);o(l,f,!1),r.value=l}return{location:r,state:s,push:a,replace:i}}function IS(e){e=_S(e);const t=PS(e),n=AS(e,t.state,t.location,t.replace);function r(o,i=!0){i||n.pauseListeners(),history.go(o)}const s=De({location:"",base:e,go:r,createHref:wS.bind(null,e)},t,n);return Object.defineProperty(s,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(s,"state",{enumerable:!0,get:()=>t.state.value}),s}function OS(e){return e=location.host?e||location.pathname+location.search:"",e.includes("#")||(e+="#"),IS(e)}function RS(e){return typeof e=="string"||e&&typeof e=="object"}function Kh(e){return typeof e=="string"||typeof e=="symbol"}const Gh=Symbol("");var Zu;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Zu||(Zu={}));function Yr(e,t){return De(new Error,{type:e,[Gh]:!0},t)}function _n(e,t){return e instanceof Error&&Gh in e&&(t==null||!!(e.type&t))}const Qu="[^/]+?",LS={sensitive:!1,strict:!1,start:!0,end:!0},NS=/[.+*?^${}()[\]/\\]/g;function DS(e,t){const n=De({},LS,t),r=[];let s=n.start?"^":"";const o=[];for(const u of e){const c=u.length?[]:[90];n.strict&&!u.length&&(s+="/");for(let f=0;f<u.length;f++){const d=u[f];let m=40+(n.sensitive?.25:0);if(d.type===0)f||(s+="/"),s+=d.value.replace(NS,"\\$&"),m+=40;else if(d.type===1){const{value:g,repeatable:p,optional:w,regexp:h}=d;o.push({name:g,repeatable:p,optional:w});const v=h||Qu;if(v!==Qu){m+=10;try{new RegExp(`(${v})`)}catch(y){throw new Error(`Invalid custom RegExp for param "${g}" (${v}): `+y.message)}}let b=p?`((?:${v})(?:/(?:${v}))*)`:`(${v})`;f||(b=w&&u.length<2?`(?:/${b})`:"/"+b),w&&(b+="?"),s+=b,m+=20,w&&(m+=-8),p&&(m+=-20),v===".*"&&(m+=-50)}c.push(m)}r.push(c)}if(n.strict&&n.end){const u=r.length-1;r[u][r[u].length-1]+=.7000000000000001}n.strict||(s+="/?"),n.end?s+="$":n.strict&&!s.endsWith("/")&&(s+="(?:/|$)");const i=new RegExp(s,n.sensitive?"":"i");function a(u){const c=u.match(i),f={};if(!c)return null;for(let d=1;d<c.length;d++){const m=c[d]||"",g=o[d-1];f[g.name]=m&&g.repeatable?m.split("/"):m}return f}function l(u){let c="",f=!1;for(const d of e){(!f||!c.endsWith("/"))&&(c+="/"),f=!1;for(const m of d)if(m.type===0)c+=m.value;else if(m.type===1){const{value:g,repeatable:p,optional:w}=m,h=g in u?u[g]:"";if(Zt(h)&&!p)throw new Error(`Provided param "${g}" is an array but it is not repeatable (* or + modifiers)`);const v=Zt(h)?h.join("/"):h;if(!v)if(w)d.length<2&&(c.endsWith("/")?c=c.slice(0,-1):f=!0);else throw new Error(`Missing required param "${g}"`);c+=v}}return c||"/"}return{re:i,score:r,keys:o,parse:a,stringify:l}}function FS(e,t){let n=0;for(;n<e.length&&n<t.length;){const r=t[n]-e[n];if(r)return r;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function qh(e,t){let n=0;const r=e.score,s=t.score;for(;n<r.length&&n<s.length;){const o=FS(r[n],s[n]);if(o)return o;n++}if(Math.abs(s.length-r.length)===1){if(ef(r))return 1;if(ef(s))return-1}return s.length-r.length}function ef(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const MS={type:0,value:""},VS=/[a-zA-Z0-9_]/;function $S(e){if(!e)return[[]];if(e==="/")return[[MS]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(m){throw new Error(`ERR (${n})/"${u}": ${m}`)}let n=0,r=n;const s=[];let o;function i(){o&&s.push(o),o=[]}let a=0,l,u="",c="";function f(){u&&(n===0?o.push({type:0,value:u}):n===1||n===2||n===3?(o.length>1&&(l==="*"||l==="+")&&t(`A repeatable param (${u}) must be alone in its segment. eg: '/:ids+.`),o.push({type:1,value:u,regexp:c,repeatable:l==="*"||l==="+",optional:l==="*"||l==="?"})):t("Invalid state to consume buffer"),u="")}function d(){u+=l}for(;a<e.length;){if(l=e[a++],l==="\\"&&n!==2){r=n,n=4;continue}switch(n){case 0:l==="/"?(u&&f(),i()):l===":"?(f(),n=1):d();break;case 4:d(),n=r;break;case 1:l==="("?n=2:VS.test(l)?d():(f(),n=0,l!=="*"&&l!=="?"&&l!=="+"&&a--);break;case 2:l===")"?c[c.length-1]=="\\"?c=c.slice(0,-1)+l:n=3:c+=l;break;case 3:f(),n=0,l!=="*"&&l!=="?"&&l!=="+"&&a--,c="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${u}"`),f(),i(),s}function BS(e,t,n){const r=DS($S(e.path),n),s=De(r,{record:e,parent:t,children:[],alias:[]});return t&&!s.record.aliasOf==!t.record.aliasOf&&t.children.push(s),s}function US(e,t){const n=[],r=new Map;t=sf({strict:!1,end:!0,sensitive:!1},t);function s(f){return r.get(f)}function o(f,d,m){const g=!m,p=nf(f);p.aliasOf=m&&m.record;const w=sf(t,f),h=[p];if("alias"in f){const y=typeof f.alias=="string"?[f.alias]:f.alias;for(const E of y)h.push(nf(De({},p,{components:m?m.record.components:p.components,path:E,aliasOf:m?m.record:p})))}let v,b;for(const y of h){const{path:E}=y;if(d&&E[0]!=="/"){const P=d.record.path,k=P[P.length-1]==="/"?"":"/";y.path=d.record.path+(E&&k+E)}if(v=BS(y,d,w),m?m.alias.push(v):(b=b||v,b!==v&&b.alias.push(v),g&&f.name&&!rf(v)&&i(f.name)),Yh(v)&&l(v),p.children){const P=p.children;for(let k=0;k<P.length;k++)o(P[k],v,m&&m.children[k])}m=m||v}return b?()=>{i(b)}:Cs}function i(f){if(Kh(f)){const d=r.get(f);d&&(r.delete(f),n.splice(n.indexOf(d),1),d.children.forEach(i),d.alias.forEach(i))}else{const d=n.indexOf(f);d>-1&&(n.splice(d,1),f.record.name&&r.delete(f.record.name),f.children.forEach(i),f.alias.forEach(i))}}function a(){return n}function l(f){const d=WS(f,n);n.splice(d,0,f),f.record.name&&!rf(f)&&r.set(f.record.name,f)}function u(f,d){let m,g={},p,w;if("name"in f&&f.name){if(m=r.get(f.name),!m)throw Yr(1,{location:f});w=m.record.name,g=De(tf(d.params,m.keys.filter(b=>!b.optional).concat(m.parent?m.parent.keys.filter(b=>b.optional):[]).map(b=>b.name)),f.params&&tf(f.params,m.keys.map(b=>b.name))),p=m.stringify(g)}else if(f.path!=null)p=f.path,m=n.find(b=>b.re.test(p)),m&&(g=m.parse(p),w=m.record.name);else{if(m=d.name?r.get(d.name):n.find(b=>b.re.test(d.path)),!m)throw Yr(1,{location:f,currentLocation:d});w=m.record.name,g=De({},d.params,f.params),p=m.stringify(g)}const h=[];let v=m;for(;v;)h.unshift(v.record),v=v.parent;return{name:w,path:p,params:g,matched:h,meta:jS(h)}}e.forEach(f=>o(f));function c(){n.length=0,r.clear()}return{addRoute:o,resolve:u,removeRoute:i,clearRoutes:c,getRoutes:a,getRecordMatcher:s}}function tf(e,t){const n={};for(const r of t)r in e&&(n[r]=e[r]);return n}function nf(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:HS(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function HS(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const r in e.components)t[r]=typeof n=="object"?n[r]:n;return t}function rf(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function jS(e){return e.reduce((t,n)=>De(t,n.meta),{})}function sf(e,t){const n={};for(const r in e)n[r]=r in t?t[r]:e[r];return n}function WS(e,t){let n=0,r=t.length;for(;n!==r;){const o=n+r>>1;qh(e,t[o])<0?r=o:n=o+1}const s=zS(e);return s&&(r=t.lastIndexOf(s,r-1)),r}function zS(e){let t=e;for(;t=t.parent;)if(Yh(t)&&qh(e,t)===0)return t}function Yh({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function KS(e){const t={};if(e===""||e==="?")return t;const r=(e[0]==="?"?e.slice(1):e).split("&");for(let s=0;s<r.length;++s){const o=r[s].replace(Bh," "),i=o.indexOf("="),a=Ms(i<0?o:o.slice(0,i)),l=i<0?null:Ms(o.slice(i+1));if(a in t){let u=t[a];Zt(u)||(u=t[a]=[u]),u.push(l)}else t[a]=l}return t}function of(e){let t="";for(let n in e){const r=e[n];if(n=fS(n),r==null){r!==void 0&&(t+=(t.length?"&":"")+n);continue}(Zt(r)?r.map(o=>o&&za(o)):[r&&za(r)]).forEach(o=>{o!==void 0&&(t+=(t.length?"&":"")+n,o!=null&&(t+="="+o))})}return t}function GS(e){const t={};for(const n in e){const r=e[n];r!==void 0&&(t[n]=Zt(r)?r.map(s=>s==null?null:""+s):r==null?r:""+r)}return t}const qS=Symbol(""),af=Symbol(""),Ti=Symbol(""),Xh=Symbol(""),Ga=Symbol("");function fs(){let e=[];function t(r){return e.push(r),()=>{const s=e.indexOf(r);s>-1&&e.splice(s,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function Un(e,t,n,r,s,o=i=>i()){const i=r&&(r.enterCallbacks[s]=r.enterCallbacks[s]||[]);return()=>new Promise((a,l)=>{const u=d=>{d===!1?l(Yr(4,{from:n,to:t})):d instanceof Error?l(d):RS(d)?l(Yr(2,{from:t,to:d})):(i&&r.enterCallbacks[s]===i&&typeof d=="function"&&i.push(d),a())},c=o(()=>e.call(r&&r.instances[s],t,n,u));let f=Promise.resolve(c);e.length<3&&(f=f.then(u)),f.catch(d=>l(d))})}function la(e,t,n,r,s=o=>o()){const o=[];for(const i of e)for(const a in i.components){let l=i.components[a];if(!(t!=="beforeRouteEnter"&&!i.instances[a]))if(Vh(l)){const c=(l.__vccOpts||l)[t];c&&o.push(Un(c,n,r,i,a,s))}else{let u=l();o.push(()=>u.then(c=>{if(!c)throw new Error(`Couldn't resolve component "${a}" at "${i.path}"`);const f=eS(c)?c.default:c;i.mods[a]=c,i.components[a]=f;const m=(f.__vccOpts||f)[t];return m&&Un(m,n,r,i,a,s)()}))}}return o}function lf(e){const t=Le(Ti),n=Le(Xh),r=U(()=>{const l=ot(e.to);return t.resolve(l)}),s=U(()=>{const{matched:l}=r.value,{length:u}=l,c=l[u-1],f=n.matched;if(!c||!f.length)return-1;const d=f.findIndex(qr.bind(null,c));if(d>-1)return d;const m=cf(l[u-2]);return u>1&&cf(c)===m&&f[f.length-1].path!==m?f.findIndex(qr.bind(null,l[u-2])):d}),o=U(()=>s.value>-1&&QS(n.params,r.value.params)),i=U(()=>s.value>-1&&s.value===n.matched.length-1&&Wh(n.params,r.value.params));function a(l={}){if(ZS(l)){const u=t[ot(e.replace)?"replace":"push"](ot(e.to)).catch(Cs);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>u),u}return Promise.resolve()}return{route:r,href:U(()=>r.value.href),isActive:o,isExactActive:i,navigate:a}}function YS(e){return e.length===1?e[0]:e}const XS=es({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:lf,setup(e,{slots:t}){const n=st(lf(e)),{options:r}=Le(Ti),s=U(()=>({[uf(e.activeClass,r.linkActiveClass,"router-link-active")]:n.isActive,[uf(e.exactActiveClass,r.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const o=t.default&&YS(t.default(n));return e.custom?o:vn("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:s.value},o)}}}),JS=XS;function ZS(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function QS(e,t){for(const n in t){const r=t[n],s=e[n];if(typeof r=="string"){if(r!==s)return!1}else if(!Zt(s)||s.length!==r.length||r.some((o,i)=>o!==s[i]))return!1}return!0}function cf(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const uf=(e,t,n)=>e??t??n,ew=es({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const r=Le(Ga),s=U(()=>e.route||r.value),o=Le(af,0),i=U(()=>{let u=ot(o);const{matched:c}=s.value;let f;for(;(f=c[u])&&!f.components;)u++;return u}),a=U(()=>s.value.matched[i.value]);Ft(af,U(()=>i.value+1)),Ft(qS,a),Ft(Ga,s);const l=J();return le(()=>[l.value,a.value,e.name],([u,c,f],[d,m,g])=>{c&&(c.instances[f]=u,m&&m!==c&&u&&u===d&&(c.leaveGuards.size||(c.leaveGuards=m.leaveGuards),c.updateGuards.size||(c.updateGuards=m.updateGuards))),u&&c&&(!m||!qr(c,m)||!d)&&(c.enterCallbacks[f]||[]).forEach(p=>p(u))},{flush:"post"}),()=>{const u=s.value,c=e.name,f=a.value,d=f&&f.components[c];if(!d)return ff(n.default,{Component:d,route:u});const m=f.props[c],g=m?m===!0?u.params:typeof m=="function"?m(u):m:null,w=vn(d,De({},g,t,{onVnodeUnmounted:h=>{h.component.isUnmounted&&(f.instances[c]=null)},ref:l}));return ff(n.default,{Component:w,route:u})||w}}});function ff(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const tw=ew;function nw(e){const t=US(e.routes,e),n=e.parseQuery||KS,r=e.stringifyQuery||of,s=e.history,o=fs(),i=fs(),a=fs(),l=Ae(Nn);let u=Nn;$r&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const c=ia.bind(null,F=>""+F),f=ia.bind(null,mS),d=ia.bind(null,Ms);function m(F,q){let Y,ie;return Kh(F)?(Y=t.getRecordMatcher(F),ie=q):ie=F,t.addRoute(ie,Y)}function g(F){const q=t.getRecordMatcher(F);q&&t.removeRoute(q)}function p(){return t.getRoutes().map(F=>F.record)}function w(F){return!!t.getRecordMatcher(F)}function h(F,q){if(q=De({},q||l.value),typeof F=="string"){const L=aa(n,F,q.path),j=t.resolve({path:L.path},q),z=s.createHref(L.fullPath);return De(L,j,{params:d(j.params),hash:Ms(L.hash),redirectedFrom:void 0,href:z})}let Y;if(F.path!=null)Y=De({},F,{path:aa(n,F.path,q.path).path});else{const L=De({},F.params);for(const j in L)L[j]==null&&delete L[j];Y=De({},F,{params:f(L)}),q.params=f(q.params)}const ie=t.resolve(Y,q),Te=F.hash||"";ie.params=c(d(ie.params));const C=vS(r,De({},F,{hash:uS(Te),path:ie.path})),x=s.createHref(C);return De({fullPath:C,hash:Te,query:r===of?GS(F.query):F.query||{}},ie,{redirectedFrom:void 0,href:x})}function v(F){return typeof F=="string"?aa(n,F,l.value.path):De({},F)}function b(F,q){if(u!==F)return Yr(8,{from:q,to:F})}function y(F){return k(F)}function E(F){return y(De(v(F),{replace:!0}))}function P(F){const q=F.matched[F.matched.length-1];if(q&&q.redirect){const{redirect:Y}=q;let ie=typeof Y=="function"?Y(F):Y;return typeof ie=="string"&&(ie=ie.includes("?")||ie.includes("#")?ie=v(ie):{path:ie},ie.params={}),De({query:F.query,hash:F.hash,params:ie.path!=null?{}:F.params},ie)}}function k(F,q){const Y=u=h(F),ie=l.value,Te=F.state,C=F.force,x=F.replace===!0,L=P(Y);if(L)return k(De(v(L),{state:typeof L=="object"?De({},Te,L.state):Te,force:C,replace:x}),q||Y);const j=Y;j.redirectedFrom=q;let z;return!C&&pS(r,ie,Y)&&(z=Yr(16,{to:j,from:ie}),xe(ie,ie,!0,!1)),(z?Promise.resolve(z):H(j,ie)).catch(W=>_n(W)?_n(W,2)?W:ue(W):re(W,j,ie)).then(W=>{if(W){if(_n(W,2))return k(De({replace:x},v(W.to),{state:typeof W.to=="object"?De({},Te,W.to.state):Te,force:C}),q||j)}else W=D(j,ie,!0,x,Te);return V(j,ie,W),W})}function T(F,q){const Y=b(F,q);return Y?Promise.reject(Y):Promise.resolve()}function A(F){const q=Ge.values().next().value;return q&&typeof q.runWithContext=="function"?q.runWithContext(F):F()}function H(F,q){let Y;const[ie,Te,C]=rw(F,q);Y=la(ie.reverse(),"beforeRouteLeave",F,q);for(const L of ie)L.leaveGuards.forEach(j=>{Y.push(Un(j,F,q))});const x=T.bind(null,F,q);return Y.push(x),Ne(Y).then(()=>{Y=[];for(const L of o.list())Y.push(Un(L,F,q));return Y.push(x),Ne(Y)}).then(()=>{Y=la(Te,"beforeRouteUpdate",F,q);for(const L of Te)L.updateGuards.forEach(j=>{Y.push(Un(j,F,q))});return Y.push(x),Ne(Y)}).then(()=>{Y=[];for(const L of C)if(L.beforeEnter)if(Zt(L.beforeEnter))for(const j of L.beforeEnter)Y.push(Un(j,F,q));else Y.push(Un(L.beforeEnter,F,q));return Y.push(x),Ne(Y)}).then(()=>(F.matched.forEach(L=>L.enterCallbacks={}),Y=la(C,"beforeRouteEnter",F,q,A),Y.push(x),Ne(Y))).then(()=>{Y=[];for(const L of i.list())Y.push(Un(L,F,q));return Y.push(x),Ne(Y)}).catch(L=>_n(L,8)?L:Promise.reject(L))}function V(F,q,Y){a.list().forEach(ie=>A(()=>ie(F,q,Y)))}function D(F,q,Y,ie,Te){const C=b(F,q);if(C)return C;const x=q===Nn,L=$r?history.state:{};Y&&(ie||x?s.replace(F.fullPath,De({scroll:x&&L&&L.scroll},Te)):s.push(F.fullPath,Te)),l.value=F,xe(F,q,Y,x),ue()}let G;function ee(){G||(G=s.listen((F,q,Y)=>{if(!Se.listening)return;const ie=h(F),Te=P(ie);if(Te){k(De(Te,{replace:!0,force:!0}),ie).catch(Cs);return}u=ie;const C=l.value;$r&&xS(Xu(C.fullPath,Y.delta),xi()),H(ie,C).catch(x=>_n(x,12)?x:_n(x,2)?(k(De(v(x.to),{force:!0}),ie).then(L=>{_n(L,20)&&!Y.delta&&Y.type===Vs.pop&&s.go(-1,!1)}).catch(Cs),Promise.reject()):(Y.delta&&s.go(-Y.delta,!1),re(x,ie,C))).then(x=>{x=x||D(ie,C,!1),x&&(Y.delta&&!_n(x,8)?s.go(-Y.delta,!1):Y.type===Vs.pop&&_n(x,20)&&s.go(-1,!1)),V(ie,C,x)}).catch(Cs)}))}let ne=fs(),oe=fs(),Z;function re(F,q,Y){ue(F);const ie=oe.list();return ie.length?ie.forEach(Te=>Te(F,q,Y)):console.error(F),Promise.reject(F)}function pe(){return Z&&l.value!==Nn?Promise.resolve():new Promise((F,q)=>{ne.add([F,q])})}function ue(F){return Z||(Z=!F,ee(),ne.list().forEach(([q,Y])=>F?Y(F):q()),ne.reset()),F}function xe(F,q,Y,ie){const{scrollBehavior:Te}=e;if(!$r||!Te)return Promise.resolve();const C=!Y&&TS(Xu(F.fullPath,0))||(ie||!Y)&&history.state&&history.state.scroll||null;return bt().then(()=>Te(F,q,C)).then(x=>x&&CS(x)).catch(x=>re(x,F,q))}const _e=F=>s.go(F);let Me;const Ge=new Set,Se={currentRoute:l,listening:!0,addRoute:m,removeRoute:g,clearRoutes:t.clearRoutes,hasRoute:w,getRoutes:p,resolve:h,options:e,push:y,replace:E,go:_e,back:()=>_e(-1),forward:()=>_e(1),beforeEach:o.add,beforeResolve:i.add,afterEach:a.add,onError:oe.add,isReady:pe,install(F){const q=this;F.component("RouterLink",JS),F.component("RouterView",tw),F.config.globalProperties.$router=q,Object.defineProperty(F.config.globalProperties,"$route",{enumerable:!0,get:()=>ot(l)}),$r&&!Me&&l.value===Nn&&(Me=!0,y(s.location).catch(Te=>{}));const Y={};for(const Te in Nn)Object.defineProperty(Y,Te,{get:()=>l.value[Te],enumerable:!0});F.provide(Ti,q),F.provide(Xh,Ld(Y)),F.provide(Ga,l);const ie=F.unmount;Ge.add(F),F.unmount=function(){Ge.delete(F),Ge.size<1&&(u=Nn,G&&G(),G=null,l.value=Nn,Me=!1,Z=!1),ie()}}};function Ne(F){return F.reduce((q,Y)=>q.then(()=>A(Y)),Promise.resolve())}return Se}function rw(e,t){const n=[],r=[],s=[],o=Math.max(t.matched.length,e.matched.length);for(let i=0;i<o;i++){const a=t.matched[i];a&&(e.matched.find(u=>qr(u,a))?r.push(a):n.push(a));const l=e.matched[i];l&&(t.matched.find(u=>qr(u,l))||s.push(l))}return[n,r,s]}function Xl(){return Le(Ti)}function Jh(e,t){return function(){return e.apply(t,arguments)}}const{toString:sw}=Object.prototype,{getPrototypeOf:Jl}=Object,{iterator:ki,toStringTag:Zh}=Symbol,Ai=(e=>t=>{const n=sw.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),tn=e=>(e=e.toLowerCase(),t=>Ai(t)===e),Pi=e=>t=>typeof t===e,{isArray:ss}=Array,$s=Pi("undefined");function ow(e){return e!==null&&!$s(e)&&e.constructor!==null&&!$s(e.constructor)&&At(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const Qh=tn("ArrayBuffer");function iw(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&Qh(e.buffer),t}const aw=Pi("string"),At=Pi("function"),eg=Pi("number"),Ii=e=>e!==null&&typeof e=="object",lw=e=>e===!0||e===!1,Co=e=>{if(Ai(e)!=="object")return!1;const t=Jl(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Zh in e)&&!(ki in e)},cw=tn("Date"),uw=tn("File"),fw=tn("Blob"),dw=tn("FileList"),mw=e=>Ii(e)&&At(e.pipe),hw=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||At(e.append)&&((t=Ai(e))==="formdata"||t==="object"&&At(e.toString)&&e.toString()==="[object FormData]"))},gw=tn("URLSearchParams"),[vw,pw,yw,bw]=["ReadableStream","Request","Response","Headers"].map(tn),_w=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function to(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let r,s;if(typeof e!="object"&&(e=[e]),ss(e))for(r=0,s=e.length;r<s;r++)t.call(null,e[r],r,e);else{const o=n?Object.getOwnPropertyNames(e):Object.keys(e),i=o.length;let a;for(r=0;r<i;r++)a=o[r],t.call(null,e[a],a,e)}}function tg(e,t){t=t.toLowerCase();const n=Object.keys(e);let r=n.length,s;for(;r-- >0;)if(s=n[r],t===s.toLowerCase())return s;return null}const hr=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,ng=e=>!$s(e)&&e!==hr;function qa(){const{caseless:e}=ng(this)&&this||{},t={},n=(r,s)=>{const o=e&&tg(t,s)||s;Co(t[o])&&Co(r)?t[o]=qa(t[o],r):Co(r)?t[o]=qa({},r):ss(r)?t[o]=r.slice():t[o]=r};for(let r=0,s=arguments.length;r<s;r++)arguments[r]&&to(arguments[r],n);return t}const Sw=(e,t,n,{allOwnKeys:r}={})=>(to(t,(s,o)=>{n&&At(s)?e[o]=Jh(s,n):e[o]=s},{allOwnKeys:r}),e),ww=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),Ew=(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},Cw=(e,t,n,r)=>{let s,o,i;const a={};if(t=t||{},e==null)return t;do{for(s=Object.getOwnPropertyNames(e),o=s.length;o-- >0;)i=s[o],(!r||r(i,e,t))&&!a[i]&&(t[i]=e[i],a[i]=!0);e=n!==!1&&Jl(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},xw=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return r!==-1&&r===n},Tw=e=>{if(!e)return null;if(ss(e))return e;let t=e.length;if(!eg(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},kw=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&Jl(Uint8Array)),Aw=(e,t)=>{const r=(e&&e[ki]).call(e);let s;for(;(s=r.next())&&!s.done;){const o=s.value;t.call(e,o[0],o[1])}},Pw=(e,t)=>{let n;const r=[];for(;(n=e.exec(t))!==null;)r.push(n);return r},Iw=tn("HTMLFormElement"),Ow=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,r,s){return r.toUpperCase()+s}),df=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),Rw=tn("RegExp"),rg=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};to(n,(s,o)=>{let i;(i=t(s,o,e))!==!1&&(r[o]=i||s)}),Object.defineProperties(e,r)},Lw=e=>{rg(e,(t,n)=>{if(At(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const r=e[n];if(At(r)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},Nw=(e,t)=>{const n={},r=s=>{s.forEach(o=>{n[o]=!0})};return ss(e)?r(e):r(String(e).split(t)),n},Dw=()=>{},Fw=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function Mw(e){return!!(e&&At(e.append)&&e[Zh]==="FormData"&&e[ki])}const Vw=e=>{const t=new Array(10),n=(r,s)=>{if(Ii(r)){if(t.indexOf(r)>=0)return;if(!("toJSON"in r)){t[s]=r;const o=ss(r)?[]:{};return to(r,(i,a)=>{const l=n(i,s+1);!$s(l)&&(o[a]=l)}),t[s]=void 0,o}}return r};return n(e,0)},$w=tn("AsyncFunction"),Bw=e=>e&&(Ii(e)||At(e))&&At(e.then)&&At(e.catch),sg=((e,t)=>e?setImmediate:t?((n,r)=>(hr.addEventListener("message",({source:s,data:o})=>{s===hr&&o===n&&r.length&&r.shift()()},!1),s=>{r.push(s),hr.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",At(hr.postMessage)),Uw=typeof queueMicrotask<"u"?queueMicrotask.bind(hr):typeof process<"u"&&process.nextTick||sg,Hw=e=>e!=null&&At(e[ki]),$={isArray:ss,isArrayBuffer:Qh,isBuffer:ow,isFormData:hw,isArrayBufferView:iw,isString:aw,isNumber:eg,isBoolean:lw,isObject:Ii,isPlainObject:Co,isReadableStream:vw,isRequest:pw,isResponse:yw,isHeaders:bw,isUndefined:$s,isDate:cw,isFile:uw,isBlob:fw,isRegExp:Rw,isFunction:At,isStream:mw,isURLSearchParams:gw,isTypedArray:kw,isFileList:dw,forEach:to,merge:qa,extend:Sw,trim:_w,stripBOM:ww,inherits:Ew,toFlatObject:Cw,kindOf:Ai,kindOfTest:tn,endsWith:xw,toArray:Tw,forEachEntry:Aw,matchAll:Pw,isHTMLForm:Iw,hasOwnProperty:df,hasOwnProp:df,reduceDescriptors:rg,freezeMethods:Lw,toObjectSet:Nw,toCamelCase:Ow,noop:Dw,toFiniteNumber:Fw,findKey:tg,global:hr,isContextDefined:ng,isSpecCompliantForm:Mw,toJSONObject:Vw,isAsyncFn:$w,isThenable:Bw,setImmediate:sg,asap:Uw,isIterable:Hw};function ve(e,t,n,r,s){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),s&&(this.response=s,this.status=s.status?s.status:null)}$.inherits(ve,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:$.toJSONObject(this.config),code:this.code,status:this.status}}});const og=ve.prototype,ig={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{ig[e]={value:e}});Object.defineProperties(ve,ig);Object.defineProperty(og,"isAxiosError",{value:!0});ve.from=(e,t,n,r,s,o)=>{const i=Object.create(og);return $.toFlatObject(e,i,function(l){return l!==Error.prototype},a=>a!=="isAxiosError"),ve.call(i,e.message,t,n,r,s),i.cause=e,i.name=e.name,o&&Object.assign(i,o),i};const jw=null;function Ya(e){return $.isPlainObject(e)||$.isArray(e)}function ag(e){return $.endsWith(e,"[]")?e.slice(0,-2):e}function mf(e,t,n){return e?e.concat(t).map(function(s,o){return s=ag(s),!n&&o?"["+s+"]":s}).join(n?".":""):t}function Ww(e){return $.isArray(e)&&!e.some(Ya)}const zw=$.toFlatObject($,{},null,function(t){return/^is[A-Z]/.test(t)});function Oi(e,t,n){if(!$.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=$.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(p,w){return!$.isUndefined(w[p])});const r=n.metaTokens,s=n.visitor||c,o=n.dots,i=n.indexes,l=(n.Blob||typeof Blob<"u"&&Blob)&&$.isSpecCompliantForm(t);if(!$.isFunction(s))throw new TypeError("visitor must be a function");function u(g){if(g===null)return"";if($.isDate(g))return g.toISOString();if($.isBoolean(g))return g.toString();if(!l&&$.isBlob(g))throw new ve("Blob is not supported. Use a Buffer instead.");return $.isArrayBuffer(g)||$.isTypedArray(g)?l&&typeof Blob=="function"?new Blob([g]):Buffer.from(g):g}function c(g,p,w){let h=g;if(g&&!w&&typeof g=="object"){if($.endsWith(p,"{}"))p=r?p:p.slice(0,-2),g=JSON.stringify(g);else if($.isArray(g)&&Ww(g)||($.isFileList(g)||$.endsWith(p,"[]"))&&(h=$.toArray(g)))return p=ag(p),h.forEach(function(b,y){!($.isUndefined(b)||b===null)&&t.append(i===!0?mf([p],y,o):i===null?p:p+"[]",u(b))}),!1}return Ya(g)?!0:(t.append(mf(w,p,o),u(g)),!1)}const f=[],d=Object.assign(zw,{defaultVisitor:c,convertValue:u,isVisitable:Ya});function m(g,p){if(!$.isUndefined(g)){if(f.indexOf(g)!==-1)throw Error("Circular reference detected in "+p.join("."));f.push(g),$.forEach(g,function(h,v){(!($.isUndefined(h)||h===null)&&s.call(t,h,$.isString(v)?v.trim():v,p,d))===!0&&m(h,p?p.concat(v):[v])}),f.pop()}}if(!$.isObject(e))throw new TypeError("data must be an object");return m(e),t}function hf(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(r){return t[r]})}function Zl(e,t){this._pairs=[],e&&Oi(e,this,t)}const lg=Zl.prototype;lg.append=function(t,n){this._pairs.push([t,n])};lg.toString=function(t){const n=t?function(r){return t.call(this,r,hf)}:hf;return this._pairs.map(function(s){return n(s[0])+"="+n(s[1])},"").join("&")};function Kw(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function cg(e,t,n){if(!t)return e;const r=n&&n.encode||Kw;$.isFunction(n)&&(n={serialize:n});const s=n&&n.serialize;let o;if(s?o=s(t,n):o=$.isURLSearchParams(t)?t.toString():new Zl(t,n).toString(r),o){const i=e.indexOf("#");i!==-1&&(e=e.slice(0,i)),e+=(e.indexOf("?")===-1?"?":"&")+o}return e}class gf{constructor(){this.handlers=[]}use(t,n,r){return this.handlers.push({fulfilled:t,rejected:n,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){$.forEach(this.handlers,function(r){r!==null&&t(r)})}}const ug={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Gw=typeof URLSearchParams<"u"?URLSearchParams:Zl,qw=typeof FormData<"u"?FormData:null,Yw=typeof Blob<"u"?Blob:null,Xw={isBrowser:!0,classes:{URLSearchParams:Gw,FormData:qw,Blob:Yw},protocols:["http","https","file","blob","url","data"]},Ql=typeof window<"u"&&typeof document<"u",Xa=typeof navigator=="object"&&navigator||void 0,Jw=Ql&&(!Xa||["ReactNative","NativeScript","NS"].indexOf(Xa.product)<0),Zw=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",Qw=Ql&&window.location.href||"http://localhost",eE=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Ql,hasStandardBrowserEnv:Jw,hasStandardBrowserWebWorkerEnv:Zw,navigator:Xa,origin:Qw},Symbol.toStringTag,{value:"Module"})),yt={...eE,...Xw};function tE(e,t){return Oi(e,new yt.classes.URLSearchParams,Object.assign({visitor:function(n,r,s,o){return yt.isNode&&$.isBuffer(n)?(this.append(r,n.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)}},t))}function nE(e){return $.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function rE(e){const t={},n=Object.keys(e);let r;const s=n.length;let o;for(r=0;r<s;r++)o=n[r],t[o]=e[o];return t}function fg(e){function t(n,r,s,o){let i=n[o++];if(i==="__proto__")return!0;const a=Number.isFinite(+i),l=o>=n.length;return i=!i&&$.isArray(s)?s.length:i,l?($.hasOwnProp(s,i)?s[i]=[s[i],r]:s[i]=r,!a):((!s[i]||!$.isObject(s[i]))&&(s[i]=[]),t(n,r,s[i],o)&&$.isArray(s[i])&&(s[i]=rE(s[i])),!a)}if($.isFormData(e)&&$.isFunction(e.entries)){const n={};return $.forEachEntry(e,(r,s)=>{t(nE(r),s,n,0)}),n}return null}function sE(e,t,n){if($.isString(e))try{return(t||JSON.parse)(e),$.trim(e)}catch(r){if(r.name!=="SyntaxError")throw r}return(n||JSON.stringify)(e)}const no={transitional:ug,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const r=n.getContentType()||"",s=r.indexOf("application/json")>-1,o=$.isObject(t);if(o&&$.isHTMLForm(t)&&(t=new FormData(t)),$.isFormData(t))return s?JSON.stringify(fg(t)):t;if($.isArrayBuffer(t)||$.isBuffer(t)||$.isStream(t)||$.isFile(t)||$.isBlob(t)||$.isReadableStream(t))return t;if($.isArrayBufferView(t))return t.buffer;if($.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let a;if(o){if(r.indexOf("application/x-www-form-urlencoded")>-1)return tE(t,this.formSerializer).toString();if((a=$.isFileList(t))||r.indexOf("multipart/form-data")>-1){const l=this.env&&this.env.FormData;return Oi(a?{"files[]":t}:t,l&&new l,this.formSerializer)}}return o||s?(n.setContentType("application/json",!1),sE(t)):t}],transformResponse:[function(t){const n=this.transitional||no.transitional,r=n&&n.forcedJSONParsing,s=this.responseType==="json";if($.isResponse(t)||$.isReadableStream(t))return t;if(t&&$.isString(t)&&(r&&!this.responseType||s)){const i=!(n&&n.silentJSONParsing)&&s;try{return JSON.parse(t)}catch(a){if(i)throw a.name==="SyntaxError"?ve.from(a,ve.ERR_BAD_RESPONSE,this,null,this.response):a}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:yt.classes.FormData,Blob:yt.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};$.forEach(["delete","get","head","post","put","patch"],e=>{no.headers[e]={}});const oE=$.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),iE=e=>{const t={};let n,r,s;return e&&e.split(`
`).forEach(function(i){s=i.indexOf(":"),n=i.substring(0,s).trim().toLowerCase(),r=i.substring(s+1).trim(),!(!n||t[n]&&oE[n])&&(n==="set-cookie"?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t},vf=Symbol("internals");function ds(e){return e&&String(e).trim().toLowerCase()}function xo(e){return e===!1||e==null?e:$.isArray(e)?e.map(xo):String(e)}function aE(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}const lE=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function ca(e,t,n,r,s){if($.isFunction(r))return r.call(this,t,n);if(s&&(t=n),!!$.isString(t)){if($.isString(r))return t.indexOf(r)!==-1;if($.isRegExp(r))return r.test(t)}}function cE(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,r)=>n.toUpperCase()+r)}function uE(e,t){const n=$.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(s,o,i){return this[r].call(this,t,s,o,i)},configurable:!0})})}let Pt=class{constructor(t){t&&this.set(t)}set(t,n,r){const s=this;function o(a,l,u){const c=ds(l);if(!c)throw new Error("header name must be a non-empty string");const f=$.findKey(s,c);(!f||s[f]===void 0||u===!0||u===void 0&&s[f]!==!1)&&(s[f||l]=xo(a))}const i=(a,l)=>$.forEach(a,(u,c)=>o(u,c,l));if($.isPlainObject(t)||t instanceof this.constructor)i(t,n);else if($.isString(t)&&(t=t.trim())&&!lE(t))i(iE(t),n);else if($.isObject(t)&&$.isIterable(t)){let a={},l,u;for(const c of t){if(!$.isArray(c))throw TypeError("Object iterator must return a key-value pair");a[u=c[0]]=(l=a[u])?$.isArray(l)?[...l,c[1]]:[l,c[1]]:c[1]}i(a,n)}else t!=null&&o(n,t,r);return this}get(t,n){if(t=ds(t),t){const r=$.findKey(this,t);if(r){const s=this[r];if(!n)return s;if(n===!0)return aE(s);if($.isFunction(n))return n.call(this,s,r);if($.isRegExp(n))return n.exec(s);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=ds(t),t){const r=$.findKey(this,t);return!!(r&&this[r]!==void 0&&(!n||ca(this,this[r],r,n)))}return!1}delete(t,n){const r=this;let s=!1;function o(i){if(i=ds(i),i){const a=$.findKey(r,i);a&&(!n||ca(r,r[a],a,n))&&(delete r[a],s=!0)}}return $.isArray(t)?t.forEach(o):o(t),s}clear(t){const n=Object.keys(this);let r=n.length,s=!1;for(;r--;){const o=n[r];(!t||ca(this,this[o],o,t,!0))&&(delete this[o],s=!0)}return s}normalize(t){const n=this,r={};return $.forEach(this,(s,o)=>{const i=$.findKey(r,o);if(i){n[i]=xo(s),delete n[o];return}const a=t?cE(o):String(o).trim();a!==o&&delete n[o],n[a]=xo(s),r[a]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return $.forEach(this,(r,s)=>{r!=null&&r!==!1&&(n[s]=t&&$.isArray(r)?r.join(", "):r)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const r=new this(t);return n.forEach(s=>r.set(s)),r}static accessor(t){const r=(this[vf]=this[vf]={accessors:{}}).accessors,s=this.prototype;function o(i){const a=ds(i);r[a]||(uE(s,i),r[a]=!0)}return $.isArray(t)?t.forEach(o):o(t),this}};Pt.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);$.reduceDescriptors(Pt.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(r){this[n]=r}}});$.freezeMethods(Pt);function ua(e,t){const n=this||no,r=t||n,s=Pt.from(r.headers);let o=r.data;return $.forEach(e,function(a){o=a.call(n,o,s.normalize(),t?t.status:void 0)}),s.normalize(),o}function dg(e){return!!(e&&e.__CANCEL__)}function os(e,t,n){ve.call(this,e??"canceled",ve.ERR_CANCELED,t,n),this.name="CanceledError"}$.inherits(os,ve,{__CANCEL__:!0});function mg(e,t,n){const r=n.config.validateStatus;!n.status||!r||r(n.status)?e(n):t(new ve("Request failed with status code "+n.status,[ve.ERR_BAD_REQUEST,ve.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function fE(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function dE(e,t){e=e||10;const n=new Array(e),r=new Array(e);let s=0,o=0,i;return t=t!==void 0?t:1e3,function(l){const u=Date.now(),c=r[o];i||(i=u),n[s]=l,r[s]=u;let f=o,d=0;for(;f!==s;)d+=n[f++],f=f%e;if(s=(s+1)%e,s===o&&(o=(o+1)%e),u-i<t)return;const m=c&&u-c;return m?Math.round(d*1e3/m):void 0}}function mE(e,t){let n=0,r=1e3/t,s,o;const i=(u,c=Date.now())=>{n=c,s=null,o&&(clearTimeout(o),o=null),e.apply(null,u)};return[(...u)=>{const c=Date.now(),f=c-n;f>=r?i(u,c):(s=u,o||(o=setTimeout(()=>{o=null,i(s)},r-f)))},()=>s&&i(s)]}const Yo=(e,t,n=3)=>{let r=0;const s=dE(50,250);return mE(o=>{const i=o.loaded,a=o.lengthComputable?o.total:void 0,l=i-r,u=s(l),c=i<=a;r=i;const f={loaded:i,total:a,progress:a?i/a:void 0,bytes:l,rate:u||void 0,estimated:u&&a&&c?(a-i)/u:void 0,event:o,lengthComputable:a!=null,[t?"download":"upload"]:!0};e(f)},n)},pf=(e,t)=>{const n=e!=null;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},yf=e=>(...t)=>$.asap(()=>e(...t)),hE=yt.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,yt.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(yt.origin),yt.navigator&&/(msie|trident)/i.test(yt.navigator.userAgent)):()=>!0,gE=yt.hasStandardBrowserEnv?{write(e,t,n,r,s,o){const i=[e+"="+encodeURIComponent(t)];$.isNumber(n)&&i.push("expires="+new Date(n).toGMTString()),$.isString(r)&&i.push("path="+r),$.isString(s)&&i.push("domain="+s),o===!0&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function vE(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function pE(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function hg(e,t,n){let r=!vE(t);return e&&(r||n==!1)?pE(e,t):t}const bf=e=>e instanceof Pt?{...e}:e;function Ar(e,t){t=t||{};const n={};function r(u,c,f,d){return $.isPlainObject(u)&&$.isPlainObject(c)?$.merge.call({caseless:d},u,c):$.isPlainObject(c)?$.merge({},c):$.isArray(c)?c.slice():c}function s(u,c,f,d){if($.isUndefined(c)){if(!$.isUndefined(u))return r(void 0,u,f,d)}else return r(u,c,f,d)}function o(u,c){if(!$.isUndefined(c))return r(void 0,c)}function i(u,c){if($.isUndefined(c)){if(!$.isUndefined(u))return r(void 0,u)}else return r(void 0,c)}function a(u,c,f){if(f in t)return r(u,c);if(f in e)return r(void 0,u)}const l={url:o,method:o,data:o,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:a,headers:(u,c,f)=>s(bf(u),bf(c),f,!0)};return $.forEach(Object.keys(Object.assign({},e,t)),function(c){const f=l[c]||s,d=f(e[c],t[c],c);$.isUndefined(d)&&f!==a||(n[c]=d)}),n}const gg=e=>{const t=Ar({},e);let{data:n,withXSRFToken:r,xsrfHeaderName:s,xsrfCookieName:o,headers:i,auth:a}=t;t.headers=i=Pt.from(i),t.url=cg(hg(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),a&&i.set("Authorization","Basic "+btoa((a.username||"")+":"+(a.password?unescape(encodeURIComponent(a.password)):"")));let l;if($.isFormData(n)){if(yt.hasStandardBrowserEnv||yt.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if((l=i.getContentType())!==!1){const[u,...c]=l?l.split(";").map(f=>f.trim()).filter(Boolean):[];i.setContentType([u||"multipart/form-data",...c].join("; "))}}if(yt.hasStandardBrowserEnv&&(r&&$.isFunction(r)&&(r=r(t)),r||r!==!1&&hE(t.url))){const u=s&&o&&gE.read(o);u&&i.set(s,u)}return t},yE=typeof XMLHttpRequest<"u",bE=yE&&function(e){return new Promise(function(n,r){const s=gg(e);let o=s.data;const i=Pt.from(s.headers).normalize();let{responseType:a,onUploadProgress:l,onDownloadProgress:u}=s,c,f,d,m,g;function p(){m&&m(),g&&g(),s.cancelToken&&s.cancelToken.unsubscribe(c),s.signal&&s.signal.removeEventListener("abort",c)}let w=new XMLHttpRequest;w.open(s.method.toUpperCase(),s.url,!0),w.timeout=s.timeout;function h(){if(!w)return;const b=Pt.from("getAllResponseHeaders"in w&&w.getAllResponseHeaders()),E={data:!a||a==="text"||a==="json"?w.responseText:w.response,status:w.status,statusText:w.statusText,headers:b,config:e,request:w};mg(function(k){n(k),p()},function(k){r(k),p()},E),w=null}"onloadend"in w?w.onloadend=h:w.onreadystatechange=function(){!w||w.readyState!==4||w.status===0&&!(w.responseURL&&w.responseURL.indexOf("file:")===0)||setTimeout(h)},w.onabort=function(){w&&(r(new ve("Request aborted",ve.ECONNABORTED,e,w)),w=null)},w.onerror=function(){r(new ve("Network Error",ve.ERR_NETWORK,e,w)),w=null},w.ontimeout=function(){let y=s.timeout?"timeout of "+s.timeout+"ms exceeded":"timeout exceeded";const E=s.transitional||ug;s.timeoutErrorMessage&&(y=s.timeoutErrorMessage),r(new ve(y,E.clarifyTimeoutError?ve.ETIMEDOUT:ve.ECONNABORTED,e,w)),w=null},o===void 0&&i.setContentType(null),"setRequestHeader"in w&&$.forEach(i.toJSON(),function(y,E){w.setRequestHeader(E,y)}),$.isUndefined(s.withCredentials)||(w.withCredentials=!!s.withCredentials),a&&a!=="json"&&(w.responseType=s.responseType),u&&([d,g]=Yo(u,!0),w.addEventListener("progress",d)),l&&w.upload&&([f,m]=Yo(l),w.upload.addEventListener("progress",f),w.upload.addEventListener("loadend",m)),(s.cancelToken||s.signal)&&(c=b=>{w&&(r(!b||b.type?new os(null,e,w):b),w.abort(),w=null)},s.cancelToken&&s.cancelToken.subscribe(c),s.signal&&(s.signal.aborted?c():s.signal.addEventListener("abort",c)));const v=fE(s.url);if(v&&yt.protocols.indexOf(v)===-1){r(new ve("Unsupported protocol "+v+":",ve.ERR_BAD_REQUEST,e));return}w.send(o||null)})},_E=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let r=new AbortController,s;const o=function(u){if(!s){s=!0,a();const c=u instanceof Error?u:this.reason;r.abort(c instanceof ve?c:new os(c instanceof Error?c.message:c))}};let i=t&&setTimeout(()=>{i=null,o(new ve(`timeout ${t} of ms exceeded`,ve.ETIMEDOUT))},t);const a=()=>{e&&(i&&clearTimeout(i),i=null,e.forEach(u=>{u.unsubscribe?u.unsubscribe(o):u.removeEventListener("abort",o)}),e=null)};e.forEach(u=>u.addEventListener("abort",o));const{signal:l}=r;return l.unsubscribe=()=>$.asap(a),l}},SE=function*(e,t){let n=e.byteLength;if(n<t){yield e;return}let r=0,s;for(;r<n;)s=r+t,yield e.slice(r,s),r=s},wE=async function*(e,t){for await(const n of EE(e))yield*SE(n,t)},EE=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:n,value:r}=await t.read();if(n)break;yield r}}finally{await t.cancel()}},_f=(e,t,n,r)=>{const s=wE(e,t);let o=0,i,a=l=>{i||(i=!0,r&&r(l))};return new ReadableStream({async pull(l){try{const{done:u,value:c}=await s.next();if(u){a(),l.close();return}let f=c.byteLength;if(n){let d=o+=f;n(d)}l.enqueue(new Uint8Array(c))}catch(u){throw a(u),u}},cancel(l){return a(l),s.return()}},{highWaterMark:2})},Ri=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",vg=Ri&&typeof ReadableStream=="function",CE=Ri&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),pg=(e,...t)=>{try{return!!e(...t)}catch{return!1}},xE=vg&&pg(()=>{let e=!1;const t=new Request(yt.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),Sf=64*1024,Ja=vg&&pg(()=>$.isReadableStream(new Response("").body)),Xo={stream:Ja&&(e=>e.body)};Ri&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!Xo[t]&&(Xo[t]=$.isFunction(e[t])?n=>n[t]():(n,r)=>{throw new ve(`Response type '${t}' is not supported`,ve.ERR_NOT_SUPPORT,r)})})})(new Response);const TE=async e=>{if(e==null)return 0;if($.isBlob(e))return e.size;if($.isSpecCompliantForm(e))return(await new Request(yt.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if($.isArrayBufferView(e)||$.isArrayBuffer(e))return e.byteLength;if($.isURLSearchParams(e)&&(e=e+""),$.isString(e))return(await CE(e)).byteLength},kE=async(e,t)=>{const n=$.toFiniteNumber(e.getContentLength());return n??TE(t)},AE=Ri&&(async e=>{let{url:t,method:n,data:r,signal:s,cancelToken:o,timeout:i,onDownloadProgress:a,onUploadProgress:l,responseType:u,headers:c,withCredentials:f="same-origin",fetchOptions:d}=gg(e);u=u?(u+"").toLowerCase():"text";let m=_E([s,o&&o.toAbortSignal()],i),g;const p=m&&m.unsubscribe&&(()=>{m.unsubscribe()});let w;try{if(l&&xE&&n!=="get"&&n!=="head"&&(w=await kE(c,r))!==0){let E=new Request(t,{method:"POST",body:r,duplex:"half"}),P;if($.isFormData(r)&&(P=E.headers.get("content-type"))&&c.setContentType(P),E.body){const[k,T]=pf(w,Yo(yf(l)));r=_f(E.body,Sf,k,T)}}$.isString(f)||(f=f?"include":"omit");const h="credentials"in Request.prototype;g=new Request(t,{...d,signal:m,method:n.toUpperCase(),headers:c.normalize().toJSON(),body:r,duplex:"half",credentials:h?f:void 0});let v=await fetch(g,d);const b=Ja&&(u==="stream"||u==="response");if(Ja&&(a||b&&p)){const E={};["status","statusText","headers"].forEach(A=>{E[A]=v[A]});const P=$.toFiniteNumber(v.headers.get("content-length")),[k,T]=a&&pf(P,Yo(yf(a),!0))||[];v=new Response(_f(v.body,Sf,k,()=>{T&&T(),p&&p()}),E)}u=u||"text";let y=await Xo[$.findKey(Xo,u)||"text"](v,e);return!b&&p&&p(),await new Promise((E,P)=>{mg(E,P,{data:y,headers:Pt.from(v.headers),status:v.status,statusText:v.statusText,config:e,request:g})})}catch(h){throw p&&p(),h&&h.name==="TypeError"&&/Load failed|fetch/i.test(h.message)?Object.assign(new ve("Network Error",ve.ERR_NETWORK,e,g),{cause:h.cause||h}):ve.from(h,h&&h.code,e,g)}}),Za={http:jw,xhr:bE,fetch:AE};$.forEach(Za,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const wf=e=>`- ${e}`,PE=e=>$.isFunction(e)||e===null||e===!1,yg={getAdapter:e=>{e=$.isArray(e)?e:[e];const{length:t}=e;let n,r;const s={};for(let o=0;o<t;o++){n=e[o];let i;if(r=n,!PE(n)&&(r=Za[(i=String(n)).toLowerCase()],r===void 0))throw new ve(`Unknown adapter '${i}'`);if(r)break;s[i||"#"+o]=r}if(!r){const o=Object.entries(s).map(([a,l])=>`adapter ${a} `+(l===!1?"is not supported by the environment":"is not available in the build"));let i=t?o.length>1?`since :
`+o.map(wf).join(`
`):" "+wf(o[0]):"as no adapter specified";throw new ve("There is no suitable adapter to dispatch the request "+i,"ERR_NOT_SUPPORT")}return r},adapters:Za};function fa(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new os(null,e)}function Ef(e){return fa(e),e.headers=Pt.from(e.headers),e.data=ua.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),yg.getAdapter(e.adapter||no.adapter)(e).then(function(r){return fa(e),r.data=ua.call(e,e.transformResponse,r),r.headers=Pt.from(r.headers),r},function(r){return dg(r)||(fa(e),r&&r.response&&(r.response.data=ua.call(e,e.transformResponse,r.response),r.response.headers=Pt.from(r.response.headers))),Promise.reject(r)})}const bg="1.10.0",Li={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{Li[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}});const Cf={};Li.transitional=function(t,n,r){function s(o,i){return"[Axios v"+bg+"] Transitional option '"+o+"'"+i+(r?". "+r:"")}return(o,i,a)=>{if(t===!1)throw new ve(s(i," has been removed"+(n?" in "+n:"")),ve.ERR_DEPRECATED);return n&&!Cf[i]&&(Cf[i]=!0,console.warn(s(i," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(o,i,a):!0}};Li.spelling=function(t){return(n,r)=>(console.warn(`${r} is likely a misspelling of ${t}`),!0)};function IE(e,t,n){if(typeof e!="object")throw new ve("options must be an object",ve.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let s=r.length;for(;s-- >0;){const o=r[s],i=t[o];if(i){const a=e[o],l=a===void 0||i(a,o,e);if(l!==!0)throw new ve("option "+o+" must be "+l,ve.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new ve("Unknown option "+o,ve.ERR_BAD_OPTION)}}const To={assertOptions:IE,validators:Li},sn=To.validators;let _r=class{constructor(t){this.defaults=t||{},this.interceptors={request:new gf,response:new gf}}async request(t,n){try{return await this._request(t,n)}catch(r){if(r instanceof Error){let s={};Error.captureStackTrace?Error.captureStackTrace(s):s=new Error;const o=s.stack?s.stack.replace(/^.+\n/,""):"";try{r.stack?o&&!String(r.stack).endsWith(o.replace(/^.+\n.+\n/,""))&&(r.stack+=`
`+o):r.stack=o}catch{}}throw r}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=Ar(this.defaults,n);const{transitional:r,paramsSerializer:s,headers:o}=n;r!==void 0&&To.assertOptions(r,{silentJSONParsing:sn.transitional(sn.boolean),forcedJSONParsing:sn.transitional(sn.boolean),clarifyTimeoutError:sn.transitional(sn.boolean)},!1),s!=null&&($.isFunction(s)?n.paramsSerializer={serialize:s}:To.assertOptions(s,{encode:sn.function,serialize:sn.function},!0)),n.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?n.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:n.allowAbsoluteUrls=!0),To.assertOptions(n,{baseUrl:sn.spelling("baseURL"),withXsrfToken:sn.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let i=o&&$.merge(o.common,o[n.method]);o&&$.forEach(["delete","get","head","post","put","patch","common"],g=>{delete o[g]}),n.headers=Pt.concat(i,o);const a=[];let l=!0;this.interceptors.request.forEach(function(p){typeof p.runWhen=="function"&&p.runWhen(n)===!1||(l=l&&p.synchronous,a.unshift(p.fulfilled,p.rejected))});const u=[];this.interceptors.response.forEach(function(p){u.push(p.fulfilled,p.rejected)});let c,f=0,d;if(!l){const g=[Ef.bind(this),void 0];for(g.unshift.apply(g,a),g.push.apply(g,u),d=g.length,c=Promise.resolve(n);f<d;)c=c.then(g[f++],g[f++]);return c}d=a.length;let m=n;for(f=0;f<d;){const g=a[f++],p=a[f++];try{m=g(m)}catch(w){p.call(this,w);break}}try{c=Ef.call(this,m)}catch(g){return Promise.reject(g)}for(f=0,d=u.length;f<d;)c=c.then(u[f++],u[f++]);return c}getUri(t){t=Ar(this.defaults,t);const n=hg(t.baseURL,t.url,t.allowAbsoluteUrls);return cg(n,t.params,t.paramsSerializer)}};$.forEach(["delete","get","head","options"],function(t){_r.prototype[t]=function(n,r){return this.request(Ar(r||{},{method:t,url:n,data:(r||{}).data}))}});$.forEach(["post","put","patch"],function(t){function n(r){return function(o,i,a){return this.request(Ar(a||{},{method:t,headers:r?{"Content-Type":"multipart/form-data"}:{},url:o,data:i}))}}_r.prototype[t]=n(),_r.prototype[t+"Form"]=n(!0)});let OE=class _g{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(o){n=o});const r=this;this.promise.then(s=>{if(!r._listeners)return;let o=r._listeners.length;for(;o-- >0;)r._listeners[o](s);r._listeners=null}),this.promise.then=s=>{let o;const i=new Promise(a=>{r.subscribe(a),o=a}).then(s);return i.cancel=function(){r.unsubscribe(o)},i},t(function(o,i,a){r.reason||(r.reason=new os(o,i,a),n(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const t=new AbortController,n=r=>{t.abort(r)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new _g(function(s){t=s}),cancel:t}}};function RE(e){return function(n){return e.apply(null,n)}}function LE(e){return $.isObject(e)&&e.isAxiosError===!0}const Qa={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Qa).forEach(([e,t])=>{Qa[t]=e});function Sg(e){const t=new _r(e),n=Jh(_r.prototype.request,t);return $.extend(n,_r.prototype,t,{allOwnKeys:!0}),$.extend(n,t,null,{allOwnKeys:!0}),n.create=function(s){return Sg(Ar(e,s))},n}const Qe=Sg(no);Qe.Axios=_r;Qe.CanceledError=os;Qe.CancelToken=OE;Qe.isCancel=dg;Qe.VERSION=bg;Qe.toFormData=Oi;Qe.AxiosError=ve;Qe.Cancel=Qe.CanceledError;Qe.all=function(t){return Promise.all(t)};Qe.spread=RE;Qe.isAxiosError=LE;Qe.mergeConfig=Ar;Qe.AxiosHeaders=Pt;Qe.formToJSON=e=>fg($.isHTMLForm(e)?new FormData(e):e);Qe.getAdapter=yg.getAdapter;Qe.HttpStatusCode=Qa;Qe.default=Qe;const{Axios:bT,AxiosError:_T,CanceledError:ST,isCancel:wT,CancelToken:ET,VERSION:CT,all:xT,Cancel:TT,isAxiosError:kT,spread:AT,toFormData:PT,AxiosHeaders:IT,HttpStatusCode:OT,formToJSON:RT,getAdapter:LT,mergeConfig:NT}=Qe,ec=e=>{const t=Qe.create({baseURL:"/nl/gpt-chat",timeout:2e4});return t.interceptors.response.use(n=>n,n=>(n.response?n.response.status===401?e.push({name:"/login"}):console.error(`API error: ${n.response.status} - ${n.response.data.message}`):console.error("API error: ",n.message),Promise.reject(n))),t},wg=Symbol.for("vuetify:form"),NE=te({disabled:Boolean,fastFail:Boolean,readonly:Boolean,modelValue:{type:Boolean,default:null},validateOn:{type:String,default:"input"}},"form");function DE(e){const t=Vt(e,"modelValue"),n=X(()=>e.disabled),r=X(()=>e.readonly),s=Ae(!1),o=J([]),i=J([]);async function a(){const c=[];let f=!0;i.value=[],s.value=!0;for(const d of o.value){const m=await d.validate();if(m.length>0&&(f=!1,c.push({id:d.id,errorMessages:m})),!f&&e.fastFail)break}return i.value=c,s.value=!1,{valid:f,errors:i.value}}function l(){o.value.forEach(c=>c.reset())}function u(){o.value.forEach(c=>c.resetValidation())}return le(o,()=>{let c=0,f=0;const d=[];for(const m of o.value)m.isValid===!1?(f++,d.push({id:m.id,errorMessages:m.errorMessages})):m.isValid===!0&&c++;i.value=d,t.value=f>0?!1:c===o.value.length?!0:null},{deep:!0,flush:"post"}),Ft(wg,{register:c=>{let{id:f,vm:d,validate:m,reset:g,resetValidation:p}=c;o.value.some(w=>w.id===f),o.value.push({id:f,validate:m,reset:g,resetValidation:p,vm:ii(d),isValid:null,errorMessages:[]})},unregister:c=>{o.value=o.value.filter(f=>f.id!==c)},update:(c,f,d)=>{const m=o.value.find(g=>g.id===c);m&&(m.isValid=f,m.errorMessages=d)},isDisabled:n,isReadonly:r,isValidating:s,isValid:t,items:o,validateOn:X(()=>e.validateOn)}),{errors:i,isDisabled:n,isReadonly:r,isValidating:s,isValid:t,items:o,validate:a,reset:l,resetValidation:u}}function FE(e){const t=Le(wg,null);return{...t,isReadonly:U(()=>!!(e?.readonly??t?.isReadonly.value)),isDisabled:U(()=>!!(e?.disabled??t?.isDisabled.value))}}const ME=te({...Ie(),...NE()},"VForm"),VE=ge()({name:"VForm",props:ME(),emits:{"update:modelValue":e=>!0,submit:e=>!0},setup(e,t){let{slots:n,emit:r}=t;const s=DE(e),o=J();function i(l){l.preventDefault(),s.reset()}function a(l){const u=l,c=s.validate();u.then=c.then.bind(c),u.catch=c.catch.bind(c),u.finally=c.finally.bind(c),r("submit",u),u.defaultPrevented||c.then(f=>{let{valid:d}=f;d&&o.value?.submit()}),u.preventDefault()}return ye(()=>N("form",{ref:o,class:de(["v-form",e.class]),style:be(e.style),novalidate:!0,onReset:i,onSubmit:a},[n.default?.(s)])),eo(s,o)}}),$E=te({disabled:Boolean,group:Boolean,hideOnLeave:Boolean,leaveAbsolute:Boolean,mode:String,origin:String},"transition");function Ut(e,t,n){return ge()({name:e,props:$E({mode:n,origin:t}),setup(r,s){let{slots:o}=s;const i={onBeforeEnter(a){r.origin&&(a.style.transformOrigin=r.origin)},onLeave(a){if(r.leaveAbsolute){const{offsetTop:l,offsetLeft:u,offsetWidth:c,offsetHeight:f}=a;a._transitionInitialStyles={position:a.style.position,top:a.style.top,left:a.style.left,width:a.style.width,height:a.style.height},a.style.position="absolute",a.style.top=`${l}px`,a.style.left=`${u}px`,a.style.width=`${c}px`,a.style.height=`${f}px`}r.hideOnLeave&&a.style.setProperty("display","none","important")},onAfterLeave(a){if(r.leaveAbsolute&&a?._transitionInitialStyles){const{position:l,top:u,left:c,width:f,height:d}=a._transitionInitialStyles;delete a._transitionInitialStyles,a.style.position=l||"",a.style.top=u||"",a.style.left=c||"",a.style.width=f||"",a.style.height=d||""}}};return()=>{const a=r.group?Nl:wr;return vn(a,{name:r.disabled?"":e,css:!r.disabled,...r.group?void 0:{mode:r.mode},...r.disabled?{}:i},o.default)}}})}function Eg(e,t){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:"in-out";return ge()({name:e,props:{mode:{type:String,default:n},disabled:Boolean,group:Boolean},setup(r,s){let{slots:o}=s;const i=r.group?Nl:wr;return()=>vn(i,{name:r.disabled?"":e,css:!r.disabled,...r.disabled?{}:t},o.default)}})}function Cg(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"";const n=(arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1)?"width":"height",r=It(`offset-${n}`);return{onBeforeEnter(i){i._parent=i.parentNode,i._initialStyle={transition:i.style.transition,overflow:i.style.overflow,[n]:i.style[n]}},onEnter(i){const a=i._initialStyle;if(!a)return;i.style.setProperty("transition","none","important"),i.style.overflow="hidden";const l=`${i[r]}px`;i.style[n]="0",i.offsetHeight,i.style.transition=a.transition,e&&i._parent&&i._parent.classList.add(e),requestAnimationFrame(()=>{i.style[n]=l})},onAfterEnter:o,onEnterCancelled:o,onLeave(i){i._initialStyle={transition:"",overflow:i.style.overflow,[n]:i.style[n]},i.style.overflow="hidden",i.style[n]=`${i[r]}px`,i.offsetHeight,requestAnimationFrame(()=>i.style[n]="0")},onAfterLeave:s,onLeaveCancelled:s};function s(i){e&&i._parent&&i._parent.classList.remove(e),o(i)}function o(i){if(!i._initialStyle)return;const a=i._initialStyle[n];i.style.overflow=i._initialStyle.overflow,a!=null&&(i.style[n]=a),delete i._initialStyle}}const BE=te({target:[Object,Array]},"v-dialog-transition"),da=new WeakMap,UE=ge()({name:"VDialogTransition",props:BE(),setup(e,t){let{slots:n}=t;const r={onBeforeEnter(s){s.style.pointerEvents="none",s.style.visibility="hidden"},async onEnter(s,o){await new Promise(m=>requestAnimationFrame(m)),await new Promise(m=>requestAnimationFrame(m)),s.style.visibility="";const i=Tf(e.target,s),{x:a,y:l,sx:u,sy:c,speed:f}=i;da.set(s,i);const d=Hr(s,[{transform:`translate(${a}px, ${l}px) scale(${u}, ${c})`,opacity:0},{}],{duration:225*f,easing:$b});xf(s)?.forEach(m=>{Hr(m,[{opacity:0},{opacity:0,offset:.33},{}],{duration:225*2*f,easing:Uo})}),d.finished.then(()=>o())},onAfterEnter(s){s.style.removeProperty("pointer-events")},onBeforeLeave(s){s.style.pointerEvents="none"},async onLeave(s,o){await new Promise(m=>requestAnimationFrame(m));let i;!da.has(s)||Array.isArray(e.target)||e.target.offsetParent||e.target.getClientRects().length?i=Tf(e.target,s):i=da.get(s);const{x:a,y:l,sx:u,sy:c,speed:f}=i;Hr(s,[{},{transform:`translate(${a}px, ${l}px) scale(${u}, ${c})`,opacity:0}],{duration:125*f,easing:Bb}).finished.then(()=>o()),xf(s)?.forEach(m=>{Hr(m,[{},{opacity:0,offset:.2},{opacity:0}],{duration:125*2*f,easing:Uo})})},onAfterLeave(s){s.style.removeProperty("pointer-events")}};return()=>e.target?R(wr,Pe({name:"dialog-transition"},r,{css:!1}),n):R(wr,{name:"dialog-transition"},n)}});function xf(e){const t=e.querySelector(":scope > .v-card, :scope > .v-sheet, :scope > .v-list")?.children;return t&&[...t]}function Tf(e,t){const n=zm(e),r=Vl(t),[s,o]=getComputedStyle(t).transformOrigin.split(" ").map(h=>parseFloat(h)),[i,a]=getComputedStyle(t).getPropertyValue("--v-overlay-anchor-origin").split(" ");let l=n.left+n.width/2;i==="left"||a==="left"?l-=n.width/2:(i==="right"||a==="right")&&(l+=n.width/2);let u=n.top+n.height/2;i==="top"||a==="top"?u-=n.height/2:(i==="bottom"||a==="bottom")&&(u+=n.height/2);const c=n.width/r.width,f=n.height/r.height,d=Math.max(1,c,f),m=c/d||0,g=f/d||0,p=r.width*r.height/(window.innerWidth*window.innerHeight),w=p>.12?Math.min(1.5,(p-.12)*10+1):1;return{x:l-(s+r.left),y:u-(o+r.top),sx:m,sy:g,speed:w}}Ut("fab-transition","center center","out-in");Ut("dialog-bottom-transition");Ut("dialog-top-transition");Ut("fade-transition");Ut("scale-transition");Ut("scroll-x-transition");Ut("scroll-x-reverse-transition");Ut("scroll-y-transition");Ut("scroll-y-reverse-transition");Ut("slide-x-transition");Ut("slide-x-reverse-transition");const xg=Ut("slide-y-transition");Ut("slide-y-reverse-transition");const Tg=Eg("expand-transition",Cg()),HE=Eg("expand-x-transition",Cg("",!0)),jE=te({active:Boolean,disabled:Boolean,max:[Number,String],value:{type:[Number,String],default:0},...Ie(),...Ei({transition:{component:xg}})},"VCounter"),WE=ge()({name:"VCounter",functional:!0,props:jE(),setup(e,t){let{slots:n}=t;const r=X(()=>e.max?`${e.value} / ${e.max}`:String(e.value));return ye(()=>R(mr,{transition:e.transition},{default:()=>[Kt(N("div",{class:de(["v-counter",{"text-error":e.max&&!e.disabled&&parseFloat(e.value)>parseFloat(e.max)},e.class]),style:be(e.style)},[n.default?n.default({counter:r.value,max:e.max,value:e.value}):r.value]),[[Ys,e.active]])]})),{}}}),zE=te({text:String,onClick:An(),...Ie(),...ct()},"VLabel"),KE=ge()({name:"VLabel",props:zE(),setup(e,t){let{slots:n}=t;return ye(()=>N("label",{class:de(["v-label",{"v-label--clickable":!!e.onClick},e.class]),style:be(e.style),onClick:e.onClick},[e.text,n.default?.()])),{}}}),GE=te({floating:Boolean,...Ie()},"VFieldLabel"),mo=ge()({name:"VFieldLabel",props:GE(),setup(e,t){let{slots:n}=t;return ye(()=>R(KE,{class:de(["v-field-label",{"v-field-label--floating":e.floating},e.class]),style:be(e.style),"aria-hidden":e.floating||void 0},n)),{}}});function kg(e){const{t}=rh();function n(r){let{name:s,color:o,...i}=r;const a={prepend:"prependAction",prependInner:"prependAction",append:"appendAction",appendInner:"appendAction",clear:"clear"}[s],l=e[`onClick:${s}`];function u(f){f.key!=="Enter"&&f.key!==" "||(f.preventDefault(),f.stopPropagation(),jm(l,new PointerEvent("click",f)))}const c=l&&a?t(`$vuetify.input.${a}`,e.label??""):void 0;return R(_t,Pe({icon:e[`${s}Icon`],"aria-label":c,onClick:l,onKeydown:u,color:o},i),null)}return{InputIcon:n}}const Ag=te({focused:Boolean,"onUpdate:focused":An()},"focus");function Pg(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:pn();const n=Vt(e,"focused"),r=X(()=>({[`${t}--focused`]:n.value}));function s(){n.value=!0}function o(){n.value=!1}return{focusClasses:r,isFocused:n,focus:s,blur:o}}const qE=["underlined","outlined","filled","solo","solo-inverted","solo-filled","plain"],Ig=te({appendInnerIcon:it,bgColor:String,clearable:Boolean,clearIcon:{type:it,default:"$clear"},active:Boolean,centerAffix:{type:Boolean,default:void 0},color:String,baseColor:String,dirty:Boolean,disabled:{type:Boolean,default:null},glow:Boolean,error:Boolean,flat:Boolean,iconColor:[Boolean,String],label:String,persistentClear:Boolean,prependInnerIcon:it,reverse:Boolean,singleLine:Boolean,variant:{type:String,default:"filled",validator:e=>qE.includes(e)},"onClick:clear":An(),"onClick:appendInner":An(),"onClick:prependInner":An(),...Ie(),...zl(),...$t(),...ct()},"VField"),kf=ge()({name:"VField",inheritAttrs:!1,props:{id:String,...Ag(),...Ig()},emits:{"update:focused":e=>!0,"update:modelValue":e=>!0},setup(e,t){let{attrs:n,emit:r,slots:s}=t;const{themeClasses:o}=ht(e),{loaderClasses:i}=Kl(e),{focusClasses:a,isFocused:l,focus:u,blur:c}=Pg(e),{InputIcon:f}=kg(e),{roundedClasses:d}=Bt(e),{rtlClasses:m}=Pr(),g=X(()=>e.dirty||e.active),p=X(()=>!!(e.label||s.label)),w=X(()=>!e.singleLine&&p.value),h=Ks(),v=U(()=>e.id||`input-${h}`),b=X(()=>`${v.value}-messages`),y=J(),E=J(),P=J(),k=U(()=>["plain","underlined"].includes(e.variant)),T=U(()=>e.error||e.disabled?void 0:g.value&&l.value?e.color:e.baseColor),A=U(()=>{if(!(!e.iconColor||e.glow&&!l.value))return e.iconColor===!0?T.value:e.iconColor}),{backgroundColorClasses:H,backgroundColorStyles:V}=mn(()=>e.bgColor),{textColorClasses:D,textColorStyles:G}=xr(T);le(g,oe=>{if(w.value){const Z=y.value.$el,re=E.value.$el;requestAnimationFrame(()=>{const pe=Vl(Z),ue=re.getBoundingClientRect(),xe=ue.x-pe.x,_e=ue.y-pe.y-(pe.height/2-ue.height/2),Me=ue.width/.75,Ge=Math.abs(Me-pe.width)>1?{maxWidth:me(Me)}:void 0,Se=getComputedStyle(Z),Ne=getComputedStyle(re),F=parseFloat(Se.transitionDuration)*1e3||150,q=parseFloat(Ne.getPropertyValue("--v-field-label-scale")),Y=Ne.getPropertyValue("color");Z.style.visibility="visible",re.style.visibility="hidden",Hr(Z,{transform:`translate(${xe}px, ${_e}px) scale(${q})`,color:Y,...Ge},{duration:F,easing:Uo,direction:oe?"normal":"reverse"}).finished.then(()=>{Z.style.removeProperty("visibility"),re.style.removeProperty("visibility")})})}},{flush:"post"});const ee=U(()=>({isActive:g,isFocused:l,controlRef:P,blur:c,focus:u}));function ne(oe){oe.target!==document.activeElement&&oe.preventDefault()}return ye(()=>{const oe=e.variant==="outlined",Z=!!(s["prepend-inner"]||e.prependInnerIcon),re=!!(e.clearable||s.clear)&&!e.disabled,pe=!!(s["append-inner"]||e.appendInnerIcon||re),ue=()=>s.label?s.label({...ee.value,label:e.label,props:{for:v.value}}):e.label;return N("div",Pe({class:["v-field",{"v-field--active":g.value,"v-field--appended":pe,"v-field--center-affix":e.centerAffix??!k.value,"v-field--disabled":e.disabled,"v-field--dirty":e.dirty,"v-field--error":e.error,"v-field--glow":e.glow,"v-field--flat":e.flat,"v-field--has-background":!!e.bgColor,"v-field--persistent-clear":e.persistentClear,"v-field--prepended":Z,"v-field--reverse":e.reverse,"v-field--single-line":e.singleLine,"v-field--no-label":!ue(),[`v-field--variant-${e.variant}`]:!0},o.value,H.value,a.value,i.value,d.value,m.value,e.class],style:[V.value,e.style],onClick:ne},n),[N("div",{class:"v-field__overlay"},null),R(ph,{name:"v-field",active:!!e.loading,color:e.error?"error":typeof e.loading=="string"?e.loading:e.color},{default:s.loader}),Z&&N("div",{key:"prepend",class:"v-field__prepend-inner"},[e.prependInnerIcon&&R(f,{key:"prepend-icon",name:"prependInner",color:A.value},null),s["prepend-inner"]?.(ee.value)]),N("div",{class:"v-field__field","data-no-activator":""},[["filled","solo","solo-inverted","solo-filled"].includes(e.variant)&&w.value&&R(mo,{key:"floating-label",ref:E,class:de([D.value]),floating:!0,for:v.value,style:be(G.value)},{default:()=>[ue()]}),p.value&&R(mo,{key:"label",ref:y,for:v.value},{default:()=>[ue()]}),s.default?.({...ee.value,props:{id:v.value,class:"v-field__input","aria-describedby":b.value},focus:u,blur:c})??N("div",{id:v.value,class:"v-field__input","aria-describedby":b.value},null)]),re&&R(HE,{key:"clear"},{default:()=>[Kt(N("div",{class:"v-field__clearable",onMousedown:xe=>{xe.preventDefault(),xe.stopPropagation()}},[R(Ye,{defaults:{VIcon:{icon:e.clearIcon}}},{default:()=>[s.clear?s.clear({...ee.value,props:{onFocus:u,onBlur:c,onClick:e["onClick:clear"],tabindex:-1}}):R(f,{name:"clear",onFocus:u,onBlur:c,tabindex:-1},null)]})]),[[Ys,e.dirty]])]}),pe&&N("div",{key:"append",class:"v-field__append-inner"},[s["append-inner"]?.(ee.value),e.appendInnerIcon&&R(f,{key:"append-icon",name:"appendInner",color:A.value},null)]),N("div",{class:de(["v-field__outline",D.value]),style:be(G.value)},[oe&&N(ke,null,[N("div",{class:"v-field__outline__start"},null),w.value&&N("div",{class:"v-field__outline__notch"},[R(mo,{ref:E,floating:!0,for:v.value},{default:()=>[ue()]})]),N("div",{class:"v-field__outline__end"},null)]),k.value&&w.value&&R(mo,{ref:E,floating:!0,for:v.value},{default:()=>[ue()]})])])}),{controlRef:P,fieldIconColor:A}}}),YE=te({active:Boolean,color:String,messages:{type:[Array,String],default:()=>[]},...Ie(),...Ei({transition:{component:xg,leaveAbsolute:!0,group:!0}})},"VMessages"),XE=ge()({name:"VMessages",props:YE(),setup(e,t){let{slots:n}=t;const r=U(()=>yr(e.messages)),{textColorClasses:s,textColorStyles:o}=xr(()=>e.color);return ye(()=>R(mr,{transition:e.transition,tag:"div",class:de(["v-messages",s.value,e.class]),style:be([o.value,e.style])},{default:()=>[e.active&&r.value.map((i,a)=>N("div",{class:"v-messages__message",key:`${a}-${r.value}`},[n.message?n.message({message:i}):i]))]})),{}}}),JE=Symbol.for("vuetify:rules");function ZE(e){const t=Le(JE,null);return t?t(e):X(e)}const QE=te({disabled:{type:Boolean,default:null},error:Boolean,errorMessages:{type:[Array,String],default:()=>[]},maxErrors:{type:[Number,String],default:1},name:String,label:String,readonly:{type:Boolean,default:null},rules:{type:Array,default:()=>[]},modelValue:null,validateOn:String,validationValue:null,...Ag()},"validation");function eC(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:pn(),n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:Ks();const r=Vt(e,"modelValue"),s=U(()=>e.validationValue===void 0?r.value:e.validationValue),o=FE(e),i=ZE(()=>e.rules),a=J([]),l=Ae(!0),u=U(()=>!!(yr(r.value===""?null:r.value).length||yr(s.value===""?null:s.value).length)),c=U(()=>e.errorMessages?.length?yr(e.errorMessages).concat(a.value).slice(0,Math.max(0,Number(e.maxErrors))):a.value),f=U(()=>{let y=(e.validateOn??o.validateOn?.value)||"input";y==="lazy"&&(y="input lazy"),y==="eager"&&(y="input eager");const E=new Set(y?.split(" ")??[]);return{input:E.has("input"),blur:E.has("blur")||E.has("input")||E.has("invalid-input"),invalidInput:E.has("invalid-input"),lazy:E.has("lazy"),eager:E.has("eager")}}),d=U(()=>e.error||e.errorMessages?.length?!1:e.rules.length?l.value?a.value.length||f.value.lazy?null:!0:!a.value.length:!0),m=Ae(!1),g=U(()=>({[`${t}--error`]:d.value===!1,[`${t}--dirty`]:u.value,[`${t}--disabled`]:o.isDisabled.value,[`${t}--readonly`]:o.isReadonly.value})),p=dt("validation"),w=U(()=>e.name??ot(n));Gs(()=>{o.register?.({id:w.value,vm:p,validate:b,reset:h,resetValidation:v})}),Qt(()=>{o.unregister?.(w.value)}),Gt(async()=>{f.value.lazy||await b(!f.value.eager),o.update?.(w.value,d.value,c.value)}),Cr(()=>f.value.input||f.value.invalidInput&&d.value===!1,()=>{le(s,()=>{if(s.value!=null)b();else if(e.focused){const y=le(()=>e.focused,E=>{E||b(),y()})}})}),Cr(()=>f.value.blur,()=>{le(()=>e.focused,y=>{y||b()})}),le([d,c],()=>{o.update?.(w.value,d.value,c.value)});async function h(){r.value=null,await bt(),await v()}async function v(){l.value=!0,f.value.lazy?a.value=[]:await b(!f.value.eager)}async function b(){let y=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;const E=[];m.value=!0;for(const P of i.value){if(E.length>=Number(e.maxErrors??1))break;const T=await(typeof P=="function"?P:()=>P)(s.value);if(T!==!0){if(T!==!1&&typeof T!="string"){console.warn(`${T} is not a valid value. Rule functions must return boolean true or a string.`);continue}E.push(T||"")}}return a.value=E,m.value=!1,l.value=y,a.value}return{errorMessages:c,isDirty:u,isDisabled:o.isDisabled,isReadonly:o.isReadonly,isPristine:l,isValid:d,isValidating:m,reset:h,resetValidation:v,validate:b,validationClasses:g}}const Og=te({id:String,appendIcon:it,baseColor:String,centerAffix:{type:Boolean,default:!0},color:String,glow:Boolean,iconColor:[Boolean,String],prependIcon:it,hideDetails:[Boolean,String],hideSpinButtons:Boolean,hint:String,persistentHint:Boolean,messages:{type:[Array,String],default:()=>[]},direction:{type:String,default:"horizontal",validator:e=>["horizontal","vertical"].includes(e)},"onClick:prepend":An(),"onClick:append":An(),...Ie(),...Zn(),...gi(tr(),["maxWidth","minWidth","width"]),...ct(),...QE()},"VInput"),Af=ge()({name:"VInput",props:{...Og()},emits:{"update:modelValue":e=>!0},setup(e,t){let{attrs:n,slots:r,emit:s}=t;const{densityClasses:o}=Ir(e),{dimensionStyles:i}=nr(e),{themeClasses:a}=ht(e),{rtlClasses:l}=Pr(),{InputIcon:u}=kg(e),c=Ks(),f=U(()=>e.id||`input-${c}`),d=U(()=>`${f.value}-messages`),{errorMessages:m,isDirty:g,isDisabled:p,isReadonly:w,isPristine:h,isValid:v,isValidating:b,reset:y,resetValidation:E,validate:P,validationClasses:k}=eC(e,"v-input",f),T=U(()=>({id:f,messagesId:d,isDirty:g,isDisabled:p,isReadonly:w,isPristine:h,isValid:v,isValidating:b,reset:y,resetValidation:E,validate:P})),A=X(()=>e.error||e.disabled?void 0:e.focused?e.color:e.baseColor),H=X(()=>{if(e.iconColor)return e.iconColor===!0?A.value:e.iconColor}),V=U(()=>e.errorMessages?.length||!h.value&&m.value.length?m.value:e.hint&&(e.persistentHint||e.focused)?e.hint:e.messages);return ye(()=>{const D=!!(r.prepend||e.prependIcon),G=!!(r.append||e.appendIcon),ee=V.value.length>0,ne=!e.hideDetails||e.hideDetails==="auto"&&(ee||!!r.details);return N("div",{class:de(["v-input",`v-input--${e.direction}`,{"v-input--center-affix":e.centerAffix,"v-input--focused":e.focused,"v-input--glow":e.glow,"v-input--hide-spin-buttons":e.hideSpinButtons},o.value,a.value,l.value,k.value,e.class]),style:be([i.value,e.style])},[D&&N("div",{key:"prepend",class:"v-input__prepend"},[r.prepend?.(T.value),e.prependIcon&&R(u,{key:"prepend-icon",name:"prepend",color:H.value},null)]),r.default&&N("div",{class:"v-input__control"},[r.default?.(T.value)]),G&&N("div",{key:"append",class:"v-input__append"},[e.appendIcon&&R(u,{key:"append-icon",name:"append",color:H.value},null),r.append?.(T.value)]),ne&&N("div",{id:d.value,class:"v-input__details",role:"alert","aria-live":"polite"},[R(XE,{active:ee,messages:V.value},{message:r.message}),r.details?.(T.value)])])}),{reset:y,resetValidation:E,validate:P,isValid:v,errorMessages:m}}});function tC(e){function t(n,r){!e.autofocus||!n||r[0].target?.focus?.()}return{onIntersect:t}}function nC(e,t){if(!Dl)return;const n=t.modifiers||{},r=t.value,{handler:s,options:o}=typeof r=="object"?r:{handler:r,options:{}},i=new IntersectionObserver(function(){let a=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],l=arguments.length>1?arguments[1]:void 0;const u=e._observe?.[t.instance.$.uid];if(!u)return;const c=a.some(f=>f.isIntersecting);s&&(!n.quiet||u.init)&&(!n.once||c||u.init)&&s(c,a,l),c&&n.once?Rg(e,t):u.init=!0},o);e._observe=Object(e._observe),e._observe[t.instance.$.uid]={init:!1,observer:i},i.observe(e)}function Rg(e,t){const n=e._observe?.[t.instance.$.uid];n&&(n.observer.unobserve(e),delete e._observe[t.instance.$.uid])}const Jo={mounted:nC,unmounted:Rg},rC=te({autoGrow:Boolean,autofocus:Boolean,counter:[Boolean,Number,String],counterValue:Function,prefix:String,placeholder:String,persistentPlaceholder:Boolean,persistentCounter:Boolean,noResize:Boolean,rows:{type:[Number,String],default:5,validator:e=>!isNaN(parseFloat(e))},maxRows:{type:[Number,String],validator:e=>!isNaN(parseFloat(e))},suffix:String,modelModifiers:Object,...Og(),...Ig()},"VTextarea"),Lg=ge()({name:"VTextarea",directives:{vIntersect:Jo},inheritAttrs:!1,props:rC(),emits:{"click:control":e=>!0,"mousedown:control":e=>!0,"update:focused":e=>!0,"update:modelValue":e=>!0,"update:rows":e=>!0},setup(e,t){let{attrs:n,emit:r,slots:s}=t;const o=Vt(e,"modelValue"),{isFocused:i,focus:a,blur:l}=Pg(e),{onIntersect:u}=tC(e),c=U(()=>typeof e.counterValue=="function"?e.counterValue(o.value):(o.value||"").toString().length),f=U(()=>{if(n.maxlength)return n.maxlength;if(!(!e.counter||typeof e.counter!="number"&&typeof e.counter!="string"))return e.counter}),d=J(),m=J(),g=Ae(""),p=J(),w=U(()=>e.persistentPlaceholder||i.value||e.active);function h(){p.value!==document.activeElement&&p.value?.focus(),i.value||a()}function v(V){h(),r("click:control",V)}function b(V){r("mousedown:control",V)}function y(V){V.stopPropagation(),h(),bt(()=>{o.value="",jm(e["onClick:clear"],V)})}function E(V){const D=V.target;if(o.value=D.value,e.modelModifiers?.trim){const G=[D.selectionStart,D.selectionEnd];bt(()=>{D.selectionStart=G[0],D.selectionEnd=G[1]})}}const P=J(),k=J(Number(e.rows)),T=U(()=>["plain","underlined"].includes(e.variant));en(()=>{e.autoGrow||(k.value=Number(e.rows))});function A(){e.autoGrow&&bt(()=>{if(!P.value||!m.value)return;const V=getComputedStyle(P.value),D=getComputedStyle(m.value.$el),G=parseFloat(V.getPropertyValue("--v-field-padding-top"))+parseFloat(V.getPropertyValue("--v-input-padding-top"))+parseFloat(V.getPropertyValue("--v-field-padding-bottom")),ee=P.value.scrollHeight,ne=parseFloat(V.lineHeight),oe=Math.max(parseFloat(e.rows)*ne+G,parseFloat(D.getPropertyValue("--v-input-control-height"))),Z=parseFloat(e.maxRows)*ne+G||1/0,re=Er(ee??0,oe,Z);k.value=Math.floor((re-G)/ne),g.value=me(re)})}Gt(A),le(o,A),le(()=>e.rows,A),le(()=>e.maxRows,A),le(()=>e.density,A),le(k,V=>{r("update:rows",V)});let H;return le(P,V=>{V?(H=new ResizeObserver(A),H.observe(P.value)):H?.disconnect()}),Qt(()=>{H?.disconnect()}),ye(()=>{const V=!!(s.counter||e.counter||e.counterValue),D=!!(V||s.details),[G,ee]=Bm(n),{modelValue:ne,...oe}=Af.filterProps(e),Z=kf.filterProps(e);return R(Af,Pe({ref:d,modelValue:o.value,"onUpdate:modelValue":re=>o.value=re,class:["v-textarea v-text-field",{"v-textarea--prefixed":e.prefix,"v-textarea--suffixed":e.suffix,"v-text-field--prefixed":e.prefix,"v-text-field--suffixed":e.suffix,"v-textarea--auto-grow":e.autoGrow,"v-textarea--no-resize":e.noResize||e.autoGrow,"v-input--plain-underlined":T.value},e.class],style:e.style},G,oe,{centerAffix:k.value===1&&!T.value,focused:i.value}),{...s,default:re=>{let{id:pe,isDisabled:ue,isDirty:xe,isReadonly:_e,isValid:Me}=re;return R(kf,Pe({ref:m,style:{"--v-textarea-control-height":g.value},onClick:v,onMousedown:b,"onClick:clear":y,"onClick:prependInner":e["onClick:prependInner"],"onClick:appendInner":e["onClick:appendInner"]},Z,{id:pe.value,active:w.value||xe.value,centerAffix:k.value===1&&!T.value,dirty:xe.value||e.dirty,disabled:ue.value,focused:i.value,error:Me.value===!1}),{...s,default:Ge=>{let{props:{class:Se,...Ne}}=Ge;return N(ke,null,[e.prefix&&N("span",{class:"v-text-field__prefix"},[e.prefix]),Kt(N("textarea",Pe({ref:p,class:Se,value:o.value,onInput:E,autofocus:e.autofocus,readonly:_e.value,disabled:ue.value,placeholder:e.placeholder,rows:e.rows,name:e.name,onFocus:h,onBlur:l},Ne,ee),null),[[Jo,{handler:u},null,{once:!0}]]),e.autoGrow&&Kt(N("textarea",{class:de([Se,"v-textarea__sizer"]),id:`${Ne.id}-sizer`,"onUpdate:modelValue":F=>o.value=F,ref:P,readonly:!0,"aria-hidden":"true"},null),[[My,o.value]]),e.suffix&&N("span",{class:"v-text-field__suffix"},[e.suffix])])}})},details:D?re=>N(ke,null,[s.details?.(re),V&&N(ke,null,[N("span",null,null),R(WE,{active:e.persistentCounter||i.value,value:c.value,max:f.value,disabled:e.disabled},s.counter)])]):void 0})}),eo({},d,m,p)}}),sC={class:"question"},oC=["innerHTML"],iC={key:0,id:"loader"},aC={key:1,id:"error"},lC={id:"chatgpt-buttons"},cC={__name:"Chat",setup(e){const t=Xl(),n=ec(t);hi();const r=J(null),s=J(null),o=J(!1),i=J(""),a=J(""),l=J([]),u={required:y=>!!y||"Dit veld mag niet leeg zijn"};Gt(()=>{c()});const c=async y=>{try{const E=await n.get("?action=ChatInit");l.value=E.data.data}catch(E){i.value="Foutmelding: "+(E.message??"onbekende fout"),console.error("Error: ",E)}finally{}},f=async y=>{try{l.value=[];const E=await n.get("?action=ChatReset")}catch(E){console.error("Error: ",E),E.value=E.message||"Er is een fout opgetreden bij het resetten van de chat."}finally{}},d=y=>{a.value.trim()!==""&&b()};var m="",g="";const p=y=>{m+=y,g+=y,h(m)&&(l.value[l.value.length-1].answer+=g,g="")},w=y=>{let E={question:y,answer:""};l.value.push(E),o.value=!0},h=y=>{try{return y.match(/<[^>]*$/)?!1:new DOMParser().parseFromString(y,"text/html").body.innerHTML.trim()!==""}catch{return!1}},v=()=>{m.trim()===""&&(m="Er is iets misgegaan. Stel je vraag opnieuw.");const y=m.replace(/【\d+:\d+†[^】]+】/g,"");l.value[l.value.length-1].answer=y,m="",o.value=!1},b=()=>{let y=a.value.trim();const E=new EventSource(`?action=streamer&question=${encodeURIComponent(y)}`);E.onmessage=function(P){try{if(P.data==="[DONE]"){v(),E.close();return}const k=JSON.parse(P.data);if(k.event==="thread.message.delta"){const T=k?.response?.delta?.content[0].text.value;T&&p(T)}else k.event==="error"&&(console.warn("❌ Error",P.data),v(),E.close())}catch(k){console.warn("❌ Ongeldige JSON:",k),console.warn("Data",P.data)}},E.onerror=function(P){console.error("❌ Stream error:",P),o.value=!1,E.close()},w(y),a.value!=""&&(a.value=null,r.value?.resetValidation())};return(y,E)=>(qe(),rt("div",null,[E[4]||(E[4]=N("h1",null,"GPT - AI Support assistent",-1)),E[5]||(E[5]=N("div",null,[N("div",{id:"chatintro"},[ut(" De VDL Container Systems AI-chatbot is gevuld met onze eigen support data, en weet alles* over onze machines."),N("br"),ut(" Onze AI-chatbot helpt je graag vooruit, dus stel gerust een vraag ! "),N("br"),ut(" * Onze AI is volop in ontwikkeling en een test case van ons innovatie team "),N("br"),N("br")])],-1)),N("div",{id:"output",ref_key:"output",ref:s},[(qe(!0),rt(ke,null,Lo(l.value,(P,k)=>(qe(),rt("div",{key:k},[N("div",sC,tt(P.question),1),N("div",{class:"answer",innerHTML:P.answer},null,8,oC)]))),128))],512),o.value?(qe(),rt("div",iC)):xn("",!0),i.value!=""?(qe(),rt("div",aC,tt(i.value),1)):xn("",!0),R(VE,{ref_key:"formRef",ref:r,id:"chatgptform",onSubmit:E[2]||(E[2]=So(P=>b(),["prevent"]))},{default:Ue(()=>[R(Lg,{id:"question",placeholder:"Stel je vraag...",modelValue:a.value,"onUpdate:modelValue":E[0]||(E[0]=P=>a.value=P),rules:[u.required],onKeydown:Pm(So(d,["exact","prevent"]),["enter"])},null,8,["modelValue","rules","onKeydown"]),N("div",lC,[E[3]||(E[3]=N("input",{type:"submit",id:"ask",class:"gsd-btn gsd-btn-primary",value:"Stel je vraag"},null,-1)),N("input",{type:"button",id:"reset",class:"gsd-btn gsd-btn-secondary",value:"Start nieuw gesprek",onClick:E[1]||(E[1]=So(P=>f(),["prevent"]))})])]),_:1},512)]))}},uC=(e,t)=>{const n=e.__vccOpts||e;for(const[r,s]of t)n[r]=s;return n},Ng=ge()({name:"VCardActions",props:Ie(),setup(e,t){let{slots:n}=t;return Xs({VBtn:{slim:!0,variant:"text"}}),ye(()=>N("div",{class:de(["v-card-actions",e.class]),style:be(e.style)},[n.default?.()])),{}}}),fC=te({opacity:[Number,String],...Ie(),...gt()},"VCardSubtitle"),dC=ge()({name:"VCardSubtitle",props:fC(),setup(e,t){let{slots:n}=t;return ye(()=>R(e.tag,{class:de(["v-card-subtitle",e.class]),style:be([{"--v-card-subtitle-opacity":e.opacity},e.style])},n)),{}}}),mC=Ul("v-card-title");function hC(e){return{aspectStyles:U(()=>{const t=Number(e.aspectRatio);return t?{paddingBottom:String(1/t*100)+"%"}:void 0})}}const Dg=te({aspectRatio:[String,Number],contentClass:null,inline:Boolean,...Ie(),...tr()},"VResponsive"),Pf=ge()({name:"VResponsive",props:Dg(),setup(e,t){let{slots:n}=t;const{aspectStyles:r}=hC(e),{dimensionStyles:s}=nr(e);return ye(()=>N("div",{class:de(["v-responsive",{"v-responsive--inline":e.inline},e.class]),style:be([s.value,e.style])},[N("div",{class:"v-responsive__sizer",style:be(r.value)},null),n.additional?.(),n.default&&N("div",{class:de(["v-responsive__content",e.contentClass])},[n.default()])])),{}}}),gC=te({absolute:Boolean,alt:String,cover:Boolean,color:String,draggable:{type:[Boolean,String],default:void 0},eager:Boolean,gradient:String,lazySrc:String,options:{type:Object,default:()=>({root:void 0,rootMargin:void 0,threshold:void 0})},sizes:String,src:{type:[String,Object],default:""},crossorigin:String,referrerpolicy:String,srcset:String,position:String,...Dg(),...Ie(),...$t(),...Ei()},"VImg"),tc=ge()({name:"VImg",directives:{vIntersect:Jo},props:gC(),emits:{loadstart:e=>!0,load:e=>!0,error:e=>!0},setup(e,t){let{emit:n,slots:r}=t;const{backgroundColorClasses:s,backgroundColorStyles:o}=mn(()=>e.color),{roundedClasses:i}=Bt(e),a=dt("VImg"),l=Ae(""),u=J(),c=Ae(e.eager?"loading":"idle"),f=Ae(),d=Ae(),m=U(()=>e.src&&typeof e.src=="object"?{src:e.src.src,srcset:e.srcset||e.src.srcset,lazySrc:e.lazySrc||e.src.lazySrc,aspect:Number(e.aspectRatio||e.src.aspect||0)}:{src:e.src,srcset:e.srcset,lazySrc:e.lazySrc,aspect:Number(e.aspectRatio||0)}),g=U(()=>m.value.aspect||f.value/d.value||0);le(()=>e.src,()=>{p(c.value!=="idle")}),le(g,(D,G)=>{!D&&G&&u.value&&y(u.value)}),Gs(()=>p());function p(D){if(!(e.eager&&D)&&!(Dl&&!D&&!e.eager)){if(c.value="loading",m.value.lazySrc){const G=new Image;G.src=m.value.lazySrc,y(G,null)}m.value.src&&bt(()=>{n("loadstart",u.value?.currentSrc||m.value.src),setTimeout(()=>{if(!a.isUnmounted)if(u.value?.complete){if(u.value.naturalWidth||h(),c.value==="error")return;g.value||y(u.value,null),c.value==="loading"&&w()}else g.value||y(u.value),v()})})}}function w(){a.isUnmounted||(v(),y(u.value),c.value="loaded",n("load",u.value?.currentSrc||m.value.src))}function h(){a.isUnmounted||(c.value="error",n("error",u.value?.currentSrc||m.value.src))}function v(){const D=u.value;D&&(l.value=D.currentSrc||D.src)}let b=-1;Qt(()=>{clearTimeout(b)});function y(D){let G=arguments.length>1&&arguments[1]!==void 0?arguments[1]:100;const ee=()=>{if(clearTimeout(b),a.isUnmounted)return;const{naturalHeight:ne,naturalWidth:oe}=D;ne||oe?(f.value=oe,d.value=ne):!D.complete&&c.value==="loading"&&G!=null?b=window.setTimeout(ee,G):(D.currentSrc.endsWith(".svg")||D.currentSrc.startsWith("data:image/svg+xml"))&&(f.value=1,d.value=1)};ee()}const E=X(()=>({"v-img__img--cover":e.cover,"v-img__img--contain":!e.cover})),P=()=>{if(!m.value.src||c.value==="idle")return null;const D=N("img",{class:de(["v-img__img",E.value]),style:{objectPosition:e.position},crossorigin:e.crossorigin,src:m.value.src,srcset:m.value.srcset,alt:e.alt,referrerpolicy:e.referrerpolicy,draggable:e.draggable,sizes:e.sizes,ref:u,onLoad:w,onError:h},null),G=r.sources?.();return R(mr,{transition:e.transition,appear:!0},{default:()=>[Kt(G?N("picture",{class:"v-img__picture"},[G,D]):D,[[Ys,c.value==="loaded"]])]})},k=()=>R(mr,{transition:e.transition},{default:()=>[m.value.lazySrc&&c.value!=="loaded"&&N("img",{class:de(["v-img__img","v-img__img--preload",E.value]),style:{objectPosition:e.position},crossorigin:e.crossorigin,src:m.value.lazySrc,alt:e.alt,referrerpolicy:e.referrerpolicy,draggable:e.draggable},null)]}),T=()=>r.placeholder?R(mr,{transition:e.transition,appear:!0},{default:()=>[(c.value==="loading"||c.value==="error"&&!r.error)&&N("div",{class:"v-img__placeholder"},[r.placeholder()])]}):null,A=()=>r.error?R(mr,{transition:e.transition,appear:!0},{default:()=>[c.value==="error"&&N("div",{class:"v-img__error"},[r.error()])]}):null,H=()=>e.gradient?N("div",{class:"v-img__gradient",style:{backgroundImage:`linear-gradient(${e.gradient})`}},null):null,V=Ae(!1);{const D=le(g,G=>{G&&(requestAnimationFrame(()=>{requestAnimationFrame(()=>{V.value=!0})}),D())})}return ye(()=>{const D=Pf.filterProps(e);return Kt(R(Pf,Pe({class:["v-img",{"v-img--absolute":e.absolute,"v-img--booting":!V.value},s.value,i.value,e.class],style:[{width:me(e.width==="auto"?f.value:e.width)},o.value,e.style]},D,{aspectRatio:g.value,"aria-label":e.alt,role:e.alt?"img":void 0}),{additional:()=>N(ke,null,[R(P,null,null),R(k,null,null),R(H,null,null),R(T,null,null),R(A,null,null)]),default:r.default}),[[Jo,{handler:p,options:e.options},null,{once:!0}]])}),{currentSrc:l,image:u,state:c,naturalWidth:f,naturalHeight:d}}}),vC=te({start:Boolean,end:Boolean,icon:it,image:String,text:String,...Xn(),...Ie(),...Zn(),...$t(),...pi(),...gt(),...ct(),...Or({variant:"flat"})},"VAvatar"),Xr=ge()({name:"VAvatar",props:vC(),setup(e,t){let{slots:n}=t;const{themeClasses:r}=ht(e),{borderClasses:s}=Jn(e),{colorClasses:o,colorStyles:i,variantClasses:a}=rs(e),{densityClasses:l}=Ir(e),{roundedClasses:u}=Bt(e),{sizeClasses:c,sizeStyles:f}=yi(e);return ye(()=>R(e.tag,{class:de(["v-avatar",{"v-avatar--start":e.start,"v-avatar--end":e.end},r.value,s.value,o.value,l.value,u.value,c.value,a.value,e.class]),style:be([i.value,f.value,e.style])},{default:()=>[n.default?R(Ye,{key:"content-defaults",defaults:{VImg:{cover:!0,src:e.image},VIcon:{icon:e.icon}}},{default:()=>[n.default()]}):e.image?R(tc,{key:"image",src:e.image,alt:"",cover:!0},null):e.icon?R(_t,{key:"icon",icon:e.icon},null):e.text,ns(!1,"v-avatar")]})),{}}}),pC=te({appendAvatar:String,appendIcon:it,prependAvatar:String,prependIcon:it,subtitle:{type:[String,Number,Boolean],default:void 0},title:{type:[String,Number,Boolean],default:void 0},...Ie(),...Zn()},"VCardItem"),yC=ge()({name:"VCardItem",props:pC(),setup(e,t){let{slots:n}=t;return ye(()=>{const r=!!(e.prependAvatar||e.prependIcon),s=!!(r||n.prepend),o=!!(e.appendAvatar||e.appendIcon),i=!!(o||n.append),a=!!(e.title!=null||n.title),l=!!(e.subtitle!=null||n.subtitle);return N("div",{class:de(["v-card-item",e.class]),style:be(e.style)},[s&&N("div",{key:"prepend",class:"v-card-item__prepend"},[n.prepend?R(Ye,{key:"prepend-defaults",disabled:!r,defaults:{VAvatar:{density:e.density,image:e.prependAvatar},VIcon:{density:e.density,icon:e.prependIcon}}},n.prepend):N(ke,null,[e.prependAvatar&&R(Xr,{key:"prepend-avatar",density:e.density,image:e.prependAvatar},null),e.prependIcon&&R(_t,{key:"prepend-icon",density:e.density,icon:e.prependIcon},null)])]),N("div",{class:"v-card-item__content"},[a&&R(mC,{key:"title"},{default:()=>[n.title?.()??tt(e.title)]}),l&&R(dC,{key:"subtitle"},{default:()=>[n.subtitle?.()??tt(e.subtitle)]}),n.default?.()]),i&&N("div",{key:"append",class:"v-card-item__append"},[n.append?R(Ye,{key:"append-defaults",disabled:!o,defaults:{VAvatar:{density:e.density,image:e.appendAvatar},VIcon:{density:e.density,icon:e.appendIcon}}},n.append):N(ke,null,[e.appendIcon&&R(_t,{key:"append-icon",density:e.density,icon:e.appendIcon},null),e.appendAvatar&&R(Xr,{key:"append-avatar",density:e.density,image:e.appendAvatar},null)])])])}),{}}}),bC=te({opacity:[Number,String],...Ie(),...gt()},"VCardText"),Zo=ge()({name:"VCardText",props:bC(),setup(e,t){let{slots:n}=t;return ye(()=>R(e.tag,{class:de(["v-card-text",e.class]),style:be([{"--v-card-text-opacity":e.opacity},e.style])},n)),{}}}),_C=te({appendAvatar:String,appendIcon:it,disabled:Boolean,flat:Boolean,hover:Boolean,image:String,link:{type:Boolean,default:void 0},prependAvatar:String,prependIcon:it,ripple:{type:[Boolean,Object],default:!0},subtitle:{type:[String,Number,Boolean],default:void 0},text:{type:[String,Number,Boolean],default:void 0},title:{type:[String,Number,Boolean],default:void 0},...Xn(),...Ie(),...Zn(),...tr(),...Qn(),...zl(),...Zs(),..._i(),...$t(),...ql(),...gt(),...ct(),...Or({variant:"elevated"})},"VCard"),SC=ge()({name:"VCard",directives:{vRipple:Tr},props:_C(),setup(e,t){let{attrs:n,slots:r}=t;const{themeClasses:s}=ht(e),{borderClasses:o}=Jn(e),{colorClasses:i,colorStyles:a,variantClasses:l}=rs(e),{densityClasses:u}=Ir(e),{dimensionStyles:c}=nr(e),{elevationClasses:f}=er(e),{loaderClasses:d}=Kl(e),{locationStyles:m}=bi(e),{positionClasses:g}=Si(e),{roundedClasses:p}=Bt(e),w=Gl(e,n);return ye(()=>{const h=e.link!==!1&&w.isLink.value,v=!e.disabled&&e.link!==!1&&(e.link||w.isClickable.value),b=h?"a":e.tag,y=!!(r.title||e.title!=null),E=!!(r.subtitle||e.subtitle!=null),P=y||E,k=!!(r.append||e.appendAvatar||e.appendIcon),T=!!(r.prepend||e.prependAvatar||e.prependIcon),A=!!(r.image||e.image),H=P||T||k,V=!!(r.text||e.text!=null);return Kt(R(b,Pe({class:["v-card",{"v-card--disabled":e.disabled,"v-card--flat":e.flat,"v-card--hover":e.hover&&!(e.disabled||e.flat),"v-card--link":v},s.value,o.value,i.value,u.value,f.value,d.value,g.value,p.value,l.value,e.class],style:[a.value,c.value,m.value,e.style],onClick:v&&w.navigate,tabindex:e.disabled?-1:void 0},w.linkProps),{default:()=>[A&&N("div",{key:"image",class:"v-card__image"},[r.image?R(Ye,{key:"image-defaults",disabled:!e.image,defaults:{VImg:{cover:!0,src:e.image}}},r.image):R(tc,{key:"image-img",cover:!0,src:e.image},null)]),R(ph,{name:"v-card",active:!!e.loading,color:typeof e.loading=="boolean"?void 0:e.loading},{default:r.loader}),H&&R(yC,{key:"item",prependAvatar:e.prependAvatar,prependIcon:e.prependIcon,title:e.title,subtitle:e.subtitle,appendAvatar:e.appendAvatar,appendIcon:e.appendIcon},{default:r.item,prepend:r.prepend,title:r.title,subtitle:r.subtitle,append:r.append}),V&&R(Zo,{key:"text"},{default:()=>[r.text?.()??e.text]}),r.default?.(),r.actions&&R(Ng,null,{default:r.actions}),ns(v,"v-card")]}),[[Tr,v&&e.ripple]])}),{}}}),wC=te({fullscreen:Boolean,retainFocus:{type:Boolean,default:!0},scrollable:Boolean,...Ci({origin:"center center",scrollStrategy:"block",transition:{component:UE},zIndex:2400})},"VDialog"),EC=ge()({name:"VDialog",props:wC(),emits:{"update:modelValue":e=>!0,afterEnter:()=>!0,afterLeave:()=>!0},setup(e,t){let{emit:n,slots:r}=t;const s=Vt(e,"modelValue"),{scopeId:o}=wi(),i=J();function a(c){const f=c.relatedTarget,d=c.target;if(f!==d&&i.value?.contentEl&&i.value?.globalTop&&![document,i.value.contentEl].includes(d)&&!i.value.contentEl.contains(d)){const m=ib(i.value.contentEl);if(!m.length)return;const g=m[0],p=m[m.length-1];f===g?p.focus():g.focus()}}Qt(()=>{document.removeEventListener("focusin",a)}),Ke&&le(()=>s.value&&e.retainFocus,c=>{c?document.addEventListener("focusin",a):document.removeEventListener("focusin",a)},{immediate:!0});function l(){n("afterEnter"),(e.scrim||e.retainFocus)&&i.value?.contentEl&&!i.value.contentEl.contains(document.activeElement)&&i.value.contentEl.focus({preventScroll:!0})}function u(){n("afterLeave")}return le(s,async c=>{c||(await bt(),i.value.activatorEl?.focus({preventScroll:!0}))}),ye(()=>{const c=kr.filterProps(e),f=Pe({"aria-haspopup":"dialog"},e.activatorProps),d=Pe({tabindex:-1},e.contentProps);return R(kr,Pe({ref:i,class:["v-dialog",{"v-dialog--fullscreen":e.fullscreen,"v-dialog--scrollable":e.scrollable},e.class],style:e.style},c,{modelValue:s.value,"onUpdate:modelValue":m=>s.value=m,"aria-modal":"true",activatorProps:f,contentProps:d,height:e.fullscreen?void 0:e.height,width:e.fullscreen?void 0:e.width,maxHeight:e.fullscreen?void 0:e.maxHeight,maxWidth:e.fullscreen?void 0:e.maxWidth,role:"dialog",onAfterEnter:l,onAfterLeave:u},o),{activator:r.activator,default:function(){for(var m=arguments.length,g=new Array(m),p=0;p<m;p++)g[p]=arguments[p];return R(Ye,{root:"VDialog"},{default:()=>[r.default?.(...g)]})}})}),eo({},i)}}),CC=Ul("v-spacer","div","VSpacer"),xC=te({text:String,...Ie(),...gt()},"VToolbarTitle"),Fg=ge()({name:"VToolbarTitle",props:xC(),setup(e,t){let{slots:n}=t;return ye(()=>{const r=!!(n.default||n.text||e.text);return R(e.tag,{class:de(["v-toolbar-title",e.class]),style:be(e.style)},{default:()=>[r&&N("div",{class:"v-toolbar-title__placeholder"},[n.text?n.text():e.text,n.default?.()])]})}),{}}}),TC=[null,"prominent","default","comfortable","compact"],kC=te({absolute:Boolean,collapse:Boolean,color:String,density:{type:String,default:"default",validator:e=>TC.includes(e)},extended:{type:Boolean,default:null},extensionHeight:{type:[Number,String],default:48},flat:Boolean,floating:Boolean,height:{type:[Number,String],default:64},image:String,title:String,...Xn(),...Ie(),...Qn(),...$t(),...gt({tag:"header"}),...ct()},"VToolbar"),AC=ge()({name:"VToolbar",props:kC(),setup(e,t){let{slots:n}=t;const{backgroundColorClasses:r,backgroundColorStyles:s}=mn(()=>e.color),{borderClasses:o}=Jn(e),{elevationClasses:i}=er(e),{roundedClasses:a}=Bt(e),{themeClasses:l}=ht(e),{rtlClasses:u}=Pr(),c=Ae(e.extended===null?!!n.extension?.():e.extended),f=U(()=>parseInt(Number(e.height)+(e.density==="prominent"?Number(e.height):0)-(e.density==="comfortable"?8:0)-(e.density==="compact"?16:0),10)),d=U(()=>c.value?parseInt(Number(e.extensionHeight)+(e.density==="prominent"?Number(e.extensionHeight):0)-(e.density==="comfortable"?4:0)-(e.density==="compact"?8:0),10):0);return Xs({VBtn:{variant:"text"}}),ye(()=>{const m=!!(e.title||n.title),g=!!(n.image||e.image),p=n.extension?.();return c.value=e.extended===null?!!p:e.extended,R(e.tag,{class:de(["v-toolbar",{"v-toolbar--absolute":e.absolute,"v-toolbar--collapse":e.collapse,"v-toolbar--flat":e.flat,"v-toolbar--floating":e.floating,[`v-toolbar--density-${e.density}`]:!0},r.value,o.value,i.value,a.value,l.value,u.value,e.class]),style:be([s.value,e.style])},{default:()=>[g&&N("div",{key:"image",class:"v-toolbar__image"},[n.image?R(Ye,{key:"image-defaults",disabled:!e.image,defaults:{VImg:{cover:!0,src:e.image}}},n.image):R(tc,{key:"image-img",cover:!0,src:e.image},null)]),R(Ye,{defaults:{VTabs:{height:me(f.value)}}},{default:()=>[N("div",{class:"v-toolbar__content",style:{height:me(f.value)}},[n.prepend&&N("div",{class:"v-toolbar__prepend"},[n.prepend?.()]),m&&R(Fg,{key:"title",text:e.title},{text:n.title}),n.default?.(),n.append&&N("div",{class:"v-toolbar__append"},[n.append?.()])])]}),R(Ye,{defaults:{VTabs:{height:me(d.value)}}},{default:()=>[R(Tg,null,{default:()=>[c.value&&N("div",{class:"v-toolbar__extension",style:{height:me(d.value)}},[p])]})]})]})}),{contentHeight:f,extensionHeight:d}}}),PC={__name:"ConfirmDialog",props:{modelValue:Boolean,title:String,message:String,options:{type:Object,default:()=>({color:"primary",width:400,zIndex:2e3,cancelText:"Annuleren",confirmText:"Bevestigen",confirmColor:"primary"})}},emits:["update:modelValue","confirm","cancel"],setup(e,{emit:t}){const n=e,r=t,s=J(n.modelValue);le(()=>n.modelValue,a=>{s.value=a}),le(s,a=>{r("update:modelValue",a)});const o=()=>{s.value=!1,r("confirm")},i=()=>{s.value=!1,r("cancel")};return U(()=>({color:"primary",width:400,zIndex:2e3,cancelText:"Annuleren",confirmText:"Bevestigen",confirmColor:"primary",...n.options})),(a,l)=>(qe(),fi(EC,{modelValue:s.value,"onUpdate:modelValue":l[0]||(l[0]=u=>s.value=u),"max-width":e.options.width,style:be({zIndex:e.options.zIndex}),onKeydown:Pm(i,["esc"]),persistent:""},{default:Ue(()=>[R(SC,null,{default:Ue(()=>[R(AC,{color:e.options.color,density:"compact",flat:"",dark:""},{default:Ue(()=>[R(Fg,{class:"text-h6 text-white"},{default:Ue(()=>[ut(tt(e.title),1)]),_:1})]),_:1},8,["color"]),R(Zo,{class:"pa-4 text-body-1"},{default:Ue(()=>[ut(tt(e.message),1)]),_:1}),R(Ng,{class:"pt-0"},{default:Ue(()=>[R(CC),R(Gn,{color:"grey",variant:"text",onClick:i},{default:Ue(()=>[ut(tt(e.options.cancelText),1)]),_:1}),R(Gn,{color:e.options.confirmColor,variant:"flat",onClick:o},{default:Ue(()=>[ut(tt(e.options.confirmText),1)]),_:1},8,["color"])]),_:1})]),_:1})]),_:1},8,["modelValue","max-width","style"]))}},Mg=uC(PC,[["__scopeId","data-v-fb42f891"]]),IC=te({opacity:[Number,String],...Ie(),...gt()},"VListItemSubtitle"),OC=ge()({name:"VListItemSubtitle",props:IC(),setup(e,t){let{slots:n}=t;return ye(()=>R(e.tag,{class:de(["v-list-item-subtitle",e.class]),style:be([{"--v-list-item-subtitle-opacity":e.opacity},e.style])},n)),{}}}),RC=Ul("v-list-item-title"),LC=Symbol.for("vuetify:list");function NC(){return Le(LC,null)}const DC=Symbol.for("vuetify:nested"),FC={id:Ae(),root:{register:()=>null,unregister:()=>null,children:J(new Map),parents:J(new Map),disabled:J(new Set),open:()=>null,openOnSelect:()=>null,activate:()=>null,select:()=>null,activatable:J(!1),selectable:J(!1),opened:J(new Set),activated:J(new Set),selected:J(new Map),selectedValues:J([]),getPath:()=>[]}},MC=(e,t,n)=>{const r=Le(DC,FC),s=Symbol("nested item"),o=U(()=>Ee(gn(e))??s),i={...r,id:o,open:(a,l)=>r.root.open(o.value,a,l),openOnSelect:(a,l)=>r.root.openOnSelect(o.value,a,l),isOpen:U(()=>r.root.opened.value.has(o.value)),parent:U(()=>r.root.parents.value.get(o.value)),activate:(a,l)=>r.root.activate(o.value,a,l),isActivated:U(()=>r.root.activated.value.has(o.value)),select:(a,l)=>r.root.select(o.value,a,l),isSelected:U(()=>r.root.selected.value.get(o.value)==="on"),isIndeterminate:U(()=>r.root.selected.value.get(o.value)==="indeterminate"),isLeaf:U(()=>!r.root.children.value.get(o.value)),isGroupActivator:r.isGroupActivator};return Gs(()=>{r.isGroupActivator||r.root.register(o.value,r.id.value,gn(t),n)}),Qt(()=>{r.isGroupActivator||r.root.unregister(o.value)}),i},Vg=te({active:{type:Boolean,default:void 0},activeClass:String,activeColor:String,appendAvatar:String,appendIcon:it,baseColor:String,disabled:Boolean,lines:[Boolean,String],link:{type:Boolean,default:void 0},nav:Boolean,prependAvatar:String,prependIcon:it,ripple:{type:[Boolean,Object],default:!0},slim:Boolean,subtitle:{type:[String,Number,Boolean],default:void 0},title:{type:[String,Number,Boolean],default:void 0},value:null,onClick:An(),onClickOnce:An(),...Xn(),...Ie(),...Zn(),...tr(),...Qn(),...$t(),...ql(),...gt(),...ct(),...Or({variant:"text"})},"VListItem"),If=ge()({name:"VListItem",directives:{vRipple:Tr},props:Vg(),emits:{click:e=>!0},setup(e,t){let{attrs:n,slots:r,emit:s}=t;const o=Gl(e,n),i=U(()=>e.value===void 0?o.href.value:e.value),{activate:a,isActivated:l,select:u,isOpen:c,isSelected:f,isIndeterminate:d,isGroupActivator:m,root:g,parent:p,openOnSelect:w,id:h}=MC(i,()=>e.disabled,!1),v=NC(),b=U(()=>e.active!==!1&&(e.active||o.isActive?.value||(g.activatable.value?l.value:f.value))),y=X(()=>e.link!==!1&&o.isLink.value),E=U(()=>!!v&&(g.selectable.value||g.activatable.value||e.value!=null)),P=U(()=>!e.disabled&&e.link!==!1&&(e.link||o.isClickable.value||E.value)),k=X(()=>e.rounded||e.nav),T=X(()=>e.color??e.activeColor),A=X(()=>({color:b.value?T.value??e.baseColor:e.baseColor,variant:e.variant}));le(()=>o.isActive?.value,Se=>{Se&&H()}),Gs(()=>{o.isActive?.value&&H()});function H(){p.value!=null&&g.open(p.value,!0),w(!0)}const{themeClasses:V}=ht(e),{borderClasses:D}=Jn(e),{colorClasses:G,colorStyles:ee,variantClasses:ne}=rs(A),{densityClasses:oe}=Ir(e),{dimensionStyles:Z}=nr(e),{elevationClasses:re}=er(e),{roundedClasses:pe}=Bt(k),ue=X(()=>e.lines?`v-list-item--${e.lines}-line`:void 0),xe=X(()=>e.ripple!==void 0&&e.ripple&&v?.filterable?{keys:[Ra.enter]}:e.ripple),_e=U(()=>({isActive:b.value,select:u,isOpen:c.value,isSelected:f.value,isIndeterminate:d.value}));function Me(Se){s("click",Se),!["INPUT","TEXTAREA"].includes(Se.target?.tagName)&&P.value&&(o.navigate?.(Se),!m&&(g.activatable.value?a(!l.value,Se):(g.selectable.value||e.value!=null)&&u(!f.value,Se)))}function Ge(Se){const Ne=Se.target;["INPUT","TEXTAREA"].includes(Ne.tagName)||(Se.key==="Enter"||Se.key===" "&&!v?.filterable)&&(Se.preventDefault(),Se.stopPropagation(),Se.target.dispatchEvent(new MouseEvent("click",Se)))}return ye(()=>{const Se=y.value?"a":e.tag,Ne=r.title||e.title!=null,F=r.subtitle||e.subtitle!=null,q=!!(e.appendAvatar||e.appendIcon),Y=!!(q||r.append),ie=!!(e.prependAvatar||e.prependIcon),Te=!!(ie||r.prepend);return v?.updateHasPrepend(Te),e.activeColor&&Km("active-color",["color","base-color"]),Kt(R(Se,Pe({class:["v-list-item",{"v-list-item--active":b.value,"v-list-item--disabled":e.disabled,"v-list-item--link":P.value,"v-list-item--nav":e.nav,"v-list-item--prepend":!Te&&v?.hasPrepend.value,"v-list-item--slim":e.slim,[`${e.activeClass}`]:e.activeClass&&b.value},V.value,D.value,G.value,oe.value,re.value,ue.value,pe.value,ne.value,e.class],style:[ee.value,Z.value,e.style],tabindex:P.value?v?-2:0:void 0,"aria-selected":E.value?g.activatable.value?l.value:g.selectable.value?f.value:b.value:void 0,onClick:Me,onKeydown:P.value&&!y.value&&Ge},o.linkProps),{default:()=>[ns(P.value||b.value,"v-list-item"),Te&&N("div",{key:"prepend",class:"v-list-item__prepend"},[r.prepend?R(Ye,{key:"prepend-defaults",disabled:!ie,defaults:{VAvatar:{density:e.density,image:e.prependAvatar},VIcon:{density:e.density,icon:e.prependIcon},VListItemAction:{start:!0}}},{default:()=>[r.prepend?.(_e.value)]}):N(ke,null,[e.prependAvatar&&R(Xr,{key:"prepend-avatar",density:e.density,image:e.prependAvatar},null),e.prependIcon&&R(_t,{key:"prepend-icon",density:e.density,icon:e.prependIcon},null)]),N("div",{class:"v-list-item__spacer"},null)]),N("div",{class:"v-list-item__content","data-no-activator":""},[Ne&&R(RC,{key:"title"},{default:()=>[r.title?.({title:e.title})??tt(e.title)]}),F&&R(OC,{key:"subtitle"},{default:()=>[r.subtitle?.({subtitle:e.subtitle})??tt(e.subtitle)]}),r.default?.(_e.value)]),Y&&N("div",{key:"append",class:"v-list-item__append"},[r.append?R(Ye,{key:"append-defaults",disabled:!q,defaults:{VAvatar:{density:e.density,image:e.appendAvatar},VIcon:{density:e.density,icon:e.appendIcon},VListItemAction:{end:!0}}},{default:()=>[r.append?.(_e.value)]}):N(ke,null,[e.appendIcon&&R(_t,{key:"append-icon",density:e.density,icon:e.appendIcon},null),e.appendAvatar&&R(Xr,{key:"append-avatar",density:e.density,image:e.appendAvatar},null)]),N("div",{class:"v-list-item__spacer"},null)])]}),[[Tr,P.value&&xe.value]])}),{activate:a,isActivated:l,isGroupActivator:m,isSelected:f,list:v,select:u,root:g,id:h,link:o}}}),VC=te({clearable:Boolean,file:{type:Object,default:null},fileIcon:{type:String,default:"mdi-file-document"},showSize:Boolean,...Vg({border:!0,rounded:!0,lines:"two"})},"VFileUploadItem"),$C=ge()({name:"VFileUploadItem",props:VC(),emits:{"click:remove":()=>!0,click:e=>!0},setup(e,t){let{emit:n,slots:r}=t;const s=J(),o=U(()=>typeof e.showSize!="boolean"?e.showSize:void 0);function i(){n("click:remove")}en(()=>{s.value=e.file?.type.startsWith("image")?URL.createObjectURL(e.file):void 0}),ye(()=>{const a=If.filterProps(e);return R(If,Pe(a,{title:e.title??e.file?.name,subtitle:e.showSize?sb(e.file?.size,o.value):e.file?.type,class:"v-file-upload-item"}),{...r,title:()=>e?.title??e.file?.name,prepend:l=>N(ke,null,[r.prepend?R(Ye,{defaults:{VAvatar:{image:s.value,icon:s.value?void 0:e.fileIcon,rounded:!0}}},{default:()=>[r.prepend?.(l)??R(Xr,null,null)]}):R(Xr,{icon:e.fileIcon,image:s.value,rounded:!0},null)]),append:l=>N(ke,null,[e.clearable&&N(ke,null,[r.clear?R(Ye,{defaults:{VBtn:{icon:"$clear",density:"comfortable",variant:"text"}}},{default:()=>[r.clear?.({...l,props:{onClick:i}})??R(Gn,null,null)]}):R(Gn,{icon:"$clear",density:"comfortable",variant:"text",onClick:i},null)]),r.append?.(l)])})})}}),$g=te({color:String,inset:Boolean,length:[Number,String],opacity:[Number,String],thickness:[Number,String],vertical:Boolean,...Ie(),...ct()},"VDivider"),Of=ge()({name:"VDivider",props:$g(),setup(e,t){let{attrs:n,slots:r}=t;const{themeClasses:s}=ht(e),{textColorClasses:o,textColorStyles:i}=xr(()=>e.color),a=U(()=>{const l={};return e.length&&(l[e.vertical?"height":"width"]=me(e.length)),e.thickness&&(l[e.vertical?"borderRightWidth":"borderTopWidth"]=me(e.thickness)),l});return ye(()=>{const l=N("hr",{class:de([{"v-divider":!0,"v-divider--inset":e.inset,"v-divider--vertical":e.vertical},s.value,o.value,e.class]),style:be([a.value,i.value,{"--v-border-opacity":e.opacity},e.style]),"aria-orientation":!n.role||n.role==="separator"?e.vertical?"vertical":"horizontal":void 0,role:`${n.role||"separator"}`},null);return r.default?N("div",{class:de(["v-divider__wrapper",{"v-divider__wrapper--vertical":e.vertical,"v-divider__wrapper--inset":e.inset}])},[l,N("div",{class:"v-divider__content"},[r.default()]),l]):l}),{}}}),Bg=te({color:String,...Xn(),...Ie(),...tr(),...Qn(),...Zs(),..._i(),...$t(),...gt(),...ct()},"VSheet"),Rf=ge()({name:"VSheet",props:Bg(),setup(e,t){let{slots:n}=t;const{themeClasses:r}=ht(e),{backgroundColorClasses:s,backgroundColorStyles:o}=mn(()=>e.color),{borderClasses:i}=Jn(e),{dimensionStyles:a}=nr(e),{elevationClasses:l}=er(e),{locationStyles:u}=bi(e),{positionClasses:c}=Si(e),{roundedClasses:f}=Bt(e);return ye(()=>R(e.tag,{class:de(["v-sheet",r.value,s.value,i.value,l.value,c.value,f.value,e.class]),style:be([o.value,a.value,u.value,e.style])},n)),{}}});function BC(){function e(n){return[...n.dataTransfer?.items??[]].filter(s=>s.kind==="file").map(s=>s.webkitGetAsEntry()).filter(Boolean).length>0||[...n.dataTransfer?.files??[]].length>0}async function t(n){const r=[],s=[...n.dataTransfer?.items??[]].filter(o=>o.kind==="file").map(o=>o.webkitGetAsEntry()).filter(Boolean);if(s.length)for(const o of s){const i=await Ug(o,Hg(".",o));r.push(...i.map(a=>a.file))}else r.push(...n.dataTransfer?.files??[]);return r}return{handleDrop:t,hasFilesOrFolders:e}}function Ug(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"";return new Promise((n,r)=>{e.isFile?e.file(o=>n([{file:o,path:t}]),r):e.isDirectory&&e.createReader().readEntries(async o=>{const i=[];for(const a of o)i.push(...await Ug(a,Hg(t,a)));n(i)})})}function Hg(e,t){return t.isDirectory?`${e}/${t.name}`:e}const UC=te({browseText:{type:String,default:"$vuetify.fileUpload.browse"},dividerText:{type:String,default:"$vuetify.fileUpload.divider"},title:{type:String,default:"$vuetify.fileUpload.title"},subtitle:String,icon:{type:it,default:"$upload"},modelValue:{type:[Array,Object],default:null,validator:e=>yr(e).every(t=>t!=null&&typeof t=="object")},clearable:Boolean,disabled:Boolean,hideBrowse:Boolean,multiple:Boolean,scrim:{type:[Boolean,String],default:!0},showSize:Boolean,name:String,...Ah(),...Zn(),...gi($g({length:150}),["length","thickness","opacity"]),...Bg()},"VFileUpload"),HC=ge()({name:"VFileUpload",inheritAttrs:!1,props:UC(),emits:{"update:modelValue":e=>!0},setup(e,t){let{attrs:n,slots:r}=t;const{t:s}=rh(),{densityClasses:o}=Ir(e),i=Vt(e,"modelValue",e.modelValue,w=>yr(w),w=>e.multiple||Array.isArray(e.modelValue)?w:w[0]),a=Ae(!1),l=J(null),u=J(null),{handleDrop:c}=BC();function f(w){w.preventDefault(),w.stopImmediatePropagation(),a.value=!0}function d(w){w.preventDefault(),a.value=!1}async function m(w){if(w.preventDefault(),w.stopImmediatePropagation(),a.value=!1,!u.value)return;const h=new DataTransfer;for(const v of await c(w))h.items.add(v);u.value.files=h.files,u.value.dispatchEvent(new Event("change",{bubbles:!0}))}function g(){u.value?.click()}function p(w){const h=i.value.filter((v,b)=>b!==w);i.value=h,!(h.length>0||!u.value)&&(u.value.value="")}ye(()=>{const w=!!(r.title||e.title),h=!!(r.icon||e.icon),v=!!(!e.hideBrowse&&(r.browse||e.density==="default")),b=Rf.filterProps(e),y=Of.filterProps(e),[E,P]=Bm(n),k=N("input",Pe({ref:u,type:"file",disabled:e.disabled,multiple:e.multiple,name:e.name,onChange:T=>{if(!T.target)return;const A=T.target;i.value=[...A.files??[]]}},P),null);return N(ke,null,[R(Rf,Pe({ref:l},b,{class:["v-file-upload",{"v-file-upload--clickable":!v,"v-file-upload--disabled":e.disabled,"v-file-upload--dragging":a.value},o.value,e.class],style:[e.style],onDragleave:d,onDragover:f,onDrop:m,onClick:v?void 0:g},E),{default:()=>[h&&N("div",{key:"icon",class:"v-file-upload-icon"},[r.icon?R(Ye,{key:"icon-defaults",defaults:{VIcon:{icon:e.icon}}},{default:()=>[r.icon()]}):R(_t,{key:"icon-icon",icon:e.icon},null)]),w&&N("div",{key:"title",class:"v-file-upload-title"},[r.title?.()??s(e.title)]),e.density==="default"&&N(ke,null,[N("div",{key:"upload-divider",class:"v-file-upload-divider"},[r.divider?.()??R(Of,y,{default:()=>[s(e.dividerText)]})]),v&&N(ke,null,[r.browse?R(Ye,{defaults:{VBtn:{readonly:e.disabled,size:"large",text:s(e.browseText),variant:"tonal"}}},{default:()=>[r.browse({props:{onClick:g}})]}):R(Gn,{readonly:e.disabled,size:"large",text:s(e.browseText),variant:"tonal",onClick:g},null)]),e.subtitle&&N("div",{class:"v-file-upload-subtitle"},[e.subtitle])]),R(kr,{"model-value":a.value,contained:!0,scrim:e.scrim},null),r.input?.({inputNode:k})??k]}),i.value.length>0&&N("div",{class:"v-file-upload-items"},[i.value.map((T,A)=>{const H={file:T,props:{"onClick:remove":()=>p(A)}};return R(Ye,{key:A,defaults:{VFileUploadItem:{file:T,clearable:e.clearable,disabled:e.disabled,showSize:e.showSize}}},{default:()=>[r.item?.(H)??R($C,{key:A,"onClick:remove":()=>p(A)},r)]})})])])})}}),jC=te({id:String,interactive:Boolean,text:String,...Fl(Ci({closeOnBack:!1,location:"end",locationStrategy:"connected",eager:!0,minWidth:0,offset:10,openOnClick:!1,openOnHover:!0,origin:"auto",scrim:!1,scrollStrategy:"reposition",transition:null}),["absolute","persistent"])},"VTooltip"),WC=ge()({name:"VTooltip",props:jC(),emits:{"update:modelValue":e=>!0},setup(e,t){let{slots:n}=t;const r=Vt(e,"modelValue"),{scopeId:s}=wi(),o=Ks(),i=X(()=>e.id||`v-tooltip-${o}`),a=J(),l=U(()=>e.location.split(" ").length>1?e.location:e.location+" center"),u=U(()=>e.origin==="auto"||e.origin==="overlap"||e.origin.split(" ").length>1||e.location.split(" ").length>1?e.origin:e.origin+" center"),c=X(()=>e.transition!=null?e.transition:r.value?"scale-transition":"fade-transition"),f=U(()=>Pe({"aria-describedby":i.value},e.activatorProps));return ye(()=>{const d=kr.filterProps(e);return R(kr,Pe({ref:a,class:["v-tooltip",{"v-tooltip--interactive":e.interactive},e.class],style:e.style,id:i.value},d,{modelValue:r.value,"onUpdate:modelValue":m=>r.value=m,transition:c.value,absolute:!0,location:l.value,origin:u.value,persistent:!0,role:"tooltip",activatorProps:f.value,_disableGlobalStack:!0},s),{activator:n.activator,default:function(){for(var m=arguments.length,g=new Array(m),p=0;p<m;p++)g[p]=arguments[p];return n.default?.(...g)??e.text}})}),eo({},a)}}),zC={key:0,id:"loader"},KC={key:1},GC={key:2,class:"default_table"},qC={key:3,id:"error"},YC={__name:"Files",setup(e){const t=Xl(),n=ec(t),r=hi(),s=J(!0),o=J(!1),i=J(""),a=J(""),l=J({}),u=J(null),c=J(""),f=J(""),d=J([]),m=J([]),g=J(!1),p={required:T=>T&&T.length>0||"Selecteer ten minste één bestand.",maxFiles:T=>T&&T.length<=5||"Maximaal 5 bestanden tegelijk toegestaan.",totalFileSize:T=>{if(!T||T.length===0)return!0;const A=T.reduce((V,D)=>V+D.size,0),H=10*1024*1024;return A<H||`Totale bestandsgrootte mag maximaal ${H/(1024*1024)} MB zijn.`}};Gt(()=>{w()});const w=async T=>{try{h(),v()}catch(A){f.value="Foutmelding: "+(A.message??"onbekende fout"),console.error("Error: ",A)}finally{}},h=async()=>{const T=await n.get("?action=getSystemInstructions");c.value=T.data.data.instructions},v=async()=>{const T=await n.get("?action=filelist");d.value=T.data.data,s.value=!1},b=async T=>{try{const A=await n.get(`?action=filedelete&id=${T.id}`)}catch(A){f.value="Foutmelding: "+(A.message??"onbekende fout"),console.error("Error: ",A)}finally{}},y=T=>{i.value="Item Verwijderen",a.value="Weet je zeker dat je dit item permanent wilt verwijderen? Deze actie kan niet ongedaan worden gemaakt.",l.value={color:"blue",confirmText:"Verwijder",confirmColor:"red",cancelText:"Annuleren",width:450},o.value=!0,u.value=T},E=()=>{b(u.value),r.showMessage("Item succesvol verwijderd!","success"),d.value=d.value.filter(T=>T.id!==u.value.id),u.value=null},P=async()=>{s.value=!0;try{const T=await n.post("?action=SetSystemInstructions",{system_instructions:c.value});r.showMessage("Opslaan gelukt","success")}catch(T){f.value="Foutmelding: "+(T.message??"onbekende fout"),console.error("Error: ",T)}finally{s.value=!1}},k=async()=>{if(m.value.length===0){r.showMessage("Geen bestanden geselecteerd.","warning");return}g.value=!0;const T=new FormData;m.value.forEach((A,H)=>{T.append(`bestand_uploadedFile[${H}]`,A)});try{const A=await n.post("?action=fileupload",T,{headers:{"Content-Type":"multipart/form-data"}});if(A.data.success){r.showMessage("Opslaan gelukt","success"),m.value=[];const H=await n.get("?action=filelist");d.value=H.data.data}else if(console.error("Upload error:",A.data),A.data.data.errors)for(const H of A.data.data.errors)r.showMessage(H,"error")}catch(A){console.error("Upload error:",A),A.code==="ERR_NETWORK"||A.message==="Network Error"?r.showMessage("Fout bij het uploaden: Zorg ervoor dat het bestand niet geopend is in een andere toepassing (zoals Word) en probeer het opnieuw.","error"):A.response&&A.response.data&&A.response.data.message?r.showMessage(`Upload mislukt: ${A.response.data.message}`,"error"):r.showMessage("Er is een netwerkfout opgetreden tijdens het uploaden.","error")}finally{g.value=!1}};return(T,A)=>{const H=em("v-icon-btn");return qe(),rt("div",null,[A[11]||(A[11]=N("h1",null,"GPT - bestanden",-1)),R(Zo,{style:{display:"flex",gap:"10px"}},{default:Ue(()=>[R(Lg,{label:"Systeem instructions",modelValue:c.value,"onUpdate:modelValue":A[0]||(A[0]=V=>c.value=V)},null,8,["modelValue"]),N("div",null,[R(Gn,{color:"primary",class:"mt-4 text-xl",onClick:P},{default:Ue(()=>A[3]||(A[3]=[ut(" Opslaan ")])),_:1,__:[3]}),A[4]||(A[4]=N("br",null,null,-1)),A[5]||(A[5]=N("br",null,null,-1)),R(WC,{text:"Je kunt de system instructions hier aanpassen. Deze instructies worden gebruikt om de AI assistent te sturen in zijn antwoorden. Pas bij het starten van een nieuw gesprek zal de nieuwe system intsruction worden gebruikt. Let op: bewaar je wijzigingen zelf, er word geen geschiedienis bijgehouden."},{activator:Ue(({props:V})=>[R(H,Pe({icon:"mdi-information-outline"},V),null,16)]),_:1})])]),_:1}),R(Zo,{style:{display:"flex",gap:"10px"}},{default:Ue(()=>[R(ot(HC),{style:{"flex-grow":"1"},modelValue:m.value,"onUpdate:modelValue":A[1]||(A[1]=V=>m.value=V),label:"Selecteer een bestand",variant:"outlined","prepend-icon":"mdi-paperclip","show-size":"",counter:"",multiple:"",rules:[p.required,p.fileSize],density:"compact",accept:`text/plain,
          text/html,
          text/csv,
          text/markdown,
          application/json,
          application/pdf,
          application/msword,
          application/msword,
          application/vnd.openxmlformats-officedocument.wordprocessingml.document,
          application/vnd.openxmlformats-officedocument.presentationml.presentation`},null,8,["modelValue","rules"]),R(Gn,{color:"primary",class:"mt-4 text-xl",loading:g.value,disabled:!m.value.length===0||g.value,onClick:k},{default:Ue(()=>[R(_t,{start:""},{default:Ue(()=>A[6]||(A[6]=[ut("mdi-upload")])),_:1,__:[6]}),A[7]||(A[7]=ut(" Uploaden "))]),_:1,__:[7]},8,["loading","disabled"])]),_:1}),N("div",null,[s.value?(qe(),rt("div",zC)):xn("",!0),d.value.length==0&&!s.value?(qe(),rt("span",KC,A[8]||(A[8]=[N("br",null,null,-1),ut(" Geen bestanden gevonden. Probeer later opnieuw of upload een bestand. ")]))):xn("",!0),d.value.length>0?(qe(),rt("table",GC,[A[10]||(A[10]=N("thead",null,[N("tr",{class:"dataTableHeadingRow"},[N("td",null,"Bestandsnaam"),N("td",null,"Acties")])],-1)),N("tbody",null,[(qe(!0),rt(ke,null,Lo(d.value,(V,D)=>(qe(),rt("tr",{key:D,class:"dataTableRow"},[N("td",null,tt(V.filename),1),N("td",null,[R(_t,{color:"indufastRed",onClick:G=>y(V)},{default:Ue(()=>A[9]||(A[9]=[ut("mdi-delete ")])),_:2,__:[9]},1032,["onClick"])])]))),128))])])):xn("",!0),f.value!=""?(qe(),rt("div",qC,tt(f.value),1)):xn("",!0)]),R(Mg,{modelValue:o.value,"onUpdate:modelValue":A[2]||(A[2]=V=>o.value=V),title:i.value,message:a.value,options:l.value,onConfirm:E},null,8,["modelValue","title","message","options"])])}}},Bs=Symbol.for("vuetify:v-expansion-panel"),jg=te({...Ie(),...Ih()},"VExpansionPanelText"),el=ge()({name:"VExpansionPanelText",props:jg(),setup(e,t){let{slots:n}=t;const r=Le(Bs);if(!r)throw new Error("[Vuetify] v-expansion-panel-text needs to be placed inside v-expansion-panel");const{hasContent:s,onAfterLeave:o}=Oh(e,r.isSelected);return ye(()=>R(Tg,{onAfterLeave:o},{default:()=>[Kt(N("div",{class:de(["v-expansion-panel-text",e.class]),style:be(e.style)},[n.default&&s.value&&N("div",{class:"v-expansion-panel-text__wrapper"},[n.default?.()])]),[[Ys,r.isSelected.value]])]})),{}}}),Wg=te({color:String,expandIcon:{type:it,default:"$expand"},collapseIcon:{type:it,default:"$collapse"},hideActions:Boolean,focusable:Boolean,static:Boolean,ripple:{type:[Boolean,Object],default:!1},readonly:Boolean,...Ie(),...tr()},"VExpansionPanelTitle"),tl=ge()({name:"VExpansionPanelTitle",directives:{vRipple:Tr},props:Wg(),setup(e,t){let{slots:n}=t;const r=Le(Bs);if(!r)throw new Error("[Vuetify] v-expansion-panel-title needs to be placed inside v-expansion-panel");const{backgroundColorClasses:s,backgroundColorStyles:o}=mn(()=>e.color),{dimensionStyles:i}=nr(e),a=U(()=>({collapseIcon:e.collapseIcon,disabled:r.disabled.value,expanded:r.isSelected.value,expandIcon:e.expandIcon,readonly:e.readonly})),l=X(()=>r.isSelected.value?e.collapseIcon:e.expandIcon);return ye(()=>Kt(N("button",{class:de(["v-expansion-panel-title",{"v-expansion-panel-title--active":r.isSelected.value,"v-expansion-panel-title--focusable":e.focusable,"v-expansion-panel-title--static":e.static},s.value,e.class]),style:be([o.value,i.value,e.style]),type:"button",tabindex:r.disabled.value?-1:void 0,disabled:r.disabled.value,"aria-expanded":r.isSelected.value,onClick:e.readonly?void 0:r.toggle},[N("span",{class:"v-expansion-panel-title__overlay"},null),n.default?.(a.value),!e.hideActions&&R(Ye,{defaults:{VIcon:{icon:l.value}}},{default:()=>[N("span",{class:"v-expansion-panel-title__icon"},[n.actions?.(a.value)??R(_t,null,null)])]})]),[[Tr,e.ripple]])),{}}}),zg=te({title:String,text:String,bgColor:String,...Qn(),...ih(),...$t(),...gt(),...Wg(),...jg()},"VExpansionPanel"),XC=ge()({name:"VExpansionPanel",props:zg(),emits:{"group:selected":e=>!0},setup(e,t){let{slots:n}=t;const r=ah(e,Bs),{backgroundColorClasses:s,backgroundColorStyles:o}=mn(()=>e.bgColor),{elevationClasses:i}=er(e),{roundedClasses:a}=Bt(e),l=X(()=>r?.disabled.value||e.disabled),u=U(()=>r.group.items.value.reduce((d,m,g)=>(r.group.selected.value.includes(m.id)&&d.push(g),d),[])),c=U(()=>{const d=r.group.items.value.findIndex(m=>m.id===r.id);return!r.isSelected.value&&u.value.some(m=>m-d===1)}),f=U(()=>{const d=r.group.items.value.findIndex(m=>m.id===r.id);return!r.isSelected.value&&u.value.some(m=>m-d===-1)});return Ft(Bs,r),ye(()=>{const d=!!(n.text||e.text),m=!!(n.title||e.title),g=tl.filterProps(e),p=el.filterProps(e);return R(e.tag,{class:de(["v-expansion-panel",{"v-expansion-panel--active":r.isSelected.value,"v-expansion-panel--before-active":c.value,"v-expansion-panel--after-active":f.value,"v-expansion-panel--disabled":l.value},a.value,s.value,e.class]),style:be([o.value,e.style])},{default:()=>[N("div",{class:de(["v-expansion-panel__shadow",...i.value])},null),R(Ye,{defaults:{VExpansionPanelTitle:{...g},VExpansionPanelText:{...p}}},{default:()=>[m&&R(tl,{key:"title"},{default:()=>[n.title?n.title():e.title]}),d&&R(el,{key:"text"},{default:()=>[n.text?n.text():e.text]}),n.default?.()]})]})}),{groupItem:r}}}),JC=["default","accordion","inset","popout"],ZC=te({flat:Boolean,...oh(),...gi(zg(),["bgColor","collapseIcon","color","eager","elevation","expandIcon","focusable","hideActions","readonly","ripple","rounded","tile","static"]),...ct(),...Ie(),...gt(),variant:{type:String,default:"default",validator:e=>JC.includes(e)}},"VExpansionPanels"),QC=ge()({name:"VExpansionPanels",props:ZC(),emits:{"update:modelValue":e=>!0},setup(e,t){let{slots:n}=t;const{next:r,prev:s}=lh(e,Bs),{themeClasses:o}=ht(e),i=X(()=>e.variant&&`v-expansion-panels--variant-${e.variant}`);return Xs({VExpansionPanel:{bgColor:X(()=>e.bgColor),collapseIcon:X(()=>e.collapseIcon),color:X(()=>e.color),eager:X(()=>e.eager),elevation:X(()=>e.elevation),expandIcon:X(()=>e.expandIcon),focusable:X(()=>e.focusable),hideActions:X(()=>e.hideActions),readonly:X(()=>e.readonly),ripple:X(()=>e.ripple),rounded:X(()=>e.rounded),static:X(()=>e.static)}}),ye(()=>R(e.tag,{class:de(["v-expansion-panels",{"v-expansion-panels--flat":e.flat,"v-expansion-panels--tile":e.tile},o.value,i.value,e.class]),style:be(e.style)},{default:()=>[n.default?.({prev:s,next:r})]})),{next:r,prev:s}}}),e1={key:0,id:"loader"},t1={key:1},n1={style:{"font-weight":"bold","padding-bottom":"5px","font-size":"1.6rem"}},r1=["innerHTML"],s1={key:2,id:"error"},o1={__name:"History",setup(e){const t=Xl(),n=ec(t),r=hi(),s=J(!0),o=J(!1),i=J(""),a=J(""),l=J({}),u=J(null),c=J(""),f=J([]);Gt(()=>{d()});const d=async v=>{try{m()}catch(b){c.value="Foutmelding: "+(b.message??"onbekende fout"),console.error("Error: ",b)}finally{}},m=async()=>{const v=await n.get("?action=historylist");f.value=v.data.data,s.value=!1},g=async()=>{s.value=!0;const v=await n.get("?action=historylistfill");f.value=v.data.data,s.value=!1},p=async v=>{try{const b=await n.get(`?action=threaddelete&id=${v.id}`)}catch(b){c.value="Foutmelding: "+(b.message??"onbekende fout"),console.error("Error: ",b)}finally{}},w=v=>{i.value="Item Verwijderen",a.value="Weet je zeker dat je dit item permanent wilt verwijderen? Deze actie kan niet ongedaan worden gemaakt.",l.value={color:"blue",confirmText:"Verwijder",confirmColor:"red",cancelText:"Annuleren",width:450},o.value=!0,u.value=v},h=()=>{r.showMessage("Item succesvol verwijderd!","success"),p(u.value),f.value=f.value.filter(v=>v.thread_id!==u.value.thread_id)};return(v,b)=>(qe(),rt("div",null,[b[6]||(b[6]=N("h1",null,"GPT - geschiedenis",-1)),N("div",null,[N("input",{type:"submit",id:"ask",class:"gsd-btn gsd-btn-primary",value:"Ververs geschiedenis",onClick:b[0]||(b[0]=So(y=>g(),["prevent"]))}),b[4]||(b[4]=N("br",null,null,-1)),b[5]||(b[5]=N("br",null,null,-1)),s.value?(qe(),rt("div",e1)):xn("",!0),f.value.length==0&&!s.value?(qe(),rt("span",t1,b[2]||(b[2]=[N("br",null,null,-1),N("br",null,null,-1),ut(" Geen geschiedenis gevonden. Probeer later opnieuw of stel een vraag aan de AI assistent. "),N("br",null,null,-1),N("br",null,null,-1)]))):xn("",!0),R(QC,null,{default:Ue(()=>[(qe(!0),rt(ke,null,Lo(f.value,(y,E)=>(qe(),fi(XC,{key:E},{default:Ue(()=>[R(tl,{"expand-icon":"mdi-menu-down"},{default:Ue(()=>[R(_t,{color:"indufastRed",onClick:P=>w(y)},{default:Ue(()=>b[3]||(b[3]=[ut("mdi-delete ")])),_:2,__:[3]},1032,["onClick"]),ut(" "+tt(y.insertTS)+" - "+tt(y.thread_id),1)]),_:2},1024),R(el,null,{default:Ue(()=>[(qe(!0),rt(ke,null,Lo(y.messages,(P,k)=>(qe(),rt("div",{key:k,style:{"padding-bottom":"25px"}},[N("div",n1,tt(P.question),1),N("div",{innerHTML:P.answer},null,8,r1)]))),128))]),_:2},1024)]),_:2},1024))),128))]),_:1}),c.value!=""?(qe(),rt("div",s1,tt(c.value),1)):xn("",!0)]),R(Mg,{modelValue:o.value,"onUpdate:modelValue":b[1]||(b[1]=y=>o.value=y),title:i.value,message:a.value,options:l.value,onConfirm:h},null,8,["modelValue","title","message","options"])]))}},i1=[{path:"/chat",name:"chat",component:cC},{path:"/files",name:"files",component:YC},{path:"/history",name:"history",component:o1},{path:"/",redirect:"/chat"}],Kg=nw({history:OS("./"),base:"/",routes:i1,linkActiveClass:"activeNav"});/*!
  * shared v11.1.9
  * (c) 2025 kazuya kawaguchi
  * Released under the MIT License.
  */const Qo=typeof window<"u",rr=(e,t=!1)=>t?Symbol.for(e):Symbol(e),a1=(e,t,n)=>l1({l:e,k:t,s:n}),l1=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),et=e=>typeof e=="number"&&isFinite(e),c1=e=>nc(e)==="[object Date]",Jr=e=>nc(e)==="[object RegExp]",Ni=e=>Ce(e)&&Object.keys(e).length===0,at=Object.assign,u1=Object.create,$e=(e=null)=>u1(e);let Lf;const gr=()=>Lf||(Lf=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:$e());function Nf(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const f1=Object.prototype.hasOwnProperty;function Yt(e,t){return f1.call(e,t)}const Je=Array.isArray,ze=e=>typeof e=="function",se=e=>typeof e=="string",Oe=e=>typeof e=="boolean",Re=e=>e!==null&&typeof e=="object",d1=e=>Re(e)&&ze(e.then)&&ze(e.catch),Gg=Object.prototype.toString,nc=e=>Gg.call(e),Ce=e=>nc(e)==="[object Object]",m1=e=>e==null?"":Je(e)||Ce(e)&&e.toString===Gg?JSON.stringify(e,null,2):String(e);function rc(e,t=""){return e.reduce((n,r,s)=>s===0?n+r:n+t+r,"")}function h1(e,t){typeof console<"u"&&(console.warn("[intlify] "+e),t&&console.warn(t.stack))}const ho=e=>!Re(e)||Je(e);function ko(e,t){if(ho(e)||ho(t))throw new Error("Invalid value");const n=[{src:e,des:t}];for(;n.length;){const{src:r,des:s}=n.pop();Object.keys(r).forEach(o=>{o!=="__proto__"&&(Re(r[o])&&!Re(s[o])&&(s[o]=Array.isArray(r[o])?[]:$e()),ho(s[o])||ho(r[o])?s[o]=r[o]:n.push({src:r[o],des:s[o]}))})}}/*!
  * message-compiler v11.1.9
  * (c) 2025 kazuya kawaguchi
  * Released under the MIT License.
  */function g1(e,t,n){return{line:e,column:t,offset:n}}function nl(e,t,n){return{start:e,end:t}}const Ve={EXPECTED_TOKEN:1,INVALID_TOKEN_IN_PLACEHOLDER:2,UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER:3,UNKNOWN_ESCAPE_SEQUENCE:4,INVALID_UNICODE_ESCAPE_SEQUENCE:5,UNBALANCED_CLOSING_BRACE:6,UNTERMINATED_CLOSING_BRACE:7,EMPTY_PLACEHOLDER:8,NOT_ALLOW_NEST_PLACEHOLDER:9,INVALID_LINKED_FORMAT:10,MUST_HAVE_MESSAGES_IN_PLURAL:11,UNEXPECTED_EMPTY_LINKED_MODIFIER:12,UNEXPECTED_EMPTY_LINKED_KEY:13,UNEXPECTED_LEXICAL_ANALYSIS:14},v1=17;function Di(e,t,n={}){const{domain:r,messages:s,args:o}=n,i=e,a=new SyntaxError(String(i));return a.code=e,t&&(a.location=t),a.domain=r,a}function p1(e){throw e}const Sn=" ",y1="\r",Et=`
`,b1="\u2028",_1="\u2029";function S1(e){const t=e;let n=0,r=1,s=1,o=0;const i=k=>t[k]===y1&&t[k+1]===Et,a=k=>t[k]===Et,l=k=>t[k]===_1,u=k=>t[k]===b1,c=k=>i(k)||a(k)||l(k)||u(k),f=()=>n,d=()=>r,m=()=>s,g=()=>o,p=k=>i(k)||l(k)||u(k)?Et:t[k],w=()=>p(n),h=()=>p(n+o);function v(){return o=0,c(n)&&(r++,s=0),i(n)&&n++,n++,s++,t[n]}function b(){return i(n+o)&&o++,o++,t[n+o]}function y(){n=0,r=1,s=1,o=0}function E(k=0){o=k}function P(){const k=n+o;for(;k!==n;)v();o=0}return{index:f,line:d,column:m,peekOffset:g,charAt:p,currentChar:w,currentPeek:h,next:v,peek:b,reset:y,resetPeek:E,skipToPeek:P}}const Dn=void 0,w1=".",Df="'",E1="tokenizer";function C1(e,t={}){const n=t.location!==!1,r=S1(e),s=()=>r.index(),o=()=>g1(r.line(),r.column(),r.index()),i=o(),a=s(),l={currentType:13,offset:a,startLoc:i,endLoc:i,lastType:13,lastOffset:a,lastStartLoc:i,lastEndLoc:i,braceNest:0,inLinked:!1,text:""},u=()=>l,{onError:c}=t;function f(_,S,O,...M){const Q=u();if(S.column+=O,S.offset+=O,c){const K=n?nl(Q.startLoc,S):null,I=Di(_,K,{domain:E1,args:M});c(I)}}function d(_,S,O){_.endLoc=o(),_.currentType=S;const M={type:S};return n&&(M.loc=nl(_.startLoc,_.endLoc)),O!=null&&(M.value=O),M}const m=_=>d(_,13);function g(_,S){return _.currentChar()===S?(_.next(),S):(f(Ve.EXPECTED_TOKEN,o(),0,S),"")}function p(_){let S="";for(;_.currentPeek()===Sn||_.currentPeek()===Et;)S+=_.currentPeek(),_.peek();return S}function w(_){const S=p(_);return _.skipToPeek(),S}function h(_){if(_===Dn)return!1;const S=_.charCodeAt(0);return S>=97&&S<=122||S>=65&&S<=90||S===95}function v(_){if(_===Dn)return!1;const S=_.charCodeAt(0);return S>=48&&S<=57}function b(_,S){const{currentType:O}=S;if(O!==2)return!1;p(_);const M=h(_.currentPeek());return _.resetPeek(),M}function y(_,S){const{currentType:O}=S;if(O!==2)return!1;p(_);const M=_.currentPeek()==="-"?_.peek():_.currentPeek(),Q=v(M);return _.resetPeek(),Q}function E(_,S){const{currentType:O}=S;if(O!==2)return!1;p(_);const M=_.currentPeek()===Df;return _.resetPeek(),M}function P(_,S){const{currentType:O}=S;if(O!==7)return!1;p(_);const M=_.currentPeek()===".";return _.resetPeek(),M}function k(_,S){const{currentType:O}=S;if(O!==8)return!1;p(_);const M=h(_.currentPeek());return _.resetPeek(),M}function T(_,S){const{currentType:O}=S;if(!(O===7||O===11))return!1;p(_);const M=_.currentPeek()===":";return _.resetPeek(),M}function A(_,S){const{currentType:O}=S;if(O!==9)return!1;const M=()=>{const K=_.currentPeek();return K==="{"?h(_.peek()):K==="@"||K==="|"||K===":"||K==="."||K===Sn||!K?!1:K===Et?(_.peek(),M()):V(_,!1)},Q=M();return _.resetPeek(),Q}function H(_){p(_);const S=_.currentPeek()==="|";return _.resetPeek(),S}function V(_,S=!0){const O=(Q=!1,K="")=>{const I=_.currentPeek();return I==="{"||I==="@"||!I?Q:I==="|"?!(K===Sn||K===Et):I===Sn?(_.peek(),O(!0,Sn)):I===Et?(_.peek(),O(!0,Et)):!0},M=O();return S&&_.resetPeek(),M}function D(_,S){const O=_.currentChar();return O===Dn?Dn:S(O)?(_.next(),O):null}function G(_){const S=_.charCodeAt(0);return S>=97&&S<=122||S>=65&&S<=90||S>=48&&S<=57||S===95||S===36}function ee(_){return D(_,G)}function ne(_){const S=_.charCodeAt(0);return S>=97&&S<=122||S>=65&&S<=90||S>=48&&S<=57||S===95||S===36||S===45}function oe(_){return D(_,ne)}function Z(_){const S=_.charCodeAt(0);return S>=48&&S<=57}function re(_){return D(_,Z)}function pe(_){const S=_.charCodeAt(0);return S>=48&&S<=57||S>=65&&S<=70||S>=97&&S<=102}function ue(_){return D(_,pe)}function xe(_){let S="",O="";for(;S=re(_);)O+=S;return O}function _e(_){let S="";for(;;){const O=_.currentChar();if(O==="{"||O==="}"||O==="@"||O==="|"||!O)break;if(O===Sn||O===Et)if(V(_))S+=O,_.next();else{if(H(_))break;S+=O,_.next()}else S+=O,_.next()}return S}function Me(_){w(_);let S="",O="";for(;S=oe(_);)O+=S;return _.currentChar()===Dn&&f(Ve.UNTERMINATED_CLOSING_BRACE,o(),0),O}function Ge(_){w(_);let S="";return _.currentChar()==="-"?(_.next(),S+=`-${xe(_)}`):S+=xe(_),_.currentChar()===Dn&&f(Ve.UNTERMINATED_CLOSING_BRACE,o(),0),S}function Se(_){return _!==Df&&_!==Et}function Ne(_){w(_),g(_,"'");let S="",O="";for(;S=D(_,Se);)S==="\\"?O+=F(_):O+=S;const M=_.currentChar();return M===Et||M===Dn?(f(Ve.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER,o(),0),M===Et&&(_.next(),g(_,"'")),O):(g(_,"'"),O)}function F(_){const S=_.currentChar();switch(S){case"\\":case"'":return _.next(),`\\${S}`;case"u":return q(_,S,4);case"U":return q(_,S,6);default:return f(Ve.UNKNOWN_ESCAPE_SEQUENCE,o(),0,S),""}}function q(_,S,O){g(_,S);let M="";for(let Q=0;Q<O;Q++){const K=ue(_);if(!K){f(Ve.INVALID_UNICODE_ESCAPE_SEQUENCE,o(),0,`\\${S}${M}${_.currentChar()}`);break}M+=K}return`\\${S}${M}`}function Y(_){return _!=="{"&&_!=="}"&&_!==Sn&&_!==Et}function ie(_){w(_);let S="",O="";for(;S=D(_,Y);)O+=S;return O}function Te(_){let S="",O="";for(;S=ee(_);)O+=S;return O}function C(_){const S=O=>{const M=_.currentChar();return M==="{"||M==="@"||M==="|"||M==="("||M===")"||!M||M===Sn?O:(O+=M,_.next(),S(O))};return S("")}function x(_){w(_);const S=g(_,"|");return w(_),S}function L(_,S){let O=null;switch(_.currentChar()){case"{":return S.braceNest>=1&&f(Ve.NOT_ALLOW_NEST_PLACEHOLDER,o(),0),_.next(),O=d(S,2,"{"),w(_),S.braceNest++,O;case"}":return S.braceNest>0&&S.currentType===2&&f(Ve.EMPTY_PLACEHOLDER,o(),0),_.next(),O=d(S,3,"}"),S.braceNest--,S.braceNest>0&&w(_),S.inLinked&&S.braceNest===0&&(S.inLinked=!1),O;case"@":return S.braceNest>0&&f(Ve.UNTERMINATED_CLOSING_BRACE,o(),0),O=j(_,S)||m(S),S.braceNest=0,O;default:{let Q=!0,K=!0,I=!0;if(H(_))return S.braceNest>0&&f(Ve.UNTERMINATED_CLOSING_BRACE,o(),0),O=d(S,1,x(_)),S.braceNest=0,S.inLinked=!1,O;if(S.braceNest>0&&(S.currentType===4||S.currentType===5||S.currentType===6))return f(Ve.UNTERMINATED_CLOSING_BRACE,o(),0),S.braceNest=0,z(_,S);if(Q=b(_,S))return O=d(S,4,Me(_)),w(_),O;if(K=y(_,S))return O=d(S,5,Ge(_)),w(_),O;if(I=E(_,S))return O=d(S,6,Ne(_)),w(_),O;if(!Q&&!K&&!I)return O=d(S,12,ie(_)),f(Ve.INVALID_TOKEN_IN_PLACEHOLDER,o(),0,O.value),w(_),O;break}}return O}function j(_,S){const{currentType:O}=S;let M=null;const Q=_.currentChar();switch((O===7||O===8||O===11||O===9)&&(Q===Et||Q===Sn)&&f(Ve.INVALID_LINKED_FORMAT,o(),0),Q){case"@":return _.next(),M=d(S,7,"@"),S.inLinked=!0,M;case".":return w(_),_.next(),d(S,8,".");case":":return w(_),_.next(),d(S,9,":");default:return H(_)?(M=d(S,1,x(_)),S.braceNest=0,S.inLinked=!1,M):P(_,S)||T(_,S)?(w(_),j(_,S)):k(_,S)?(w(_),d(S,11,Te(_))):A(_,S)?(w(_),Q==="{"?L(_,S)||M:d(S,10,C(_))):(O===7&&f(Ve.INVALID_LINKED_FORMAT,o(),0),S.braceNest=0,S.inLinked=!1,z(_,S))}}function z(_,S){let O={type:13};if(S.braceNest>0)return L(_,S)||m(S);if(S.inLinked)return j(_,S)||m(S);switch(_.currentChar()){case"{":return L(_,S)||m(S);case"}":return f(Ve.UNBALANCED_CLOSING_BRACE,o(),0),_.next(),d(S,3,"}");case"@":return j(_,S)||m(S);default:{if(H(_))return O=d(S,1,x(_)),S.braceNest=0,S.inLinked=!1,O;if(V(_))return d(S,0,_e(_));break}}return O}function W(){const{currentType:_,offset:S,startLoc:O,endLoc:M}=l;return l.lastType=_,l.lastOffset=S,l.lastStartLoc=O,l.lastEndLoc=M,l.offset=s(),l.startLoc=o(),r.currentChar()===Dn?d(l,13):z(r,l)}return{nextToken:W,currentOffset:s,currentPosition:o,context:u}}const x1="parser",T1=/(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;function k1(e,t,n){switch(e){case"\\\\":return"\\";case"\\'":return"'";default:{const r=parseInt(t||n,16);return r<=55295||r>=57344?String.fromCodePoint(r):"�"}}}function A1(e={}){const t=e.location!==!1,{onError:n}=e;function r(h,v,b,y,...E){const P=h.currentPosition();if(P.offset+=y,P.column+=y,n){const k=t?nl(b,P):null,T=Di(v,k,{domain:x1,args:E});n(T)}}function s(h,v,b){const y={type:h};return t&&(y.start=v,y.end=v,y.loc={start:b,end:b}),y}function o(h,v,b,y){t&&(h.end=v,h.loc&&(h.loc.end=b))}function i(h,v){const b=h.context(),y=s(3,b.offset,b.startLoc);return y.value=v,o(y,h.currentOffset(),h.currentPosition()),y}function a(h,v){const b=h.context(),{lastOffset:y,lastStartLoc:E}=b,P=s(5,y,E);return P.index=parseInt(v,10),h.nextToken(),o(P,h.currentOffset(),h.currentPosition()),P}function l(h,v){const b=h.context(),{lastOffset:y,lastStartLoc:E}=b,P=s(4,y,E);return P.key=v,h.nextToken(),o(P,h.currentOffset(),h.currentPosition()),P}function u(h,v){const b=h.context(),{lastOffset:y,lastStartLoc:E}=b,P=s(9,y,E);return P.value=v.replace(T1,k1),h.nextToken(),o(P,h.currentOffset(),h.currentPosition()),P}function c(h){const v=h.nextToken(),b=h.context(),{lastOffset:y,lastStartLoc:E}=b,P=s(8,y,E);return v.type!==11?(r(h,Ve.UNEXPECTED_EMPTY_LINKED_MODIFIER,b.lastStartLoc,0),P.value="",o(P,y,E),{nextConsumeToken:v,node:P}):(v.value==null&&r(h,Ve.UNEXPECTED_LEXICAL_ANALYSIS,b.lastStartLoc,0,on(v)),P.value=v.value||"",o(P,h.currentOffset(),h.currentPosition()),{node:P})}function f(h,v){const b=h.context(),y=s(7,b.offset,b.startLoc);return y.value=v,o(y,h.currentOffset(),h.currentPosition()),y}function d(h){const v=h.context(),b=s(6,v.offset,v.startLoc);let y=h.nextToken();if(y.type===8){const E=c(h);b.modifier=E.node,y=E.nextConsumeToken||h.nextToken()}switch(y.type!==9&&r(h,Ve.UNEXPECTED_LEXICAL_ANALYSIS,v.lastStartLoc,0,on(y)),y=h.nextToken(),y.type===2&&(y=h.nextToken()),y.type){case 10:y.value==null&&r(h,Ve.UNEXPECTED_LEXICAL_ANALYSIS,v.lastStartLoc,0,on(y)),b.key=f(h,y.value||"");break;case 4:y.value==null&&r(h,Ve.UNEXPECTED_LEXICAL_ANALYSIS,v.lastStartLoc,0,on(y)),b.key=l(h,y.value||"");break;case 5:y.value==null&&r(h,Ve.UNEXPECTED_LEXICAL_ANALYSIS,v.lastStartLoc,0,on(y)),b.key=a(h,y.value||"");break;case 6:y.value==null&&r(h,Ve.UNEXPECTED_LEXICAL_ANALYSIS,v.lastStartLoc,0,on(y)),b.key=u(h,y.value||"");break;default:{r(h,Ve.UNEXPECTED_EMPTY_LINKED_KEY,v.lastStartLoc,0);const E=h.context(),P=s(7,E.offset,E.startLoc);return P.value="",o(P,E.offset,E.startLoc),b.key=P,o(b,E.offset,E.startLoc),{nextConsumeToken:y,node:b}}}return o(b,h.currentOffset(),h.currentPosition()),{node:b}}function m(h){const v=h.context(),b=v.currentType===1?h.currentOffset():v.offset,y=v.currentType===1?v.endLoc:v.startLoc,E=s(2,b,y);E.items=[];let P=null;do{const A=P||h.nextToken();switch(P=null,A.type){case 0:A.value==null&&r(h,Ve.UNEXPECTED_LEXICAL_ANALYSIS,v.lastStartLoc,0,on(A)),E.items.push(i(h,A.value||""));break;case 5:A.value==null&&r(h,Ve.UNEXPECTED_LEXICAL_ANALYSIS,v.lastStartLoc,0,on(A)),E.items.push(a(h,A.value||""));break;case 4:A.value==null&&r(h,Ve.UNEXPECTED_LEXICAL_ANALYSIS,v.lastStartLoc,0,on(A)),E.items.push(l(h,A.value||""));break;case 6:A.value==null&&r(h,Ve.UNEXPECTED_LEXICAL_ANALYSIS,v.lastStartLoc,0,on(A)),E.items.push(u(h,A.value||""));break;case 7:{const H=d(h);E.items.push(H.node),P=H.nextConsumeToken||null;break}}}while(v.currentType!==13&&v.currentType!==1);const k=v.currentType===1?v.lastOffset:h.currentOffset(),T=v.currentType===1?v.lastEndLoc:h.currentPosition();return o(E,k,T),E}function g(h,v,b,y){const E=h.context();let P=y.items.length===0;const k=s(1,v,b);k.cases=[],k.cases.push(y);do{const T=m(h);P||(P=T.items.length===0),k.cases.push(T)}while(E.currentType!==13);return P&&r(h,Ve.MUST_HAVE_MESSAGES_IN_PLURAL,b,0),o(k,h.currentOffset(),h.currentPosition()),k}function p(h){const v=h.context(),{offset:b,startLoc:y}=v,E=m(h);return v.currentType===13?E:g(h,b,y,E)}function w(h){const v=C1(h,at({},e)),b=v.context(),y=s(0,b.offset,b.startLoc);return t&&y.loc&&(y.loc.source=h),y.body=p(v),e.onCacheKey&&(y.cacheKey=e.onCacheKey(h)),b.currentType!==13&&r(v,Ve.UNEXPECTED_LEXICAL_ANALYSIS,b.lastStartLoc,0,h[b.offset]||""),o(y,v.currentOffset(),v.currentPosition()),y}return{parse:w}}function on(e){if(e.type===13)return"EOF";const t=(e.value||"").replace(/\r?\n/gu,"\\n");return t.length>10?t.slice(0,9)+"…":t}function P1(e,t={}){const n={ast:e,helpers:new Set};return{context:()=>n,helper:o=>(n.helpers.add(o),o)}}function Ff(e,t){for(let n=0;n<e.length;n++)sc(e[n],t)}function sc(e,t){switch(e.type){case 1:Ff(e.cases,t),t.helper("plural");break;case 2:Ff(e.items,t);break;case 6:{sc(e.key,t),t.helper("linked"),t.helper("type");break}case 5:t.helper("interpolate"),t.helper("list");break;case 4:t.helper("interpolate"),t.helper("named");break}}function I1(e,t={}){const n=P1(e);n.helper("normalize"),e.body&&sc(e.body,n);const r=n.context();e.helpers=Array.from(r.helpers)}function O1(e){const t=e.body;return t.type===2?Mf(t):t.cases.forEach(n=>Mf(n)),e}function Mf(e){if(e.items.length===1){const t=e.items[0];(t.type===3||t.type===9)&&(e.static=t.value,delete t.value)}else{const t=[];for(let n=0;n<e.items.length;n++){const r=e.items[n];if(!(r.type===3||r.type===9)||r.value==null)break;t.push(r.value)}if(t.length===e.items.length){e.static=rc(t);for(let n=0;n<e.items.length;n++){const r=e.items[n];(r.type===3||r.type===9)&&delete r.value}}}}function Br(e){switch(e.t=e.type,e.type){case 0:{const t=e;Br(t.body),t.b=t.body,delete t.body;break}case 1:{const t=e,n=t.cases;for(let r=0;r<n.length;r++)Br(n[r]);t.c=n,delete t.cases;break}case 2:{const t=e,n=t.items;for(let r=0;r<n.length;r++)Br(n[r]);t.i=n,delete t.items,t.static&&(t.s=t.static,delete t.static);break}case 3:case 9:case 8:case 7:{const t=e;t.value&&(t.v=t.value,delete t.value);break}case 6:{const t=e;Br(t.key),t.k=t.key,delete t.key,t.modifier&&(Br(t.modifier),t.m=t.modifier,delete t.modifier);break}case 5:{const t=e;t.i=t.index,delete t.index;break}case 4:{const t=e;t.k=t.key,delete t.key;break}}delete e.type}function R1(e,t){const{filename:n,breakLineCode:r,needIndent:s}=t,o=t.location!==!1,i={filename:n,code:"",column:1,line:1,offset:0,map:void 0,breakLineCode:r,needIndent:s,indentLevel:0};o&&e.loc&&(i.source=e.loc.source);const a=()=>i;function l(p,w){i.code+=p}function u(p,w=!0){const h=w?r:"";l(s?h+"  ".repeat(p):h)}function c(p=!0){const w=++i.indentLevel;p&&u(w)}function f(p=!0){const w=--i.indentLevel;p&&u(w)}function d(){u(i.indentLevel)}return{context:a,push:l,indent:c,deindent:f,newline:d,helper:p=>`_${p}`,needIndent:()=>i.needIndent}}function L1(e,t){const{helper:n}=e;e.push(`${n("linked")}(`),Zr(e,t.key),t.modifier?(e.push(", "),Zr(e,t.modifier),e.push(", _type")):e.push(", undefined, _type"),e.push(")")}function N1(e,t){const{helper:n,needIndent:r}=e;e.push(`${n("normalize")}([`),e.indent(r());const s=t.items.length;for(let o=0;o<s&&(Zr(e,t.items[o]),o!==s-1);o++)e.push(", ");e.deindent(r()),e.push("])")}function D1(e,t){const{helper:n,needIndent:r}=e;if(t.cases.length>1){e.push(`${n("plural")}([`),e.indent(r());const s=t.cases.length;for(let o=0;o<s&&(Zr(e,t.cases[o]),o!==s-1);o++)e.push(", ");e.deindent(r()),e.push("])")}}function F1(e,t){t.body?Zr(e,t.body):e.push("null")}function Zr(e,t){const{helper:n}=e;switch(t.type){case 0:F1(e,t);break;case 1:D1(e,t);break;case 2:N1(e,t);break;case 6:L1(e,t);break;case 8:e.push(JSON.stringify(t.value),t);break;case 7:e.push(JSON.stringify(t.value),t);break;case 5:e.push(`${n("interpolate")}(${n("list")}(${t.index}))`,t);break;case 4:e.push(`${n("interpolate")}(${n("named")}(${JSON.stringify(t.key)}))`,t);break;case 9:e.push(JSON.stringify(t.value),t);break;case 3:e.push(JSON.stringify(t.value),t);break}}const M1=(e,t={})=>{const n=se(t.mode)?t.mode:"normal",r=se(t.filename)?t.filename:"message.intl";t.sourceMap;const s=t.breakLineCode!=null?t.breakLineCode:n==="arrow"?";":`
`,o=t.needIndent?t.needIndent:n!=="arrow",i=e.helpers||[],a=R1(e,{filename:r,breakLineCode:s,needIndent:o});a.push(n==="normal"?"function __msg__ (ctx) {":"(ctx) => {"),a.indent(o),i.length>0&&(a.push(`const { ${rc(i.map(c=>`${c}: _${c}`),", ")} } = ctx`),a.newline()),a.push("return "),Zr(a,e),a.deindent(o),a.push("}"),delete e.helpers;const{code:l,map:u}=a.context();return{ast:e,code:l,map:u?u.toJSON():void 0}};function V1(e,t={}){const n=at({},t),r=!!n.jit,s=!!n.minify,o=n.optimize==null?!0:n.optimize,a=A1(n).parse(e);return r?(o&&O1(a),s&&Br(a),{ast:a,code:""}):(I1(a,n),M1(a,n))}/*!
  * core-base v11.1.9
  * (c) 2025 kazuya kawaguchi
  * Released under the MIT License.
  */function $1(){typeof __INTLIFY_PROD_DEVTOOLS__!="boolean"&&(gr().__INTLIFY_PROD_DEVTOOLS__=!1),typeof __INTLIFY_DROP_MESSAGE_COMPILER__!="boolean"&&(gr().__INTLIFY_DROP_MESSAGE_COMPILER__=!1)}function hn(e){return Re(e)&&oc(e)===0&&(Yt(e,"b")||Yt(e,"body"))}const qg=["b","body"];function B1(e){return sr(e,qg)}const Yg=["c","cases"];function U1(e){return sr(e,Yg,[])}const Xg=["s","static"];function H1(e){return sr(e,Xg)}const Jg=["i","items"];function j1(e){return sr(e,Jg,[])}const Zg=["t","type"];function oc(e){return sr(e,Zg)}const Qg=["v","value"];function go(e,t){const n=sr(e,Qg);if(n!=null)return n;throw Us(t)}const ev=["m","modifier"];function W1(e){return sr(e,ev)}const tv=["k","key"];function z1(e){const t=sr(e,tv);if(t)return t;throw Us(6)}function sr(e,t,n){for(let r=0;r<t.length;r++){const s=t[r];if(Yt(e,s)&&e[s]!=null)return e[s]}return n}const nv=[...qg,...Yg,...Xg,...Jg,...tv,...ev,...Qg,...Zg];function Us(e){return new Error(`unhandled node type: ${e}`)}function ma(e){return n=>K1(n,e)}function K1(e,t){const n=B1(t);if(n==null)throw Us(0);if(oc(n)===1){const o=U1(n);return e.plural(o.reduce((i,a)=>[...i,Vf(e,a)],[]))}else return Vf(e,n)}function Vf(e,t){const n=H1(t);if(n!=null)return e.type==="text"?n:e.normalize([n]);{const r=j1(t).reduce((s,o)=>[...s,rl(e,o)],[]);return e.normalize(r)}}function rl(e,t){const n=oc(t);switch(n){case 3:return go(t,n);case 9:return go(t,n);case 4:{const r=t;if(Yt(r,"k")&&r.k)return e.interpolate(e.named(r.k));if(Yt(r,"key")&&r.key)return e.interpolate(e.named(r.key));throw Us(n)}case 5:{const r=t;if(Yt(r,"i")&&et(r.i))return e.interpolate(e.list(r.i));if(Yt(r,"index")&&et(r.index))return e.interpolate(e.list(r.index));throw Us(n)}case 6:{const r=t,s=W1(r),o=z1(r);return e.linked(rl(e,o),s?rl(e,s):void 0,e.type)}case 7:return go(t,n);case 8:return go(t,n);default:throw new Error(`unhandled node on format message part: ${n}`)}}const G1=e=>e;let vo=$e();function q1(e,t={}){let n=!1;const r=t.onError||p1;return t.onError=s=>{n=!0,r(s)},{...V1(e,t),detectError:n}}function Y1(e,t){if(!__INTLIFY_DROP_MESSAGE_COMPILER__&&se(e)){Oe(t.warnHtmlMessage)&&t.warnHtmlMessage;const r=(t.onCacheKey||G1)(e),s=vo[r];if(s)return s;const{ast:o,detectError:i}=q1(e,{...t,location:!1,jit:!0}),a=ma(o);return i?a:vo[r]=a}else{const n=e.cacheKey;if(n){const r=vo[n];return r||(vo[n]=ma(e))}else return ma(e)}}let Hs=null;function X1(e){Hs=e}function J1(e,t,n){Hs&&Hs.emit("i18n:init",{timestamp:Date.now(),i18n:e,version:t,meta:n})}const Z1=Q1("function:translate");function Q1(e){return t=>Hs&&Hs.emit(e,t)}const Tn={INVALID_ARGUMENT:v1,INVALID_DATE_ARGUMENT:18,INVALID_ISO_DATE_ARGUMENT:19,NOT_SUPPORT_LOCALE_PROMISE_VALUE:21,NOT_SUPPORT_LOCALE_ASYNC_FUNCTION:22,NOT_SUPPORT_LOCALE_TYPE:23},ex=24;function kn(e){return Di(e,null,void 0)}function ic(e,t){return t.locale!=null?$f(t.locale):$f(e.locale)}let ha;function $f(e){if(se(e))return e;if(ze(e)){if(e.resolvedOnce&&ha!=null)return ha;if(e.constructor.name==="Function"){const t=e();if(d1(t))throw kn(Tn.NOT_SUPPORT_LOCALE_PROMISE_VALUE);return ha=t}else throw kn(Tn.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION)}else throw kn(Tn.NOT_SUPPORT_LOCALE_TYPE)}function tx(e,t,n){return[...new Set([n,...Je(t)?t:Re(t)?Object.keys(t):se(t)?[t]:[n]])]}function rv(e,t,n){const r=se(n)?n:js,s=e;s.__localeChainCache||(s.__localeChainCache=new Map);let o=s.__localeChainCache.get(r);if(!o){o=[];let i=[n];for(;Je(i);)i=Bf(o,i,t);const a=Je(t)||!Ce(t)?t:t.default?t.default:null;i=se(a)?[a]:a,Je(i)&&Bf(o,i,!1),s.__localeChainCache.set(r,o)}return o}function Bf(e,t,n){let r=!0;for(let s=0;s<t.length&&Oe(r);s++){const o=t[s];se(o)&&(r=nx(e,t[s],n))}return r}function nx(e,t,n){let r;const s=t.split("-");do{const o=s.join("-");r=rx(e,o,n),s.splice(-1,1)}while(s.length&&r===!0);return r}function rx(e,t,n){let r=!1;if(!e.includes(t)&&(r=!0,t)){r=t[t.length-1]!=="!";const s=t.replace(/!/g,"");e.push(s),(Je(n)||Ce(n))&&n[s]&&(r=n[s])}return r}const or=[];or[0]={w:[0],i:[3,0],"[":[4],o:[7]};or[1]={w:[1],".":[2],"[":[4],o:[7]};or[2]={w:[2],i:[3,0],0:[3,0]};or[3]={i:[3,0],0:[3,0],w:[1,1],".":[2,1],"[":[4,1],o:[7,1]};or[4]={"'":[5,0],'"':[6,0],"[":[4,2],"]":[1,3],o:8,l:[4,0]};or[5]={"'":[4,0],o:8,l:[5,0]};or[6]={'"':[4,0],o:8,l:[6,0]};const sx=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function ox(e){return sx.test(e)}function ix(e){const t=e.charCodeAt(0),n=e.charCodeAt(e.length-1);return t===n&&(t===34||t===39)?e.slice(1,-1):e}function ax(e){if(e==null)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function lx(e){const t=e.trim();return e.charAt(0)==="0"&&isNaN(parseInt(e))?!1:ox(t)?ix(t):"*"+t}function cx(e){const t=[];let n=-1,r=0,s=0,o,i,a,l,u,c,f;const d=[];d[0]=()=>{i===void 0?i=a:i+=a},d[1]=()=>{i!==void 0&&(t.push(i),i=void 0)},d[2]=()=>{d[0](),s++},d[3]=()=>{if(s>0)s--,r=4,d[0]();else{if(s=0,i===void 0||(i=lx(i),i===!1))return!1;d[1]()}};function m(){const g=e[n+1];if(r===5&&g==="'"||r===6&&g==='"')return n++,a="\\"+g,d[0](),!0}for(;r!==null;)if(n++,o=e[n],!(o==="\\"&&m())){if(l=ax(o),f=or[r],u=f[l]||f.l||8,u===8||(r=u[0],u[1]!==void 0&&(c=d[u[1]],c&&(a=o,c()===!1))))return;if(r===7)return t}}const Uf=new Map;function ux(e,t){return Re(e)?e[t]:null}function fx(e,t){if(!Re(e))return null;let n=Uf.get(t);if(n||(n=cx(t),n&&Uf.set(t,n)),!n)return null;const r=n.length;let s=e,o=0;for(;o<r;){const i=n[o];if(nv.includes(i)&&hn(s))return null;const a=s[i];if(a===void 0||ze(s))return null;s=a,o++}return s}const dx="11.1.9",Fi=-1,js="en-US",Hf="",jf=e=>`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`;function mx(){return{upper:(e,t)=>t==="text"&&se(e)?e.toUpperCase():t==="vnode"&&Re(e)&&"__v_isVNode"in e?e.children.toUpperCase():e,lower:(e,t)=>t==="text"&&se(e)?e.toLowerCase():t==="vnode"&&Re(e)&&"__v_isVNode"in e?e.children.toLowerCase():e,capitalize:(e,t)=>t==="text"&&se(e)?jf(e):t==="vnode"&&Re(e)&&"__v_isVNode"in e?jf(e.children):e}}let sv;function hx(e){sv=e}let ov;function gx(e){ov=e}let iv;function vx(e){iv=e}let av=null;const px=e=>{av=e},yx=()=>av;let lv=null;const Wf=e=>{lv=e},bx=()=>lv;let zf=0;function _x(e={}){const t=ze(e.onWarn)?e.onWarn:h1,n=se(e.version)?e.version:dx,r=se(e.locale)||ze(e.locale)?e.locale:js,s=ze(r)?js:r,o=Je(e.fallbackLocale)||Ce(e.fallbackLocale)||se(e.fallbackLocale)||e.fallbackLocale===!1?e.fallbackLocale:s,i=Ce(e.messages)?e.messages:ga(s),a=Ce(e.datetimeFormats)?e.datetimeFormats:ga(s),l=Ce(e.numberFormats)?e.numberFormats:ga(s),u=at($e(),e.modifiers,mx()),c=e.pluralRules||$e(),f=ze(e.missing)?e.missing:null,d=Oe(e.missingWarn)||Jr(e.missingWarn)?e.missingWarn:!0,m=Oe(e.fallbackWarn)||Jr(e.fallbackWarn)?e.fallbackWarn:!0,g=!!e.fallbackFormat,p=!!e.unresolving,w=ze(e.postTranslation)?e.postTranslation:null,h=Ce(e.processor)?e.processor:null,v=Oe(e.warnHtmlMessage)?e.warnHtmlMessage:!0,b=!!e.escapeParameter,y=ze(e.messageCompiler)?e.messageCompiler:sv,E=ze(e.messageResolver)?e.messageResolver:ov||ux,P=ze(e.localeFallbacker)?e.localeFallbacker:iv||tx,k=Re(e.fallbackContext)?e.fallbackContext:void 0,T=e,A=Re(T.__datetimeFormatters)?T.__datetimeFormatters:new Map,H=Re(T.__numberFormatters)?T.__numberFormatters:new Map,V=Re(T.__meta)?T.__meta:{};zf++;const D={version:n,cid:zf,locale:r,fallbackLocale:o,messages:i,modifiers:u,pluralRules:c,missing:f,missingWarn:d,fallbackWarn:m,fallbackFormat:g,unresolving:p,postTranslation:w,processor:h,warnHtmlMessage:v,escapeParameter:b,messageCompiler:y,messageResolver:E,localeFallbacker:P,fallbackContext:k,onWarn:t,__meta:V};return D.datetimeFormats=a,D.numberFormats=l,D.__datetimeFormatters=A,D.__numberFormatters=H,__INTLIFY_PROD_DEVTOOLS__&&J1(D,n,V),D}const ga=e=>({[e]:$e()});function ac(e,t,n,r,s){const{missing:o,onWarn:i}=e;if(o!==null){const a=o(e,n,t,s);return se(a)?a:t}else return t}function ms(e,t,n){const r=e;r.__localeChainCache=new Map,e.localeFallbacker(e,n,t)}function Sx(e,t){return e===t?!1:e.split("-")[0]===t.split("-")[0]}function wx(e,t){const n=t.indexOf(e);if(n===-1)return!1;for(let r=n+1;r<t.length;r++)if(Sx(e,t[r]))return!0;return!1}function Kf(e,...t){const{datetimeFormats:n,unresolving:r,fallbackLocale:s,onWarn:o,localeFallbacker:i}=e,{__datetimeFormatters:a}=e,[l,u,c,f]=sl(...t),d=Oe(c.missingWarn)?c.missingWarn:e.missingWarn;Oe(c.fallbackWarn)?c.fallbackWarn:e.fallbackWarn;const m=!!c.part,g=ic(e,c),p=i(e,s,g);if(!se(l)||l==="")return new Intl.DateTimeFormat(g,f).format(u);let w={},h,v=null;const b="datetime format";for(let P=0;P<p.length&&(h=p[P],w=n[h]||{},v=w[l],!Ce(v));P++)ac(e,l,h,d,b);if(!Ce(v)||!se(h))return r?Fi:l;let y=`${h}__${l}`;Ni(f)||(y=`${y}__${JSON.stringify(f)}`);let E=a.get(y);return E||(E=new Intl.DateTimeFormat(h,at({},v,f)),a.set(y,E)),m?E.formatToParts(u):E.format(u)}const cv=["localeMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName","formatMatcher","hour12","timeZone","dateStyle","timeStyle","calendar","dayPeriod","numberingSystem","hourCycle","fractionalSecondDigits"];function sl(...e){const[t,n,r,s]=e,o=$e();let i=$e(),a;if(se(t)){const l=t.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);if(!l)throw kn(Tn.INVALID_ISO_DATE_ARGUMENT);const u=l[3]?l[3].trim().startsWith("T")?`${l[1].trim()}${l[3].trim()}`:`${l[1].trim()}T${l[3].trim()}`:l[1].trim();a=new Date(u);try{a.toISOString()}catch{throw kn(Tn.INVALID_ISO_DATE_ARGUMENT)}}else if(c1(t)){if(isNaN(t.getTime()))throw kn(Tn.INVALID_DATE_ARGUMENT);a=t}else if(et(t))a=t;else throw kn(Tn.INVALID_ARGUMENT);return se(n)?o.key=n:Ce(n)&&Object.keys(n).forEach(l=>{cv.includes(l)?i[l]=n[l]:o[l]=n[l]}),se(r)?o.locale=r:Ce(r)&&(i=r),Ce(s)&&(i=s),[o.key||"",a,o,i]}function Gf(e,t,n){const r=e;for(const s in n){const o=`${t}__${s}`;r.__datetimeFormatters.has(o)&&r.__datetimeFormatters.delete(o)}}function qf(e,...t){const{numberFormats:n,unresolving:r,fallbackLocale:s,onWarn:o,localeFallbacker:i}=e,{__numberFormatters:a}=e,[l,u,c,f]=ol(...t),d=Oe(c.missingWarn)?c.missingWarn:e.missingWarn;Oe(c.fallbackWarn)?c.fallbackWarn:e.fallbackWarn;const m=!!c.part,g=ic(e,c),p=i(e,s,g);if(!se(l)||l==="")return new Intl.NumberFormat(g,f).format(u);let w={},h,v=null;const b="number format";for(let P=0;P<p.length&&(h=p[P],w=n[h]||{},v=w[l],!Ce(v));P++)ac(e,l,h,d,b);if(!Ce(v)||!se(h))return r?Fi:l;let y=`${h}__${l}`;Ni(f)||(y=`${y}__${JSON.stringify(f)}`);let E=a.get(y);return E||(E=new Intl.NumberFormat(h,at({},v,f)),a.set(y,E)),m?E.formatToParts(u):E.format(u)}const uv=["localeMatcher","style","currency","currencyDisplay","currencySign","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","notation","signDisplay","unit","unitDisplay","roundingMode","roundingPriority","roundingIncrement","trailingZeroDisplay"];function ol(...e){const[t,n,r,s]=e,o=$e();let i=$e();if(!et(t))throw kn(Tn.INVALID_ARGUMENT);const a=t;return se(n)?o.key=n:Ce(n)&&Object.keys(n).forEach(l=>{uv.includes(l)?i[l]=n[l]:o[l]=n[l]}),se(r)?o.locale=r:Ce(r)&&(i=r),Ce(s)&&(i=s),[o.key||"",a,o,i]}function Yf(e,t,n){const r=e;for(const s in n){const o=`${t}__${s}`;r.__numberFormatters.has(o)&&r.__numberFormatters.delete(o)}}const Ex=e=>e,Cx=e=>"",xx="text",Tx=e=>e.length===0?"":rc(e),kx=m1;function Xf(e,t){return e=Math.abs(e),t===2?e?e>1?1:0:1:e?Math.min(e,2):0}function Ax(e){const t=et(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(et(e.named.count)||et(e.named.n))?et(e.named.count)?e.named.count:et(e.named.n)?e.named.n:t:t}function Px(e,t){t.count||(t.count=e),t.n||(t.n=e)}function Ix(e={}){const t=e.locale,n=Ax(e),r=Re(e.pluralRules)&&se(t)&&ze(e.pluralRules[t])?e.pluralRules[t]:Xf,s=Re(e.pluralRules)&&se(t)&&ze(e.pluralRules[t])?Xf:void 0,o=h=>h[r(n,h.length,s)],i=e.list||[],a=h=>i[h],l=e.named||$e();et(e.pluralIndex)&&Px(n,l);const u=h=>l[h];function c(h,v){const b=ze(e.messages)?e.messages(h,!!v):Re(e.messages)?e.messages[h]:!1;return b||(e.parent?e.parent.message(h):Cx)}const f=h=>e.modifiers?e.modifiers[h]:Ex,d=Ce(e.processor)&&ze(e.processor.normalize)?e.processor.normalize:Tx,m=Ce(e.processor)&&ze(e.processor.interpolate)?e.processor.interpolate:kx,g=Ce(e.processor)&&se(e.processor.type)?e.processor.type:xx,w={list:a,named:u,plural:o,linked:(h,...v)=>{const[b,y]=v;let E="text",P="";v.length===1?Re(b)?(P=b.modifier||P,E=b.type||E):se(b)&&(P=b||P):v.length===2&&(se(b)&&(P=b||P),se(y)&&(E=y||E));const k=c(h,!0)(w),T=E==="vnode"&&Je(k)&&P?k[0]:k;return P?f(P)(T,E):T},message:c,type:g,interpolate:m,normalize:d,values:at($e(),i,l)};return w}const Jf=()=>"",Wt=e=>ze(e);function Zf(e,...t){const{fallbackFormat:n,postTranslation:r,unresolving:s,messageCompiler:o,fallbackLocale:i,messages:a}=e,[l,u]=il(...t),c=Oe(u.missingWarn)?u.missingWarn:e.missingWarn,f=Oe(u.fallbackWarn)?u.fallbackWarn:e.fallbackWarn,d=Oe(u.escapeParameter)?u.escapeParameter:e.escapeParameter,m=!!u.resolvedMessage,g=se(u.default)||Oe(u.default)?Oe(u.default)?o?l:()=>l:u.default:n?o?l:()=>l:null,p=n||g!=null&&(se(g)||ze(g)),w=ic(e,u);d&&Ox(u);let[h,v,b]=m?[l,w,a[w]||$e()]:fv(e,l,w,i,f,c),y=h,E=l;if(!m&&!(se(y)||hn(y)||Wt(y))&&p&&(y=g,E=y),!m&&(!(se(y)||hn(y)||Wt(y))||!se(v)))return s?Fi:l;let P=!1;const k=()=>{P=!0},T=Wt(y)?y:dv(e,l,v,y,E,k);if(P)return y;const A=Nx(e,v,b,u),H=Ix(A),V=Rx(e,T,H),D=r?r(V,l):V;if(__INTLIFY_PROD_DEVTOOLS__){const G={timestamp:Date.now(),key:se(l)?l:Wt(y)?y.key:"",locale:v||(Wt(y)?y.locale:""),format:se(y)?y:Wt(y)?y.source:"",message:D};G.meta=at({},e.__meta,yx()||{}),Z1(G)}return D}function Ox(e){Je(e.list)?e.list=e.list.map(t=>se(t)?Nf(t):t):Re(e.named)&&Object.keys(e.named).forEach(t=>{se(e.named[t])&&(e.named[t]=Nf(e.named[t]))})}function fv(e,t,n,r,s,o){const{messages:i,onWarn:a,messageResolver:l,localeFallbacker:u}=e,c=u(e,r,n);let f=$e(),d,m=null;const g="translate";for(let p=0;p<c.length&&(d=c[p],f=i[d]||$e(),(m=l(f,t))===null&&(m=f[t]),!(se(m)||hn(m)||Wt(m)));p++)if(!wx(d,c)){const w=ac(e,t,d,o,g);w!==t&&(m=w)}return[m,d,f]}function dv(e,t,n,r,s,o){const{messageCompiler:i,warnHtmlMessage:a}=e;if(Wt(r)){const u=r;return u.locale=u.locale||n,u.key=u.key||t,u}if(i==null){const u=()=>r;return u.locale=n,u.key=t,u}const l=i(r,Lx(e,n,s,r,a,o));return l.locale=n,l.key=t,l.source=r,l}function Rx(e,t,n){return t(n)}function il(...e){const[t,n,r]=e,s=$e();if(!se(t)&&!et(t)&&!Wt(t)&&!hn(t))throw kn(Tn.INVALID_ARGUMENT);const o=et(t)?String(t):(Wt(t),t);return et(n)?s.plural=n:se(n)?s.default=n:Ce(n)&&!Ni(n)?s.named=n:Je(n)&&(s.list=n),et(r)?s.plural=r:se(r)?s.default=r:Ce(r)&&at(s,r),[o,s]}function Lx(e,t,n,r,s,o){return{locale:t,key:n,warnHtmlMessage:s,onError:i=>{throw o&&o(i),i},onCacheKey:i=>a1(t,n,i)}}function Nx(e,t,n,r){const{modifiers:s,pluralRules:o,messageResolver:i,fallbackLocale:a,fallbackWarn:l,missingWarn:u,fallbackContext:c}=e,d={locale:t,modifiers:s,pluralRules:o,messages:(m,g)=>{let p=i(n,m);if(p==null&&(c||g)){const[,,w]=fv(c||e,m,t,a,l,u);p=i(w,m)}if(se(p)||hn(p)){let w=!1;const v=dv(e,m,t,p,m,()=>{w=!0});return w?Jf:v}else return Wt(p)?p:Jf}};return e.processor&&(d.processor=e.processor),r.list&&(d.list=r.list),r.named&&(d.named=r.named),et(r.plural)&&(d.pluralIndex=r.plural),d}$1();/*!
  * vue-i18n v11.1.9
  * (c) 2025 kazuya kawaguchi
  * Released under the MIT License.
  */const Dx="11.1.9";function Fx(){typeof __VUE_I18N_FULL_INSTALL__!="boolean"&&(gr().__VUE_I18N_FULL_INSTALL__=!0),typeof __VUE_I18N_LEGACY_API__!="boolean"&&(gr().__VUE_I18N_LEGACY_API__=!0),typeof __INTLIFY_DROP_MESSAGE_COMPILER__!="boolean"&&(gr().__INTLIFY_DROP_MESSAGE_COMPILER__=!1),typeof __INTLIFY_PROD_DEVTOOLS__!="boolean"&&(gr().__INTLIFY_PROD_DEVTOOLS__=!1)}const Ot={UNEXPECTED_RETURN_TYPE:ex,INVALID_ARGUMENT:25,MUST_BE_CALL_SETUP_TOP:26,NOT_INSTALLED:27,REQUIRED_VALUE:28,INVALID_VALUE:29,NOT_INSTALLED_WITH_PROVIDE:31,UNEXPECTED_ERROR:32};function Mt(e,...t){return Di(e,null,void 0)}const al=rr("__translateVNode"),ll=rr("__datetimeParts"),cl=rr("__numberParts"),mv=rr("__setPluralRules"),hv=rr("__injectWithOption"),ul=rr("__dispose");function Ws(e){if(!Re(e)||hn(e))return e;for(const t in e)if(Yt(e,t))if(!t.includes("."))Re(e[t])&&Ws(e[t]);else{const n=t.split("."),r=n.length-1;let s=e,o=!1;for(let i=0;i<r;i++){if(n[i]==="__proto__")throw new Error(`unsafe key: ${n[i]}`);if(n[i]in s||(s[n[i]]=$e()),!Re(s[n[i]])){o=!0;break}s=s[n[i]]}if(o||(hn(s)?nv.includes(n[r])||delete e[t]:(s[n[r]]=e[t],delete e[t])),!hn(s)){const i=s[n[r]];Re(i)&&Ws(i)}}return e}function lc(e,t){const{messages:n,__i18n:r,messageResolver:s,flatJson:o}=t,i=Ce(n)?n:Je(r)?$e():{[e]:$e()};if(Je(r)&&r.forEach(a=>{if("locale"in a&&"resource"in a){const{locale:l,resource:u}=a;l?(i[l]=i[l]||$e(),ko(u,i[l])):ko(u,i)}else se(a)&&ko(JSON.parse(a),i)}),s==null&&o)for(const a in i)Yt(i,a)&&Ws(i[a]);return i}function gv(e){return e.type}function vv(e,t,n){let r=Re(t.messages)?t.messages:$e();"__i18nGlobal"in n&&(r=lc(e.locale.value,{messages:r,__i18n:n.__i18nGlobal}));const s=Object.keys(r);s.length&&s.forEach(o=>{e.mergeLocaleMessage(o,r[o])});{if(Re(t.datetimeFormats)){const o=Object.keys(t.datetimeFormats);o.length&&o.forEach(i=>{e.mergeDateTimeFormat(i,t.datetimeFormats[i])})}if(Re(t.numberFormats)){const o=Object.keys(t.numberFormats);o.length&&o.forEach(i=>{e.mergeNumberFormat(i,t.numberFormats[i])})}}}function Qf(e){return R(ts,null,e,0)}const ed="__INTLIFY_META__",td=()=>[],Mx=()=>!1;let nd=0;function rd(e){return(t,n,r,s)=>e(n,r,On()||void 0,s)}const Vx=()=>{const e=On();let t=null;return e&&(t=gv(e)[ed])?{[ed]:t}:null};function cc(e={}){const{__root:t,__injectWithOption:n}=e,r=t===void 0,s=e.flatJson,o=Qo?J:Ae;let i=Oe(e.inheritLocale)?e.inheritLocale:!0;const a=o(t&&i?t.locale.value:se(e.locale)?e.locale:js),l=o(t&&i?t.fallbackLocale.value:se(e.fallbackLocale)||Je(e.fallbackLocale)||Ce(e.fallbackLocale)||e.fallbackLocale===!1?e.fallbackLocale:a.value),u=o(lc(a.value,e)),c=o(Ce(e.datetimeFormats)?e.datetimeFormats:{[a.value]:{}}),f=o(Ce(e.numberFormats)?e.numberFormats:{[a.value]:{}});let d=t?t.missingWarn:Oe(e.missingWarn)||Jr(e.missingWarn)?e.missingWarn:!0,m=t?t.fallbackWarn:Oe(e.fallbackWarn)||Jr(e.fallbackWarn)?e.fallbackWarn:!0,g=t?t.fallbackRoot:Oe(e.fallbackRoot)?e.fallbackRoot:!0,p=!!e.fallbackFormat,w=ze(e.missing)?e.missing:null,h=ze(e.missing)?rd(e.missing):null,v=ze(e.postTranslation)?e.postTranslation:null,b=t?t.warnHtmlMessage:Oe(e.warnHtmlMessage)?e.warnHtmlMessage:!0,y=!!e.escapeParameter;const E=t?t.modifiers:Ce(e.modifiers)?e.modifiers:{};let P=e.pluralRules||t&&t.pluralRules,k;k=(()=>{r&&Wf(null);const I={version:Dx,locale:a.value,fallbackLocale:l.value,messages:u.value,modifiers:E,pluralRules:P,missing:h===null?void 0:h,missingWarn:d,fallbackWarn:m,fallbackFormat:p,unresolving:!0,postTranslation:v===null?void 0:v,warnHtmlMessage:b,escapeParameter:y,messageResolver:e.messageResolver,messageCompiler:e.messageCompiler,__meta:{framework:"vue"}};I.datetimeFormats=c.value,I.numberFormats=f.value,I.__datetimeFormatters=Ce(k)?k.__datetimeFormatters:void 0,I.__numberFormatters=Ce(k)?k.__numberFormatters:void 0;const B=_x(I);return r&&Wf(B),B})(),ms(k,a.value,l.value);function A(){return[a.value,l.value,u.value,c.value,f.value]}const H=U({get:()=>a.value,set:I=>{k.locale=I,a.value=I}}),V=U({get:()=>l.value,set:I=>{k.fallbackLocale=I,l.value=I,ms(k,a.value,I)}}),D=U(()=>u.value),G=U(()=>c.value),ee=U(()=>f.value);function ne(){return ze(v)?v:null}function oe(I){v=I,k.postTranslation=I}function Z(){return w}function re(I){I!==null&&(h=rd(I)),w=I,k.missing=h}const pe=(I,B,ae,fe,we,nt)=>{A();let Ze;try{__INTLIFY_PROD_DEVTOOLS__,r||(k.fallbackContext=t?bx():void 0),Ze=I(k)}finally{__INTLIFY_PROD_DEVTOOLS__,r||(k.fallbackContext=void 0)}if(ae!=="translate exists"&&et(Ze)&&Ze===Fi||ae==="translate exists"&&!Ze){const[Ht,qt]=B();return t&&g?fe(t):we(Ht)}else{if(nt(Ze))return Ze;throw Mt(Ot.UNEXPECTED_RETURN_TYPE)}};function ue(...I){return pe(B=>Reflect.apply(Zf,null,[B,...I]),()=>il(...I),"translate",B=>Reflect.apply(B.t,B,[...I]),B=>B,B=>se(B))}function xe(...I){const[B,ae,fe]=I;if(fe&&!Re(fe))throw Mt(Ot.INVALID_ARGUMENT);return ue(B,ae,at({resolvedMessage:!0},fe||{}))}function _e(...I){return pe(B=>Reflect.apply(Kf,null,[B,...I]),()=>sl(...I),"datetime format",B=>Reflect.apply(B.d,B,[...I]),()=>Hf,B=>se(B)||Je(B))}function Me(...I){return pe(B=>Reflect.apply(qf,null,[B,...I]),()=>ol(...I),"number format",B=>Reflect.apply(B.n,B,[...I]),()=>Hf,B=>se(B)||Je(B))}function Ge(I){return I.map(B=>se(B)||et(B)||Oe(B)?Qf(String(B)):B)}const Ne={normalize:Ge,interpolate:I=>I,type:"vnode"};function F(...I){return pe(B=>{let ae;const fe=B;try{fe.processor=Ne,ae=Reflect.apply(Zf,null,[fe,...I])}finally{fe.processor=null}return ae},()=>il(...I),"translate",B=>B[al](...I),B=>[Qf(B)],B=>Je(B))}function q(...I){return pe(B=>Reflect.apply(qf,null,[B,...I]),()=>ol(...I),"number format",B=>B[cl](...I),td,B=>se(B)||Je(B))}function Y(...I){return pe(B=>Reflect.apply(Kf,null,[B,...I]),()=>sl(...I),"datetime format",B=>B[ll](...I),td,B=>se(B)||Je(B))}function ie(I){P=I,k.pluralRules=P}function Te(I,B){return pe(()=>{if(!I)return!1;const ae=se(B)?B:a.value,fe=L(ae),we=k.messageResolver(fe,I);return hn(we)||Wt(we)||se(we)},()=>[I],"translate exists",ae=>Reflect.apply(ae.te,ae,[I,B]),Mx,ae=>Oe(ae))}function C(I){let B=null;const ae=rv(k,l.value,a.value);for(let fe=0;fe<ae.length;fe++){const we=u.value[ae[fe]]||{},nt=k.messageResolver(we,I);if(nt!=null){B=nt;break}}return B}function x(I){const B=C(I);return B??(t?t.tm(I)||{}:{})}function L(I){return u.value[I]||{}}function j(I,B){if(s){const ae={[I]:B};for(const fe in ae)Yt(ae,fe)&&Ws(ae[fe]);B=ae[I]}u.value[I]=B,k.messages=u.value}function z(I,B){u.value[I]=u.value[I]||{};const ae={[I]:B};if(s)for(const fe in ae)Yt(ae,fe)&&Ws(ae[fe]);B=ae[I],ko(B,u.value[I]),k.messages=u.value}function W(I){return c.value[I]||{}}function _(I,B){c.value[I]=B,k.datetimeFormats=c.value,Gf(k,I,B)}function S(I,B){c.value[I]=at(c.value[I]||{},B),k.datetimeFormats=c.value,Gf(k,I,B)}function O(I){return f.value[I]||{}}function M(I,B){f.value[I]=B,k.numberFormats=f.value,Yf(k,I,B)}function Q(I,B){f.value[I]=at(f.value[I]||{},B),k.numberFormats=f.value,Yf(k,I,B)}nd++,t&&Qo&&(le(t.locale,I=>{i&&(a.value=I,k.locale=I,ms(k,a.value,l.value))}),le(t.fallbackLocale,I=>{i&&(l.value=I,k.fallbackLocale=I,ms(k,a.value,l.value))}));const K={id:nd,locale:H,fallbackLocale:V,get inheritLocale(){return i},set inheritLocale(I){i=I,I&&t&&(a.value=t.locale.value,l.value=t.fallbackLocale.value,ms(k,a.value,l.value))},get availableLocales(){return Object.keys(u.value).sort()},messages:D,get modifiers(){return E},get pluralRules(){return P||{}},get isGlobal(){return r},get missingWarn(){return d},set missingWarn(I){d=I,k.missingWarn=d},get fallbackWarn(){return m},set fallbackWarn(I){m=I,k.fallbackWarn=m},get fallbackRoot(){return g},set fallbackRoot(I){g=I},get fallbackFormat(){return p},set fallbackFormat(I){p=I,k.fallbackFormat=p},get warnHtmlMessage(){return b},set warnHtmlMessage(I){b=I,k.warnHtmlMessage=I},get escapeParameter(){return y},set escapeParameter(I){y=I,k.escapeParameter=I},t:ue,getLocaleMessage:L,setLocaleMessage:j,mergeLocaleMessage:z,getPostTranslationHandler:ne,setPostTranslationHandler:oe,getMissingHandler:Z,setMissingHandler:re,[mv]:ie};return K.datetimeFormats=G,K.numberFormats=ee,K.rt=xe,K.te=Te,K.tm=x,K.d=_e,K.n=Me,K.getDateTimeFormat=W,K.setDateTimeFormat=_,K.mergeDateTimeFormat=S,K.getNumberFormat=O,K.setNumberFormat=M,K.mergeNumberFormat=Q,K[hv]=n,K[al]=F,K[ll]=Y,K[cl]=q,K}function $x(e){const t=se(e.locale)?e.locale:js,n=se(e.fallbackLocale)||Je(e.fallbackLocale)||Ce(e.fallbackLocale)||e.fallbackLocale===!1?e.fallbackLocale:t,r=ze(e.missing)?e.missing:void 0,s=Oe(e.silentTranslationWarn)||Jr(e.silentTranslationWarn)?!e.silentTranslationWarn:!0,o=Oe(e.silentFallbackWarn)||Jr(e.silentFallbackWarn)?!e.silentFallbackWarn:!0,i=Oe(e.fallbackRoot)?e.fallbackRoot:!0,a=!!e.formatFallbackMessages,l=Ce(e.modifiers)?e.modifiers:{},u=e.pluralizationRules,c=ze(e.postTranslation)?e.postTranslation:void 0,f=se(e.warnHtmlInMessage)?e.warnHtmlInMessage!=="off":!0,d=!!e.escapeParameterHtml,m=Oe(e.sync)?e.sync:!0;let g=e.messages;if(Ce(e.sharedMessages)){const E=e.sharedMessages;g=Object.keys(E).reduce((k,T)=>{const A=k[T]||(k[T]={});return at(A,E[T]),k},g||{})}const{__i18n:p,__root:w,__injectWithOption:h}=e,v=e.datetimeFormats,b=e.numberFormats,y=e.flatJson;return{locale:t,fallbackLocale:n,messages:g,flatJson:y,datetimeFormats:v,numberFormats:b,missing:r,missingWarn:s,fallbackWarn:o,fallbackRoot:i,fallbackFormat:a,modifiers:l,pluralRules:u,postTranslation:c,warnHtmlMessage:f,escapeParameter:d,messageResolver:e.messageResolver,inheritLocale:m,__i18n:p,__root:w,__injectWithOption:h}}function fl(e={}){const t=cc($x(e)),{__extender:n}=e,r={id:t.id,get locale(){return t.locale.value},set locale(s){t.locale.value=s},get fallbackLocale(){return t.fallbackLocale.value},set fallbackLocale(s){t.fallbackLocale.value=s},get messages(){return t.messages.value},get datetimeFormats(){return t.datetimeFormats.value},get numberFormats(){return t.numberFormats.value},get availableLocales(){return t.availableLocales},get missing(){return t.getMissingHandler()},set missing(s){t.setMissingHandler(s)},get silentTranslationWarn(){return Oe(t.missingWarn)?!t.missingWarn:t.missingWarn},set silentTranslationWarn(s){t.missingWarn=Oe(s)?!s:s},get silentFallbackWarn(){return Oe(t.fallbackWarn)?!t.fallbackWarn:t.fallbackWarn},set silentFallbackWarn(s){t.fallbackWarn=Oe(s)?!s:s},get modifiers(){return t.modifiers},get formatFallbackMessages(){return t.fallbackFormat},set formatFallbackMessages(s){t.fallbackFormat=s},get postTranslation(){return t.getPostTranslationHandler()},set postTranslation(s){t.setPostTranslationHandler(s)},get sync(){return t.inheritLocale},set sync(s){t.inheritLocale=s},get warnHtmlInMessage(){return t.warnHtmlMessage?"warn":"off"},set warnHtmlInMessage(s){t.warnHtmlMessage=s!=="off"},get escapeParameterHtml(){return t.escapeParameter},set escapeParameterHtml(s){t.escapeParameter=s},get pluralizationRules(){return t.pluralRules||{}},__composer:t,t(...s){return Reflect.apply(t.t,t,[...s])},rt(...s){return Reflect.apply(t.rt,t,[...s])},te(s,o){return t.te(s,o)},tm(s){return t.tm(s)},getLocaleMessage(s){return t.getLocaleMessage(s)},setLocaleMessage(s,o){t.setLocaleMessage(s,o)},mergeLocaleMessage(s,o){t.mergeLocaleMessage(s,o)},d(...s){return Reflect.apply(t.d,t,[...s])},getDateTimeFormat(s){return t.getDateTimeFormat(s)},setDateTimeFormat(s,o){t.setDateTimeFormat(s,o)},mergeDateTimeFormat(s,o){t.mergeDateTimeFormat(s,o)},n(...s){return Reflect.apply(t.n,t,[...s])},getNumberFormat(s){return t.getNumberFormat(s)},setNumberFormat(s,o){t.setNumberFormat(s,o)},mergeNumberFormat(s,o){t.mergeNumberFormat(s,o)}};return r.__extender=n,r}function Bx(e,t,n){return{beforeCreate(){const r=On();if(!r)throw Mt(Ot.UNEXPECTED_ERROR);const s=this.$options;if(s.i18n){const o=s.i18n;if(s.__i18n&&(o.__i18n=s.__i18n),o.__root=t,this===this.$root)this.$i18n=sd(e,o);else{o.__injectWithOption=!0,o.__extender=n.__vueI18nExtend,this.$i18n=fl(o);const i=this.$i18n;i.__extender&&(i.__disposer=i.__extender(this.$i18n))}}else if(s.__i18n)if(this===this.$root)this.$i18n=sd(e,s);else{this.$i18n=fl({__i18n:s.__i18n,__injectWithOption:!0,__extender:n.__vueI18nExtend,__root:t});const o=this.$i18n;o.__extender&&(o.__disposer=o.__extender(this.$i18n))}else this.$i18n=e;s.__i18nGlobal&&vv(t,s,s),this.$t=(...o)=>this.$i18n.t(...o),this.$rt=(...o)=>this.$i18n.rt(...o),this.$te=(o,i)=>this.$i18n.te(o,i),this.$d=(...o)=>this.$i18n.d(...o),this.$n=(...o)=>this.$i18n.n(...o),this.$tm=o=>this.$i18n.tm(o),n.__setInstance(r,this.$i18n)},mounted(){},unmounted(){const r=On();if(!r)throw Mt(Ot.UNEXPECTED_ERROR);const s=this.$i18n;delete this.$t,delete this.$rt,delete this.$te,delete this.$d,delete this.$n,delete this.$tm,s.__disposer&&(s.__disposer(),delete s.__disposer,delete s.__extender),n.__deleteInstance(r),delete this.$i18n}}}function sd(e,t){e.locale=t.locale||e.locale,e.fallbackLocale=t.fallbackLocale||e.fallbackLocale,e.missing=t.missing||e.missing,e.silentTranslationWarn=t.silentTranslationWarn||e.silentFallbackWarn,e.silentFallbackWarn=t.silentFallbackWarn||e.silentFallbackWarn,e.formatFallbackMessages=t.formatFallbackMessages||e.formatFallbackMessages,e.postTranslation=t.postTranslation||e.postTranslation,e.warnHtmlInMessage=t.warnHtmlInMessage||e.warnHtmlInMessage,e.escapeParameterHtml=t.escapeParameterHtml||e.escapeParameterHtml,e.sync=t.sync||e.sync,e.__composer[mv](t.pluralizationRules||e.pluralizationRules);const n=lc(e.locale,{messages:t.messages,__i18n:t.__i18n});return Object.keys(n).forEach(r=>e.mergeLocaleMessage(r,n[r])),t.datetimeFormats&&Object.keys(t.datetimeFormats).forEach(r=>e.mergeDateTimeFormat(r,t.datetimeFormats[r])),t.numberFormats&&Object.keys(t.numberFormats).forEach(r=>e.mergeNumberFormat(r,t.numberFormats[r])),e}const uc={tag:{type:[String,Object]},locale:{type:String},scope:{type:String,validator:e=>e==="parent"||e==="global",default:"parent"},i18n:{type:Object}};function Ux({slots:e},t){return t.length===1&&t[0]==="default"?(e.default?e.default():[]).reduce((r,s)=>[...r,...s.type===ke?s.children:[s]],[]):t.reduce((n,r)=>{const s=e[r];return s&&(n[r]=s()),n},$e())}function pv(){return ke}const Hx=es({name:"i18n-t",props:at({keypath:{type:String,required:!0},plural:{type:[Number,String],validator:e=>et(e)||!isNaN(e)}},uc),setup(e,t){const{slots:n,attrs:r}=t,s=e.i18n||fc({useScope:e.scope,__useComponent:!0});return()=>{const o=Object.keys(n).filter(f=>f[0]!=="_"),i=$e();e.locale&&(i.locale=e.locale),e.plural!==void 0&&(i.plural=se(e.plural)?+e.plural:e.plural);const a=Ux(t,o),l=s[al](e.keypath,a,i),u=at($e(),r),c=se(e.tag)||Re(e.tag)?e.tag:pv();return vn(c,u,l)}}}),od=Hx;function jx(e){return Je(e)&&!se(e[0])}function yv(e,t,n,r){const{slots:s,attrs:o}=t;return()=>{const i={part:!0};let a=$e();e.locale&&(i.locale=e.locale),se(e.format)?i.key=e.format:Re(e.format)&&(se(e.format.key)&&(i.key=e.format.key),a=Object.keys(e.format).reduce((d,m)=>n.includes(m)?at($e(),d,{[m]:e.format[m]}):d,$e()));const l=r(e.value,i,a);let u=[i.key];Je(l)?u=l.map((d,m)=>{const g=s[d.type],p=g?g({[d.type]:d.value,index:m,parts:l}):[d.value];return jx(p)&&(p[0].key=`${d.type}-${m}`),p}):se(l)&&(u=[l]);const c=at($e(),o),f=se(e.tag)||Re(e.tag)?e.tag:pv();return vn(f,c,u)}}const Wx=es({name:"i18n-n",props:at({value:{type:Number,required:!0},format:{type:[String,Object]}},uc),setup(e,t){const n=e.i18n||fc({useScope:e.scope,__useComponent:!0});return yv(e,t,uv,(...r)=>n[cl](...r))}}),id=Wx;function zx(e,t){const n=e;if(e.mode==="composition")return n.__getInstance(t)||e.global;{const r=n.__getInstance(t);return r!=null?r.__composer:e.global.__composer}}function Kx(e){const t=i=>{const{instance:a,value:l}=i;if(!a||!a.$)throw Mt(Ot.UNEXPECTED_ERROR);const u=zx(e,a.$),c=ad(l);return[Reflect.apply(u.t,u,[...ld(c)]),u]};return{created:(i,a)=>{const[l,u]=t(a);Qo&&e.global===u&&(i.__i18nWatcher=le(u.locale,()=>{a.instance&&a.instance.$forceUpdate()})),i.__composer=u,i.textContent=l},unmounted:i=>{Qo&&i.__i18nWatcher&&(i.__i18nWatcher(),i.__i18nWatcher=void 0,delete i.__i18nWatcher),i.__composer&&(i.__composer=void 0,delete i.__composer)},beforeUpdate:(i,{value:a})=>{if(i.__composer){const l=i.__composer,u=ad(a);i.textContent=Reflect.apply(l.t,l,[...ld(u)])}},getSSRProps:i=>{const[a]=t(i);return{textContent:a}}}}function ad(e){if(se(e))return{path:e};if(Ce(e)){if(!("path"in e))throw Mt(Ot.REQUIRED_VALUE,"path");return e}else throw Mt(Ot.INVALID_VALUE)}function ld(e){const{path:t,locale:n,args:r,choice:s,plural:o}=e,i={},a=r||{};return se(n)&&(i.locale=n),et(s)&&(i.plural=s),et(o)&&(i.plural=o),[t,a,i]}function Gx(e,t,...n){const r=Ce(n[0])?n[0]:{};(Oe(r.globalInstall)?r.globalInstall:!0)&&([od.name,"I18nT"].forEach(o=>e.component(o,od)),[id.name,"I18nN"].forEach(o=>e.component(o,id)),[ud.name,"I18nD"].forEach(o=>e.component(o,ud))),e.directive("t",Kx(t))}const qx=rr("global-vue-i18n");function Yx(e={}){const t=__VUE_I18N_LEGACY_API__&&Oe(e.legacy)?e.legacy:__VUE_I18N_LEGACY_API__,n=Oe(e.globalInjection)?e.globalInjection:!0,r=new Map,[s,o]=Xx(e,t),i=rr("");function a(f){return r.get(f)||null}function l(f,d){r.set(f,d)}function u(f){r.delete(f)}const c={get mode(){return __VUE_I18N_LEGACY_API__&&t?"legacy":"composition"},async install(f,...d){if(f.__VUE_I18N_SYMBOL__=i,f.provide(f.__VUE_I18N_SYMBOL__,c),Ce(d[0])){const p=d[0];c.__composerExtend=p.__composerExtend,c.__vueI18nExtend=p.__vueI18nExtend}let m=null;!t&&n&&(m=sT(f,c.global)),__VUE_I18N_FULL_INSTALL__&&Gx(f,c,...d),__VUE_I18N_LEGACY_API__&&t&&f.mixin(Bx(o,o.__composer,c));const g=f.unmount;f.unmount=()=>{m&&m(),c.dispose(),g()}},get global(){return o},dispose(){s.stop()},__instances:r,__getInstance:a,__setInstance:l,__deleteInstance:u};return c}function fc(e={}){const t=On();if(t==null)throw Mt(Ot.MUST_BE_CALL_SETUP_TOP);if(!t.isCE&&t.appContext.app!=null&&!t.appContext.app.__VUE_I18N_SYMBOL__)throw Mt(Ot.NOT_INSTALLED);const n=Jx(t),r=Qx(n),s=gv(t),o=Zx(e,s);if(o==="global")return vv(r,e,s),r;if(o==="parent"){let l=eT(n,t,e.__useComponent);return l==null&&(l=r),l}const i=n;let a=i.__getInstance(t);if(a==null){const l=at({},e);"__i18n"in s&&(l.__i18n=s.__i18n),r&&(l.__root=r),a=cc(l),i.__composerExtend&&(a[ul]=i.__composerExtend(a)),nT(i,t,a),i.__setInstance(t,a)}return a}function Xx(e,t){const n=Wn(),r=__VUE_I18N_LEGACY_API__&&t?n.run(()=>fl(e)):n.run(()=>cc(e));if(r==null)throw Mt(Ot.UNEXPECTED_ERROR);return[n,r]}function Jx(e){const t=Le(e.isCE?qx:e.appContext.app.__VUE_I18N_SYMBOL__);if(!t)throw Mt(e.isCE?Ot.NOT_INSTALLED_WITH_PROVIDE:Ot.UNEXPECTED_ERROR);return t}function Zx(e,t){return Ni(e)?"__i18n"in t?"local":"global":e.useScope?e.useScope:"local"}function Qx(e){return e.mode==="composition"?e.global:e.global.__composer}function eT(e,t,n=!1){let r=null;const s=t.root;let o=tT(t,n);for(;o!=null;){const i=e;if(e.mode==="composition")r=i.__getInstance(o);else if(__VUE_I18N_LEGACY_API__){const a=i.__getInstance(o);a!=null&&(r=a.__composer,n&&r&&!r[hv]&&(r=null))}if(r!=null||s===o)break;o=o.parent}return r}function tT(e,t=!1){return e==null?null:t&&e.vnode.ctx||e.parent}function nT(e,t,n){Gt(()=>{},t),Al(()=>{const r=n;e.__deleteInstance(t);const s=r[ul];s&&(s(),delete r[ul])},t)}const rT=["locale","fallbackLocale","availableLocales"],cd=["t","rt","d","n","tm","te"];function sT(e,t){const n=Object.create(null);return rT.forEach(s=>{const o=Object.getOwnPropertyDescriptor(t,s);if(!o)throw Mt(Ot.UNEXPECTED_ERROR);const i=je(o.value)?{get(){return o.value.value},set(a){o.value.value=a}}:{get(){return o.get&&o.get()}};Object.defineProperty(n,s,i)}),e.config.globalProperties.$i18n=n,cd.forEach(s=>{const o=Object.getOwnPropertyDescriptor(t,s);if(!o||!o.value)throw Mt(Ot.UNEXPECTED_ERROR);Object.defineProperty(e.config.globalProperties,`$${s}`,o)}),()=>{delete e.config.globalProperties.$i18n,cd.forEach(s=>{delete e.config.globalProperties[`$${s}`]})}}const oT=es({name:"i18n-d",props:at({value:{type:[Number,Date],required:!0},format:{type:[String,Object]}},uc),setup(e,t){const n=e.i18n||fc({useScope:e.scope,__useComponent:!0});return yv(e,t,cv,(...r)=>n[ll](...r))}}),ud=oT;Fx();hx(Y1);gx(fx);vx(rv);if(__INTLIFY_PROD_DEVTOOLS__){const e=gr();e.__INTLIFY__=!0,X1(e.__INTLIFY_DEVTOOLS_GLOBAL_HOOK__)}const iT=Nm("config",()=>{const e=J(""),t=J("");function n(r){e.value=r.lang||"en",t.value=r.startpage||"/chat"}return{startpage:t,lang:e,initialize:n}});function aT(e){return{legacy:!1,locale:e,fallbackLocale:"en",messages:{en:{},nl:{},de:{}}}}function bv(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};const{blueprint:t,...n}=e,r=Dt(t,n),{aliases:s={},components:o={},directives:i={}}=r,a=Wn();return a.run(()=>{const l=Fb(r.defaults),u=t0(r.display,r.ssr),c=l_(r.theme),f=__(r.icons),d=Zb(r.locale),m=W0(r.date,d),g=q0(r.goTo,d);function p(h){for(const b in i)h.directive(b,i[b]);for(const b in o)h.component(b,o[b]);for(const b in s)h.component(b,Js({...s[b],name:b,aliasName:s[b].name}));const v=Wn();if(v.run(()=>{c.install(h)}),h.onUnmount(()=>v.stop()),h.provide(Gr,l),h.provide(Ua,u),h.provide(Ns,c),h.provide(Fa,f),h.provide(Wo,d),h.provide(j0,m.options),h.provide(Gu,m.instance),h.provide(K0,g),Ke&&r.ssr)if(h.$nuxt)h.$nuxt.hook("app:suspense:resolve",()=>{u.update()});else{const{mount:b}=h;h.mount=function(){const y=b(...arguments);return bt(()=>u.update()),h.mount=b,y}}h.mixin({computed:{$vuetify(){return st({defaults:Fr.call(this,Gr),display:Fr.call(this,Ua),theme:Fr.call(this,Ns),icons:Fr.call(this,Fa),locale:Fr.call(this,Wo),date:Fr.call(this,Gu)})}}})}function w(){a.stop()}return{install:p,unmount:w,defaults:l,display:u,theme:c,icons:f,locale:d,date:m,goTo:g}})}const lT="3.9.0";bv.version=lT;function Fr(e){const t=this.$,n=t.parent?.provides??t.vnode.appContext?.provides;if(n&&e in n)return n[e]}const cT={defaults:{VAppBar:{flat:!0},VAutocomplete:{variant:"outlined"},VBanner:{color:"primary"},VBottomSheet:{contentClass:"rounded-t-xl overflow-hidden"},VBtn:{color:"primary",rounded:"xl"},VBtnGroup:{rounded:"xl",VBtn:{rounded:null}},VCard:{rounded:"lg"},VCheckbox:{color:"secondary",inset:!0},VChip:{rounded:"sm"},VCombobox:{variant:"outlined"},VDateInput:{variant:"outlined"},VDatePicker:{controlHeight:48,color:"primary",divided:!0,headerColor:"",elevation:3,rounded:"xl",VBtn:{color:"high-emphasis",rounded:"circle"}},VFileInput:{variant:"outlined"},VNavigationDrawer:{},VNumberInput:{variant:"outlined",VBtn:{color:void 0,rounded:void 0}},VSelect:{variant:"outlined"},VSlider:{color:"primary"},VTabs:{color:"primary"},VTextarea:{variant:"outlined"},VTextField:{variant:"outlined"},VToolbar:{VBtn:{color:null}}},icons:{defaultSet:"mdi",sets:{mdi:fh}},theme:{themes:{light:{colors:{primary:"#6750a4",secondary:"#b4b0bb",tertiary:"#7d5260",error:"#b3261e",surface:"#fffbfe"}}}}},uT=te({iconSize:[Number,String],iconSizes:{type:Array,default:()=>[["x-small",10],["small",16],["default",24],["large",28],["x-large",32]]}},"iconSize");function fT(e,t){return{iconSize:U(()=>{const r=new Map(e.iconSizes),s=e.iconSize??t()??"default";return r.has(s)?r.get(s):s})}}const dT=te({active:{type:Boolean,default:void 0},activeColor:String,activeIcon:[String,Function,Object],activeVariant:String,baseVariant:{type:String,default:"tonal"},disabled:Boolean,height:[Number,String],width:[Number,String],hideOverlay:Boolean,icon:[String,Function,Object],iconColor:String,loading:Boolean,opacity:[Number,String],readonly:Boolean,rotate:[Number,String],size:{type:[Number,String],default:"default"},sizes:{type:Array,default:()=>[["x-small",16],["small",24],["default",40],["large",48],["x-large",56]]},text:{type:[String,Number,Boolean],default:void 0},...Xn(),...Ie(),...Qn(),...uT(),...$t(),...gt({tag:"button"}),...ct(),...Or({variant:"flat"})},"VIconBtn"),mT=ge()({name:"VIconBtn",props:dT(),emits:{"update:active":e=>!0},setup(e,t){let{attrs:n,slots:r}=t;const s=Vt(e,"active"),{themeClasses:o}=ht(e),{borderClasses:i}=Jn(e),{elevationClasses:a}=er(e),{roundedClasses:l}=Bt(e),{colorClasses:u,colorStyles:c,variantClasses:f}=rs(()=>({color:(()=>{if(!e.disabled)return s.value?e.activeColor??e.color??"surface-variant":e.color})(),variant:s.value===void 0?e.variant:s.value?e.activeVariant??e.variant:e.baseVariant??e.variant})),d=new Map(e.sizes);function m(){e.disabled||e.readonly||s.value===void 0||e.tag==="a"&&n.href||(s.value=!s.value)}return ye(()=>{const g=s.value?e.activeIcon??e.icon:e.icon,p=e.size,h=d.has(p)?d.get(p):p,v=e.height??h,b=e.width??h,{iconSize:y}=fT(e,()=>new Map(e.iconSizes).get(p)),E={icon:g,size:y.value,iconColor:e.iconColor,opacity:e.opacity};return R(e.tag,{type:e.tag==="button"?"button":void 0,class:de([{"v-icon-btn":!0,"v-icon-btn--active":s.value,"v-icon-btn--disabled":e.disabled,"v-icon-btn--loading":e.loading,"v-icon-btn--readonly":e.readonly,[`v-icon-btn--${e.size}`]:!0},o.value,u.value,i.value,a.value,l.value,f.value,e.class]),style:be([{"--v-icon-btn-rotate":me(e.rotate,"deg"),"--v-icon-btn-height":me(v),"--v-icon-btn-width":me(b)},c.value,e.style]),tabindex:e.disabled||e.readonly?-1:0,onClick:m},{default:()=>[ns(!e.hideOverlay,"v-icon-btn"),N("div",{class:"v-icon-btn__content","data-no-activator":""},[!r.default&&g?R(_t,Pe({key:"content-icon"},E),null):R(Ye,{key:"content-defaults",disabled:!g,defaults:{VIcon:{...E}}},{default:()=>r.default?.()??tt(e.text)})]),!!e.loading&&N("span",{key:"loader",class:"v-icon-btn__loader"},[r.loader?.()??R(gh,{color:typeof e.loading=="boolean"?void 0:e.loading,indeterminate:"disable-shrink",width:"2",size:y.value},null)])]})}),{}}}),hT=bv({components:{VIconBtn:mT},blueprint:cT,theme:{defaultTheme:"light",themes:{light:{colors:{primary:"#01689b"}}}}}),gT=Ky(),ro=jy(Q0);ro.use(hT);ro.use(gT);ro.use(Kg);const dc=iT();dc.initialize(window.gptConfig||{});const vT=Yx(aT(dc.lang));ro.use(vT);ro.mount("#vuespa");Kg.push(dc.startpage);
