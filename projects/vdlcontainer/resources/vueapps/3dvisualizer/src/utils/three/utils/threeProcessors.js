import { InstancedMesh } from 'three'
import { getBaseName } from '@/utils/helpers'
import partContext from '@/utils/part/partContext'

// Puts mesh siblings with the same geometry and materials into groups
function groupMeshesByGeometryAndMaterial(object) {
  const groups = new Map();
  object.traverse((child) => {
    child.name = getBaseName(child.name)
    if (!child.isMesh || !child.parent) return

    const parent = child.parent
    const geometryId = child.geometry.uuid
    const materialId = child.material.uuid

    // Create a key that is only shared between meshes with the same geometry, material and parent
    const key = `${parent.uuid}-${geometryId}-${materialId}`
    if (!groups.has(key)) {
      groups.set(key, {
        parent: parent,
        geometry: child.geometry,
        material: child.material,
        name: child.name,
        meshes: []
      })
    }
    groups.get(key).meshes.push(child)
  })
  return groups
}
// Convert groups of meshes into InstancedMesh (If a group contains more than 1 mesh)
function createInstancedMeshes(groups) {
  const hideDistance = 3 // Distance at which the meshes will be hidden

  groups.forEach((group) => {
    const { parent, geometry, material, name, meshes } = group
    if (meshes.length === 1) return

    // Create a new InstancedMesh
    const instancedMesh = new InstancedMesh(geometry, material, meshes.length)
    instancedMesh.name = name
    // Set each instance's local matrix (not world matrix) from the original mesh
    meshes.forEach((mesh, index) => {
      mesh.updateMatrix()  // Ensure local matrix is up-to-date
      instancedMesh.setMatrixAt(index, mesh.matrix)  // Use local matrix
      parent.remove(mesh) // Remove the original mesh
    })
    // Add the new InstancedMesh to the parent
    parent.add(instancedMesh)
  })
}

function convertToInstancedMeshes(object) {
  // Group meshes by parent and name
  const groups = groupMeshesByGeometryAndMaterial(object)
  createInstancedMeshes(groups)
}

function setObjectTransform(object, config) {
  if (!config) return
  const { position, rotation, scale } = config
  if (!position || !rotation || !scale) return

  // deserialize JSON strings to arrays and set object properties
  object.position.fromArray(JSON.parse(position))
  object.rotation.fromArray(JSON.parse(rotation))
  object.scale.fromArray(JSON.parse(scale))
}

export function preProcessObject(object, drawingData) {
  convertToInstancedMeshes(object)
  setObjectTransform(object, drawingData.drawing)
}