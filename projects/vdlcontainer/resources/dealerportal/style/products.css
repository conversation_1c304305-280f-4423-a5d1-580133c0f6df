.product-row {
  @apply flex mb-2 border-b pt-1 border-gray-300 items-center flex-wrap pb-4;
  @apply lg:flex-nowrap lg:pb-2;
  padding-bottom: 1px;
}

.product-row > * {
  @apply mb-3;
  @apply lg:mb-0;
}

.product-row .image-container {
  @apply w-1/5 pr-2 flex-shrink-0;
  @apply lg:w-32;
}

.product-row .product-image {
  @apply w-20 mx-2;
}

.product-row .product-description-box {
  @apply w-4/5 flex-grow;
  @apply lg:w-2/3;
}

.product-row .product-code {
  @apply text-gray-600;
}

.product-row .product-price {
  @apply w-1/3 text-right font-semibold flex-shrink-0;
  @apply md:w-32 lg:pr-3;
}

.product-row .product-price .bruto-label {
  @apply text-gray-600 text-xs;
}

.product-row .product-price .price_on_request {
  @apply text-sm;
}

.product-row .product-price .not_in_backorder {
  @apply text-sm;
}

.product-row .product-amount {
  @apply w-1/3 pr-3 text-left font-semibold flex-shrink-0;
  @apply md:w-24;
  margin-left: auto;
  @apply lg:w-24 lg:text-right lg:ml-0;
}

.product-row .piece-indication {
  @apply text-gray-600 text-sm font-normal pl-3;
}

.product-row .product-amount .amount-label {
  @apply text-gray-600 text-xs;
}

.product-row .order-box {
  @apply w-full inline-flex flex-shrink-0 px-2 select-none justify-end;
  @apply lg:w-36;
  z-index: 999;
}

.product-row .order-box .amount-input {
  @apply w-12;
}

.product-row .order-box .subtract-amount,
.product-row .order-box .add-amount {
  @apply text-2xl px-1 no-underline;
}

.product-row .order-box .order-product-action {
  @apply w-8 h-8 ml-3 mt-1 flex-shrink-0;
}
.product-row .order-box .order-product-action svg {
  @apply fill-current text-primary;
}
.product-row .order-box .order-product-action.added svg {
  @apply text-green-600;
}

.product-row.summary {
  @apply border-0 lg:pb-0 pb-0;
}
.product-row .summary-label {
  @apply flex-grow text-right;
}

.product-name{
  display: flex;
  margin-top: 8px;
  justify-content: space-between;
}

.product-name h4{
  margin-top: -10px;
}


@media screen and (min-width: 1024px) {
  .w-150p{
    width: 150%;
  }
}

.bruto-netto-info{
  line-height: 1em;
}

/*********************
 * PRODUCT IMAGE POPUP
 *********************/

.image-tooltip {
  @apply relative;
  cursor: zoom-in;
}

.image-tooltip .popup {
  @apply hidden;
  @apply absolute w-64 h-auto border border-gray-700 shadow-md;

  /* vertically center */
  top:          50%;
  transform:    translateY(-50%);

  /* on the right on mobile */
  @apply left-full;
  /* on the left otherwise */
  @apply lg:right-full lg:left-auto;

  right:        100%;
  margin-right: 15px;
}

.image-tooltip:hover .popup {
  @apply block bg-white;
  z-index: 1001;
}


.label-value-box {
  @apply flex;
}
.label-value-box .label {
  @apply mr-4 w-32;
}
.label-value-box .value {
  @apply font-semibold;
}