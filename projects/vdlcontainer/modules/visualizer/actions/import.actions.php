<?php

  use domain\visualizer\entity\VisualProductImportRow;
  use domain\visualizer\entity\VisualTreeImportRow;
  use PhpOffice\PhpSpreadsheet\IOFactory;

  trait ImportActions {
    public function executeImport(): void {
      $visualProductUploader = new Uploader('visual_product_file_upload', reconstructQuery(), DIR_TEMP);
      $visualProductUploader->setAllowed([
        'application/vnd.ms-excel'                                          => 'xls',
        'application/vnd.ms-office'                                         => 'xls',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' => 'xlsx',
      ]);

      $visualTreeUploader = new Uploader('visual_tree_file_upload', reconstructQuery(), DIR_TEMP);
      $visualTreeUploader->setAllowed([
        'application/vnd.ms-excel'                                          => 'xls',
        'application/vnd.ms-office'                                         => 'xls',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' => 'xlsx',
      ]);

      $errors = [];
      if(isset($_POST['import_visual_products'])) {
        $result = $visualProductUploader->parseUpload();
        if($visualProductUploader->hasErrors()) {
          $errors = array_merge($errors, $visualProductUploader->getErrors());
        }
        else if(!$result) {
          $errors[] = 'Selecteer een bestand om te importeren.';
        }
        if(count($errors) == 0) {
          $_SESSION['import_visual_product_file']['excel_file'] = $visualProductUploader->getFilepath();
          ResponseHelper::redirect(reconstructQueryAdd(['action' => 'visualproductimportchanges']));
        }
      }
      if(isset($_POST['import_visual_tree'])) {
        $result = $visualTreeUploader->parseUpload();
        if($visualTreeUploader->hasErrors()) {
          $errors = array_merge($errors, $visualTreeUploader->getErrors());
        }
        else if(!$result) {
          $errors[] = 'Selecteer een bestand om te importeren.';
        }
        if(count($errors) == 0) {
          $_SESSION['import_visual_tree_file']['excel_file'] = $visualTreeUploader->getFilepath();
          ResponseHelper::redirect(reconstructQueryAdd(['action' => 'visualtreeimportchanges']));
        }
      }

      $this->visualTreeUploader          = $visualTreeUploader;
      $this->visualProductUploader         = $visualProductUploader;
      $this->errors                      = $errors;
    }

    public function executeVisualProductImportchanges(): void {
      if (empty($_SESSION['import_visual_product_file']['excel_file']) || !file_exists($_SESSION['import_visual_product_file']['excel_file'])) {
        ResponseHelper::redirectAlertMessage(__('Excel bestand niet gevonden'), reconstructQueryAdd());
      }

      $inputFileType = IOFactory::identify($_SESSION['import_visual_product_file']['excel_file']);
      $reader        = IOFactory::createReader($inputFileType);
      $reader->setReadDataOnly(true);
      $spreadsheet = $reader->load($_SESSION['import_visual_product_file']['excel_file']);

      $excelArray = $spreadsheet->getActiveSheet()->toArray();
      array_shift($excelArray);

      if (!ArrayHelper::hasData($excelArray)) {
        ResponseHelper::redirectAlertMessage(__('Excel bestand bevat geen data of kon niet worden gelezen'), reconstructQueryAdd());
      }

      $rows = count($excelArray);
      $added = 0;
      $updated = 0;
      $errors = [];

      $importRows = array_map(fn($row) => new VisualProductImportRow($row), $excelArray);

      $validRows = array_filter($importRows, function($row) use (&$errors) {
        return $row->validate($errors);
      });

      $productCodes = array_map(fn($row) => $row->getMaterialNr(), $validRows);
      $products =  AppModel::mapObjectIds(Product::getFromCodes($productCodes), 'code');

      $partNumbers = array_map(fn($row) => $row->getPartNr(), $validRows);
      $visualParts = AppModel::mapObjectIds(VisualPart::getFromNumbers($partNumbers), 'part_nr');

      $siteLanguages = array_values(SiteHost::getSitelanguages($_SESSION['site']));

      foreach ($validRows as $row) {
        $visualPart = $visualParts[$row->getPartNr()] ?? new VisualPart();
        $product = $products[$row->getMaterialNr()] ?? null;

        if ($visualPart->id) $updated++;
        else $added++;

        $visualPart->part_nr = $row->getPartNr();
        $visualPart->group_nr = $row->getGroupNr();
        $visualPart->product_id = $product ? $product->id : null;
        $visualPart->has_children = $row->getHasChildren();

        $visualPart->save();
        VisualPartContent::saveFromImport($visualPart, $row->getDescription(), $siteLanguages);
      }

      $visualImportLog = new VisualImportLog();
      $visualImportLog->type = VisualImportLog::TYPE_PURCHASE_CARD;;
      $visualImportLog->rows_processed = $rows;
      $visualImportLog->amount_added = $added;
      $visualImportLog->amount_updated = $updated;
      $visualImportLog->errors = json_encode($errors);
      $visualImportLog->save();

      ResponseHelper::redirectMessage(__('Import gelukt'), PageMap::getUrl('M_VISUALIZER_IMPORT'));
    }

    public function executeVisualTreeImportchanges(): void {
      if (empty($_SESSION['import_visual_tree_file']['excel_file']) || !file_exists($_SESSION['import_visual_tree_file']['excel_file'])) {
        ResponseHelper::redirectAlertMessage(__('Excel bestand niet gevonden'), reconstructQueryAdd());
      }

      $inputFileType = IOFactory::identify($_SESSION['import_visual_tree_file']['excel_file']);
      $reader        = IOFactory::createReader($inputFileType);
      $reader->setReadDataOnly(true);
      $spreadsheet = $reader->load($_SESSION['import_visual_tree_file']['excel_file']);
      $excelArray = $spreadsheet->getActiveSheet()->toArray();
      array_shift($excelArray);
      if (!ArrayHelper::hasData($excelArray)) {
        ResponseHelper::redirectAlertMessage(__('Excel bestand bevat geen data of kon niet worden gelezen'), reconstructQueryAdd());
      }

      $rows = count($excelArray);
      $added = 0;
      $updated = 0;
      $errors = [];

      $importRows = array_map(fn($row) => new VisualTreeImportRow($row), $excelArray);

      $validRows = array_filter($importRows, function($row) use (&$errors) {
        return $row->validate($errors);
      });

      $partNumbers = array_map(fn($row) => $row->getPartNr(), $validRows);
      $visualParts = AppModel::mapObjectIds(VisualPart::getFromNumbers($partNumbers), 'part_nr');

      $siteLanguages = array_values(SiteHost::getSitelanguages($_SESSION['site']));

      foreach ($validRows as $row) {
        $visualPart = $visualParts[$row->getPartNr()] ?? new VisualPart();

        if ($visualPart->id) $updated++;
        else $added++;

        $visualPart->part_nr = $row->getPartNr();
        $visualPart->group_nr = $row->getGroupNr();
        $visualPart->has_children = $row->getHasChildren();
        $visualPart->save();
        VisualPartContent::saveFromImport($visualPart, $row->getDescription(), $siteLanguages);
      }

      $visualImportLog = new VisualImportLog();
      $visualImportLog->type = VisualImportLog::TYPE_TREE_STRUCTURE;
      $visualImportLog->rows_processed = $rows;
      $visualImportLog->amount_added = $added;
      $visualImportLog->amount_updated = $updated;
      $visualImportLog->errors = json_encode($errors);
      $visualImportLog->save();

      ResponseHelper::redirectMessage(__('Import gelukt'), PageMap::getUrl('M_VISUALIZER_IMPORT'));
    }
  }