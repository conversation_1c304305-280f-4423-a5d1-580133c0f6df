
.category-row {
  @apply w-full grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6;
}

.category-row .category-item {
@apply px-2 pb-2 shadow-md border border-gray-100 inline-block;
}

.category-row .category-item:hover {
  @apply border-gray-700;
}

.category-row .category-item p {
  @apply text-center text-tertiary font-semibold mt-4 py-0;
}

.category-row .category-item .image-container {
  @apply flex items-center w-full overflow-hidden relative h-40;
}


/*********************
 * Vertical category navigation
 *********************/

.category-vertical-nav {
  @apply flex;
}
.category-vertical-nav div {
  @apply w-full inline-flex flex-col px-2;
  @apply md:w-1/2 xl:w-1/3 md:px-4;
}

.category-vertical-nav div a {
  @apply block py-2 px-1 border-b border-gray-500;
}
.category-vertical-nav div a:hover {
  @apply bg-primary text-white;
}
.category-vertical-nav div a.active {
  @apply bg-primary text-white;
}


