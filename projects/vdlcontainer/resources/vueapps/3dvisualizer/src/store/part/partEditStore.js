import { defineStore } from 'pinia'
import { nextTick, ref } from "vue"

export const usePartEditStore = defineStore('edit', () => {
  const partToEdit = ref(null)
  function setPartToEdit(part) {
    partToEdit.value = part ?? null
  }

  const lastSubmittedPartNr = ref(null)
  async function partNrWasSubmitted(number) {
    // If the same part was submitted again, reset the value first to trigger any watchers
    if (lastSubmittedPartNr.value === number) {
      lastSubmittedPartNr.value = null
      await nextTick()
    }
    lastSubmittedPartNr.value = number
  }

  return {
    partToEdit,
    setPartToEdit,
    lastSubmittedPartNr,
    partNrWasSubmitted
  }
})
