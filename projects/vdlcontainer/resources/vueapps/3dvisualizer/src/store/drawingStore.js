import { defineStore } from "pinia"
import { ref, reactive, watch } from "vue"
import axios from "axios"
import { useI18n } from "vue-i18n"
import { base64ToArrayBuffer } from '@/utils/helpers'
import threeContext from '@/utils/three/threeContext'
import partContext from '@/utils/part/partContext'
import { serializeThreeObjectToArrayBuffer } from "@/utils/three/utils/threeConvertors"
import { getRootObjectFromChild, returnObjectToUntouchedState } from "@/utils/three/utils/threeHelpers"
import { useConfigStore } from "@/store/configStore"
import { useTaskState } from "@/composables/taskState"
import { useRouter } from "vue-router";

export const useDrawingStore = defineStore('drawing', () => {
  const { t } = useI18n()
  const router = useRouter()
  const { isLoading, runTask, resetState, taskMessage } = useTaskState()
  const configStore = useConfigStore()

  /**
   * @typedef {Object} DrawingData
   * @property {string} url - URL of the drawing file.
   * @property {Object?} drawing - The drawing entity, as saved in the database.
   * @property {Object[]?} parts - List of parts related to the drawing.
   */
  /**
   * @type {import('vue').Ref<DrawingData | null>}
   */
  const drawingData = ref()
  const drawingHasUnsavedChanges = ref(false)
  const drawingInitialized = ref(false)
  const machineOrderNr = ref(null)
  const unlinkedParts = ref([])

  function retrieveUnlinkedParts() {
    if (!configStore.isAdmin) return
    const parts = threeContext.retrieveUnlinkedParts()

    // Check if the number of unlinked parts has changed, if so, mark the drawing as changed
    if (drawingData.value?.drawing?.unlinked_parts != parts.length) {
      drawingHasUnsavedChanges.value = true
    }

    unlinkedParts.value = parts
  }
  watch(() => drawingInitialized.value, () => retrieveUnlinkedParts())

  /**
   * @typedef {Object} DrawingContent
   * @property {string} locale - locale/language of the drawing content.
   * @property {string} name - name of the drawing content.
   * @property {number?} id - id of the drawing content.
   */
  /**
   * @type {import('vue').Ref<DrawingContent[]>}
   */
  const drawingContents = ref()

  // Initialize the drawing contents with a name in all available languages
  function initContentsFromName(name) {
    drawingContents.value = configStore.siteLangs.map(locale =>
      reactive({ locale, name })
    )
  }
  function setContents(contents) {
    drawingContents.value = contents.map(content => reactive(content))
  }

  function clearDrawing() {
    drawingData.value = null
    drawingContents.value = null
    machineOrderNr.value = null
  }

  /*
    * Import the drawing by id
    * @param {String} id - The id of the drawing
    * @returns {Promise<void>}
   */
  async function importDrawingById(id) {
    await runTask(
      async () => {
        const { data } = await axios.get('?action=getDrawingById', {
          params: { id },
        })
        if (!data.success) throw new Error(data.message)

        const { drawing, contents, machine } = data

        // Initialize the drawing url and entity if not already set
        if (!drawingData.value) {
          const bytes = await base64ToArrayBuffer(data.buffer)
          const blob = new Blob([bytes], { type: 'model/gltf-binary' })
          const url = URL.createObjectURL(blob)
          drawingData.value = { url, drawing }
        }

        setContents(contents)
        machineOrderNr.value = machine?.order_nr || null
      },
      t('drawingNotLoaded')
    )
  }

  /*
    * Import parts from a list of part numbers (we use this to check if parts of this model are already in the database)
    * @param {String[]} partNumbers - The part numbers to import
    * @returns {Promise<void>}
   */
  async function importPartsFromNumbers(partNumbers) {
    try {
      const { data } = await axios.post('?action=getParts', {
        partNumbers
      })
      // Initialize the part context with the parts from the drawing
      partContext.setMapFromParts(data || [])
      partContext.emitPartsRefreshed()
      if (!configStore.isAdmin) return
      for (const part of data) {
        partContext.submitPart(part)
      }
    } catch (error) {
      console.error('Error importing parts:', error)
    }
  }

  /*
    * Save the current drawing to the server
    * @param {String?} id - The id of the drawing (if updating an existing drawing)
    * @returns {Promise<void>}
   */
  async function saveDrawing(id = null) {
    if (!configStore.isAdmin) return
    await runTask(
      async() => {
        const rootObject = getRootObjectFromChild(threeContext.currentObject)
        returnObjectToUntouchedState(rootObject)
        threeContext.setCurrentObject(rootObject, false)
        const formData = await createFormDataFromObject(rootObject, id)
        const { data } = await axios.post('?action=saveDrawing', formData, {
          headers: {'Content-Type': 'multipart/form-data'}
        })
        if (!data.success) throw new Error(data.message)

        // Once the drawing is saved, change the URL to edit the drawing
        router.push(`/${data.id}`)

        drawingHasUnsavedChanges.value = false
      },
      t('drawingNotSaved'),
      t('drawingSaved')
    )
  }

  /*
    * Create a FormData object from the object properties
    * @param {Object} object - The object to serialize
    * @param {String?} id - The id of the drawing (if updating an existing drawing)
    * @returns {Promise<FormData>}
   */
  async function createFormDataFromObject(object, id) {
    const formData = new FormData()
    const { position, rotation, scale } = object
    // serialize object properties to JSON strings
    formData.append('position', JSON.stringify(position.toArray()))
    formData.append('rotation', JSON.stringify(rotation.toArray()))
    formData.append('scale', JSON.stringify(scale.toArray()))
    formData.append('contents',  JSON.stringify(drawingContents.value))
    formData.append('parts', JSON.stringify(partContext.getPartsForSaving()))
    formData.append('unlinked_parts', unlinkedParts.value.length)
    if (machineOrderNr.value) formData.append('machine_order_nr', machineOrderNr.value)
    if (id) formData.append('id', id)
    else {
      // only send a drawing buffer when creating a new drawing
      const buffer = await serializeThreeObjectToArrayBuffer(object)
      const drawingBlob = new Blob([buffer], {type: 'application/octet-stream'})
      formData.append('buffer', drawingBlob)
    }
    return formData
  }

  return {
    drawingData,
    drawingContents,
    drawingInitialized,
    machineOrderNr,
    unlinkedParts,
    clearDrawing,
    initContentsFromName,
    retrieveUnlinkedParts,
    drawingHasUnsavedChanges,
    isLoading,
    runTask,
    resetState,
    taskMessage,
    importDrawingById,
    importPartsFromNumbers,
    saveDrawing
  }
})