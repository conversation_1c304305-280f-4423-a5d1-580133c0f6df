<section class="title-bar">
  <h1><?php echo __("Visualizer")?></h1>
  <ul id="tabnav" class="nav nav-tabs">
    <?php echo Navigation::writeNavigationNodes(Navigation::getItem('M_VISUALIZER')->getChildren()); ?>
  </ul>
</section>

<?php /** @var VisualImportLog $log */ ?>

<div class="import-log-details">
  <div class="back-link">
    <a href="<?php echo PageMap::getUrl('M_VISUALIZER_IMPORT', ['action' => 'importlog']); ?>" class="gsd-btn gsd-btn-secondary">
      <i class="fa fa-arrow-left"></i> <?php echo __('Terug naar overzicht'); ?>
    </a>
  </div>

  <div class="log-header">
    <h2><?php echo __('Import Details'); ?> #<?php echo $log->id; ?></h2>
    <div class="import-type-badge <?php echo strtolower(str_replace('_', '-', $log->type)); ?>">
      <?php echo $log->type_label(); ?>
    </div>
  </div>

  <div class="log-summary">
    <div class="summary-grid">
      <div class="summary-item">
        <div class="summary-label"><?php echo __('Geïmporteerd op'); ?></div>
        <div class="summary-value"><?php echo DateTimeHelper::convertToReadable($log->insertTS, '%d %B %Y om %H:%I:%S'); ?></div>
      </div>

      <div class="summary-item">
        <div class="summary-label"><?php echo __('Aantal rijen verwerkt'); ?></div>
        <div class="summary-value"><?php echo number_format($log->rows_processed, 0, ',', '.'); ?></div>
      </div>

      <div class="summary-item success">
        <div class="summary-label"><?php echo __('Rijen toegevoegd'); ?></div>
        <div class="summary-value"><?php echo number_format($log->amount_added, 0, ',', '.'); ?></div>
      </div>

      <div class="summary-item warning">
        <div class="summary-label"><?php echo __('Rijen aangepast'); ?></div>
        <div class="summary-value"><?php echo number_format($log->amount_updated, 0, ',', '.'); ?></div>
      </div>
    </div>
  </div>

  <?php if (!empty($log->errors)): ?>
    <?php
      $errors = json_decode($log->errors, true);
      $hasErrors = !empty($errors);
    ?>

    <?php if ($hasErrors): ?>
      <div class="errors-section">
        <h3><?php echo __('Fouten tijdens import'); ?></h3>

        <?php if (is_array($errors) && isset(array_values($errors)[0]) && is_array(array_values($errors)[0])): ?>
          <!-- New format: row-keyed errors -->
          <div class="errors-by-row">
            <?php foreach ($errors as $rowNumber => $rowErrors): ?>
              <div class="error-row">
                <div class="error-row-header">
                  <strong><?php echo __('Rij'); ?> <?php echo $rowNumber; ?>:</strong>
                </div>
                <ul class="error-list">
                  <?php foreach ($rowErrors as $error): ?>
                    <li><?php echo htmlspecialchars($error); ?></li>
                  <?php endforeach; ?>
                </ul>
              </div>
            <?php endforeach; ?>
          </div>
        <?php else: ?>
          <!-- Old format: simple array of errors -->
          <div class="errors-simple">
            <ul class="error-list">
              <?php foreach ($errors as $error): ?>
                <li><?php echo htmlspecialchars($error); ?></li>
              <?php endforeach; ?>
            </ul>
          </div>
        <?php endif; ?>
      </div>
    <?php endif; ?>
  <?php else: ?>
    <div class="no-errors">
      <div class="success-message">
        <i class="fa fa-check-circle"></i>
        <?php echo __('Import succesvol voltooid zonder fouten'); ?>
      </div>
    </div>
  <?php endif; ?>
</div>

<style>
.import-log-details {
  max-width: 1000px;
  margin: 20px 0;
}

.back-link {
  margin-bottom: 20px;
}

.log-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30px;
  padding-bottom: 15px;
  border-bottom: 2px solid #e0e0e0;
}

.log-header h2 {
  margin: 0;
  color: #333;
}

.import-type-badge {
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: bold;
  text-transform: uppercase;
  font-size: 12px;
  letter-spacing: 0.5px;
}

.import-type-badge.purchase-card {
  background-color: #e3f2fd;
  color: #1976d2;
  border: 1px solid #bbdefb;
}

.import-type-badge.tree-structure {
  background-color: #f3e5f5;
  color: #7b1fa2;
  border: 1px solid #ce93d8;
}

.log-summary {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 25px;
  margin-bottom: 30px;
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.summary-item {
  text-align: center;
  padding: 15px;
  background: white;
  border-radius: 6px;
  border-left: 4px solid #ddd;
}

.summary-item.success {
  border-left-color: #4caf50;
}

.summary-item.warning {
  border-left-color: #ff9800;
}

.summary-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  font-weight: 500;
}

.summary-value {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.errors-section {
  background: #fff5f5;
  border: 1px solid #fed7d7;
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
}

.errors-section h3 {
  color: #c53030;
  margin-top: 0;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.errors-section h3:before {
  content: "⚠";
  margin-right: 8px;
  font-size: 18px;
}

.error-row {
  margin-bottom: 15px;
  padding: 12px;
  background: white;
  border-radius: 4px;
  border-left: 3px solid #e53e3e;
}

.error-row:last-child {
  margin-bottom: 0;
}

.error-row-header {
  margin-bottom: 8px;
  color: #c53030;
}

.error-list {
  margin: 0;
  padding-left: 20px;
}

.error-list li {
  margin-bottom: 4px;
  color: #666;
}

.errors-simple .error-list {
  padding-left: 20px;
}

.no-errors {
  text-align: center;
  padding: 40px 20px;
}

.success-message {
  display: inline-flex;
  align-items: center;
  padding: 15px 25px;
  background: #f0fff4;
  color: #38a169;
  border: 1px solid #9ae6b4;
  border-radius: 8px;
  font-weight: 500;
}

.success-message i {
  margin-right: 10px;
  font-size: 18px;
}

@media (max-width: 768px) {
  .log-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .summary-grid {
    grid-template-columns: 1fr;
  }
}
</style>
