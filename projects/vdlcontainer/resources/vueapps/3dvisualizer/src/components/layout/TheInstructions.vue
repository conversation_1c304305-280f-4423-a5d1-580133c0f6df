<script setup>
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
</script>

<template>
  <v-container fluid class="pa-4">
    <v-card>
      <v-card-title class="text-h5 pb-2">
        {{ t('controlsInstructions') }}
      </v-card-title>

      <v-card-text>
        <v-list density="compact">
          <v-list-item>
            <template #prepend>
              <v-icon color="primary">mdi-mouse</v-icon>
            </template>
            <v-list-item-title class="font-weight-medium">{{ t('leftMouse') }}</v-list-item-title>
            <v-list-item-subtitle>{{ t('navigate') }}</v-list-item-subtitle>
          </v-list-item>

          <v-list-item>
            <template #prepend>
              <v-icon color="primary">mdi-mouse</v-icon>
            </template>
            <v-list-item-title class="font-weight-medium">{{ t('rightMouse') }}</v-list-item-title>
            <v-list-item-subtitle>{{ t('drag') }}</v-list-item-subtitle>
          </v-list-item>

          <v-list-item>
            <template #prepend>
              <v-icon color="primary">mdi-mouse-scroll-wheel</v-icon>
            </template>
            <v-list-item-title class="font-weight-medium">{{ t('scroll') }}</v-list-item-title>
            <v-list-item-subtitle>{{ t('zoom') }}</v-list-item-subtitle>
          </v-list-item>

          <v-list-item>
            <template #prepend>
              <v-icon color="primary">mdi-cursor-default-click</v-icon>
            </template>
            <v-list-item-title class="font-weight-medium">{{ t('click') }}</v-list-item-title>
            <v-list-item-subtitle>{{ t('select') }}</v-list-item-subtitle>
          </v-list-item>

          <v-list-item>
            <template #prepend>
              <v-icon color="primary">mdi-cursor-default-click</v-icon>
            </template>
            <v-list-item-title class="font-weight-medium">{{ t('dbClick') }}</v-list-item-title>
            <v-list-item-subtitle>{{ t('focus') }}</v-list-item-subtitle>
          </v-list-item>

          <v-list-item>
            <template #prepend>
              <v-icon color="primary">mdi-keyboard-esc</v-icon>
            </template>
            <v-list-item-title class="font-weight-medium">{{ t('escape') }}</v-list-item-title>
            <v-list-item-subtitle>{{ t('goBack') }}</v-list-item-subtitle>
          </v-list-item>

          <v-list-item>
            <template #prepend>
              <v-icon color="primary">mdi-keyboard-space</v-icon>
            </template>
            <v-list-item-title class="font-weight-medium">{{ t('space') }}</v-list-item-title>
            <v-list-item-subtitle>{{ t('hide') }}</v-list-item-subtitle>
          </v-list-item>
        </v-list>
      </v-card-text>
    </v-card>
  </v-container>
</template>