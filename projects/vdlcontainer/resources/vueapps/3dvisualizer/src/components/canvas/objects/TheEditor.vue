<script setup>
import { shallowRef, watch, onUnmounted, reactive } from 'vue'
import { usePartThreeStore } from '@/store/part/partThreeStore'
import { useDrawingStore } from '@/store/drawingStore'
import { TransformControls } from '@tresjs/cientos'
import threeContext from "@/utils/three/threeContext"
import { Pane } from "tweakpane"

const emit = defineEmits(['start', 'end'])
const partThreeStore = usePartThreeStore()
const drawingStore = useDrawingStore()

// 3D-object to be transformed in the scene
const objectToEdit = shallowRef(null)
watch(() => partThreeStore.currentPart, (value) => {
  objectToEdit.value = threeContext.getObjectById(value?.id)
})

// Setup pane for selecting the transformation mode (translate, rotate, scale)
const state = reactive({ mode: "" })
const pane = new Pane({
  title: "Editor",
  container: document.querySelector(".editor"),
  expanded: false,
})
pane.addBinding(state, "mode", {
  options: {
    Verplaats: "translate",
    Roteer: "rotate",
    Vergroot: "scale",
    Uit: "",
  },
})
// Emit value event when the transformation starts or ends
watch(() => state.mode, (value) => {
  const event = value ? "start" : "end"
  emit(event)
})
onUnmounted(() => pane.dispose())

function onObjectChanged() {
  drawingStore.drawingHasUnsavedChanges = true
}
</script>

<template>
  <transform-controls
      v-if="state.mode"
      :mode="state.mode"
      :object="objectToEdit"
      :rotation-snap="Math.PI / 8"
      :translation-snap=".5"
      :scale-snap=".5"
      @object-change="onObjectChanged"
  />
  <TresGridHelper v-if="state.mode"/>
</template>