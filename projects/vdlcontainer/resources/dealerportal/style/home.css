.news-item-link {
  @apply shadow-md relative border border-transparent;
  transition: all 0.6s;
}

.news-item-link:hover {
  @apply border border-gray-700;
}

.news-item-link:hover .news-item-teaser {
  opacity:    0;
  transition: all 0.6s;
}

.news-item-link .news-item-text {
  @apply px-4 py-4 absolute top-0 left-0 opacity-0;
  @apply flex flex-col overflow-hidden h-full;
}

.news-item-text h2 {
  @apply mb-1;
}

.news-item-text .text {
  @apply overflow-hidden;
}

.news-item-text .readmore {
  @apply underline py-3;
}

.news-item-link:hover .news-item-text {
  @apply opacity-100;
  transition: all 0.6s;
}

