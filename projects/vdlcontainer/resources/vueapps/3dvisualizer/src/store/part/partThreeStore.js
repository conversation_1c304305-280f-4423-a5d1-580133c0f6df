import { defineStore } from 'pinia'
import { computed, ref, watch } from 'vue'
import { colorObject, setVisibilityOfSiblings, setVisibilityOfChildren, hideAncestorsSiblings } from '@/utils/three/utils/threeHelpers'
import threeContext from '@/utils/three/threeContext'

export const usePartThreeStore = defineStore('part', () => {
  /**
   * A reactive reference representing the current focused part.
   * @type {import('vue').Ref<null | { id: number, name: string }>}
   */
  const currentPart = ref(null)
  watch(currentPart, (part) => {
    const object = threeContext.getObjectById(part?.id)
    threeContext.setCurrentObject(object)
  })

  /**
   * A computed array of sub-parts of the current focused part.
   * @type {import('vue').Ref<{ id: number, name: string }[]>}
   */
  const subParts = computed(() => {
    if (!currentPart.value) return []
    const objects = threeContext.getObjectsByName(currentPart.value?.name)
    const children = objects[0]?.children || []

    const groups = new Map()
    for (const child of children) {
      if (!groups.has(child.name)) {
        groups.set(child.name, { name: child.name, count: 0 })
      }
      const group = groups.get(child.name)
      group.count++
    }
    return Array.from(groups.values())
  })

  function focusOnPartById(id, deepFocus = false) {
    const object = threeContext.getObjectById(id)
    colorObject(object, true)

    // Hide sibling objects to only focus on this object
    if (deepFocus) hideAncestorsSiblings(object)
    else setVisibilityOfSiblings(object, false)

    currentPart.value = { id, name: object.name }
  }

  function focusOnPartByName(name, deepFocus = false) {
    colorObject(threeContext.currentObject.children, true)

    const objects = threeContext.getObjectsByName(name)
    const object = objects[0]

    if (deepFocus) hideAncestorsSiblings(object)
    else setVisibilityOfSiblings(object, false)
    currentPart.value = { id: object.id, name: object.name }
  }

  function goBackToPreviousPart() {
    const object = threeContext.getObjectById(currentPart.value.id)

    // Reset visibility of siblings and children back to original state
    setVisibilityOfSiblings(object, true)
    setVisibilityOfChildren(object, true)

    const parent = object?.parent
    if (!parent || parent.type === "Scene") return false

    const { id, name } = parent
    currentPart.value = { id, name }
    return true
  }

  return {
    currentPart,
    subParts,
    focusOnPartById,
    focusOnPartByName,
    goBackToPreviousPart,
  }
})