<script setup>
import { ref } from 'vue'
import { useDrawingStore } from "@/store/drawingStore"
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'

const { t } = useI18n()
const drawingStore = useDrawingStore()
const fileName = ref()
const router = useRouter()

function handleFileChange(event) {
  const file = event.target.files[0]
  if (!validateFile(file)) return

  router.push('/new')
  fileName.value = file.name

  // Removes the extension from the filename and sets it as the initial name
  const initialName = file.name.replace(/\.[^/.]+$/, "")
  drawingStore.initContentsFromName(initialName)

  // Create a URL for the imported file and set it as the drawing data, so it can be loaded in the scene
  const url = URL.createObjectURL(file)
  drawingStore.drawingData = { url }
  drawingStore.drawingHasUnsavedChanges = true
}

// Check if the file is a valid .gltf or .glb file
function validateFile(file) {
  if (!file) return false
  if (!file.name.endsWith('.glb') && !file.name.endsWith('.gltf')) {
    alert('Alleen .gltf of glb bestanden zijn geldig!')
    return false
  }
  return true
}

// Clear the file input and the drawing
const fileInput = ref()
function clearFile() {
  fileInput.value.value = ""
  drawingStore.clearDrawing()
}
</script>

<template>
    <v-card class="pa-4 bg-white">
      <v-card-text>
        <div class="d-flex align-center flex-wrap">
          <input
              ref="fileInput"
              id="model-upload"
              type="file"
              @change="handleFileChange"
              accept=".glb,.gltf"
              hidden
          >

          <v-btn
              color="primary"
              size="large"
              prepend-icon="mdi-upload"
              @click="$refs.fileInput.click()"
          >
            {{ t('importGlbFile') }}
          </v-btn>

          <div class="ml-4">
            <v-chip
                v-if="!drawingStore.drawingContents"
                color="grey-lighten-2"
                text-color="grey-darken-2"
                variant="flat"
            >
              <v-icon start>mdi-file-outline</v-icon>
              {{ t('noFileChoosen') }}
            </v-chip>

            <v-chip
                v-else
                color="success"
                variant="flat"
                closable
                @click:close="clearFile"
            >
              <v-icon start>mdi-file-check</v-icon>
              {{ fileName }}
            </v-chip>
          </div>
        </div>
      </v-card-text>
    </v-card>
</template>