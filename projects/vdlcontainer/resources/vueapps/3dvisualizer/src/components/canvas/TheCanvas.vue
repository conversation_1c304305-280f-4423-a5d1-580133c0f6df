<script setup>
import { TresCanvas  } from '@tresjs/core'
import TheScene from "@/components/canvas/TheScene.vue"

/**
 * Disclaimer: The components inside the canvas are **not** traditional DOM elements.
 * They use Tres JS (similar to React Three Fiber) and Three JS to declaratively add
 * objects to a 3D scene, creating a fully interactive 3D environment.
 *
 * For more information on the 3D libraries, see:
 * - https://threejs.org/
 * - https://docs.tresjs.org/
 * - https://cientos.tresjs.org/
 */
</script>

<template>
    <tres-canvas>
      <the-scene/>
    </tres-canvas>
</template>