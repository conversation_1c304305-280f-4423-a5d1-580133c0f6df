<?php
  /** @var Uploader $visualProductUploader */
  /** @var Uploader $visualTreeUploader */
  /** @var string[] $errors */
?>


<section class="title-bar">
  <h1><?php echo __("Visualizer")?></h1>
  <ul id="tabnav" class="nav nav-tabs">
    <?php echo Navigation::writeNavigationNodes(Navigation::getItem('M_VISUALIZER')->getChildren()); ?>
  </ul>
</section>

<?php writeErrors($errors) ?>

<form method="post" class="edit-form with-padding" enctype="multipart/form-data">
  <h3>Excel bestand met inkoopkaart</h3>
  <p>Bestand moet de volgende structuur bevatten:</p>
  <br/>
  <table class="csv-example-table">
    <tr>
      <td>Kolom code</td>
      <td>A</td>
      <td>B</td>
      <td>C</td>
      <td>D</td>
      <td>E</td>
      <td>F</td>
    </tr>
    <tr>
      <td>Kolom naam</td>
      <td>Sub</td>
      <td>Materiaal nr</td>
      <td class="not-used">Materiaal rev</td>
      <td class="not-used">Materiaal oms</td>
      <td class="not-used">Verbruik</td>
      <td>Tekening nummer</td>
    </tr>
    <tr>
      <td>Kolom omschrijving</td>
      <td>Groep: 2xx, 3xx, 4xx, 5xx</td>
      <td>Artikel nummer</td>
      <td></td>
      <td></td>
      <td></td>
      <td>Tekening nummer</td>
    </tr>
  </table>
  <br>
  <?php echo $visualProductUploader->getInputs(); ?><br />
  <br />
  <input type="submit" name="import_visual_products" class="gsd-btn gsd-btn-primary" value="Importeer inkoopkaart" />
  <br />
</form>
<br>
<form method="post" class="edit-form with-padding" enctype="multipart/form-data">
  <h3>Excel bestand met boomstructuur</h3>
  <p>Bestand moet de volgende structuur bevatten:</p>
  <br/>
  <table class="csv-example-table">
    <tr>
      <td>Kolom code</td>
      <td>A</td>
      <td>B</td>
      <td>C</td>
      <td>D</td>
      <td>E</td>
    </tr>
    <tr>
      <td>Kolom naam</td>
      <td class="not-used">Order nr</td>
      <td class="not-used">Node</td>
      <td class="not-used">Parent node</td>
      <td>Sub</td>
      <td>Mat. / ond.</td>
    </tr>
    <tr>
      <td>Kolom omschrijving</td>
      <td></td>
      <td></td>
      <td></td>
      <td>Groep: 2xx, 3xx, 4xx, 5xx</td>
      <td>Materiaal/onderdeel beschrijving, <br> deze bevat het tekening nummer</td>
    </tr>
  </table>
  <br>
  <?php echo $visualTreeUploader->getInputs(); ?><br />
  <br />
  <input type="submit" name="import_visual_tree" class="gsd-btn gsd-btn-primary" value="Importeer boomstructuur" />
  <br />
</form>


<style>
  table.csv-example-table,
  .csv-example-table th,
  .csv-example-table {
    border-collapse: collapse;
  }

  table.csv-example-table,
  .csv-example-table th,
  .csv-example-table td {
    border: 1px solid #002c51;
    padding: 5px;
  }

  input {
    padding: unset;
    margin: 16px 0 0 8px;
  }

  .not-used {
    text-decoration: line-through;
  }
</style>