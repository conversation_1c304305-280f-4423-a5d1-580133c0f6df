<script setup>
import { useRouterStore } from "@/store/routerStore"
import { useConfigStore } from "@/store/configStore"
import { useInteractionStore } from '@/store/interactionStore'
import { computed } from 'vue'
import { usePart } from "@/composables/part"

const props = defineProps(['part'])

const routerStore = useRouterStore()
const configStore = useConfigStore()
const interactionStore = useInteractionStore()

const { displayName, isOrderable, openPartEditDialog, openPartOrderDialog } = usePart(props.part.name)

function onClick() {
  // if the part was already clicked, focus on it by pushing it to the route
  const partWasAlreadyClicked = props.part.name === interactionStore.clickedPartName
  if (partWasAlreadyClicked) routerStore.pushPartToRouteFromNr(props.part.name)

  else interactionStore.clickedPartName = props.part.name
}

const visible = computed(() => !interactionStore.invisibleParts.has(props.part.name))
</script>

<template>
  <v-list-item @click.stop="onClick" class="d-flex align-center justify-space-between py-1 px-4 custom-list-item">
    <div class="flex-grow-1 mr-2">
      {{ props.part.count > 1 ? `${props.part.count}x ` : '' }} {{ displayName }}</div>
    <template v-slot:append>
      <div class="d-flex align-center ga-2 px-2 py-1">
        <v-icon
            v-if="isOrderable"
            size="small"
            @click.stop="openPartOrderDialog"
            class="cursor-pointer"
        >
          mdi-basket
        </v-icon>
        <v-icon
            v-if="configStore.isAdmin"
            size="small"
            @click.stop="openPartEditDialog"
            class="cursor-pointer"
        >
          mdi-pencil
        </v-icon>
        <v-icon
            size="small"
            @click.stop="interactionStore.togglePartVisible(props.part.name)"
            class="cursor-pointer"
        >
          {{ visible ? 'mdi-eye' : 'mdi-eye-off' }}
        </v-icon>
      </div>
    </template>
  </v-list-item>
</template>

<style scoped>
.custom-list-item:hover {
  background-color: #e0e0e0;
}
.custom-list-item {
  border-bottom: 1px solid #e0e0e0;
}
</style>