#dashboard {
  padding: 2em 0;
  min-width: 1220px;
}

#dashboard .ribbon-navigation {
  display: flex;
  max-width: 100%;
}

#dashboard .ribbon-navigation .ribbon {
  display: flex;
  color: #fff;
  width: 100%;
  border-bottom: 1px solid #002c51;
}

#dashboard .ribbon-navigation .ribbon ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
  display: flex;
  justify-content: space-around;
}

#dashboard .ribbon-navigation .ribbon li {
  cursor: pointer;
  color: #002c51;
  transition: background-color 0.3s;
  padding: 1em;
  border-top: 1px solid #002c51;
  border-left: 1px solid #002c51;
  border-right: 1px solid #002c51;
  margin: 0 5px;
  font-size: 0.9em;
}

#dashboard .ribbon-navigation .ribbon .active {
  background-color: #eaf5ff;
  color: #002c51;
  box-shadow: 0px 1px #eaf5ff;
}

#dashboard .ribbon-navigation .ribbon .active_status {
  color: rgba(255, 102, 0);
}

.side-bar {
  width: 20%;
  padding: 0 2.5% 0 1%;
}

.side-bar-box {
  width: 20%;
  min-width: 250px;
  margin-top: 1.5em;
  top: 0px;
  right: 0px;
  bottom: 0px;
  left: 0px;
  width: 16rem;
  overflow: hidden;
  border-right-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgba(222, 226, 230, var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgba(255, 255, 255, var(--tw-bg-opacity));
  padding: 0.5rem;
  font-size: 1.2em;
  line-height: 1.5em;
  --tw-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  flex-direction: column;
  align-items: flex-start;
  z-index: 1000;
}

.icon {
  margin-right: 0.5rem;
  width: 1.5rem;
}

.status {
  font-size: 0.8em;
}

.badge {
  border-radius: 20px;
  background-color: rgba(255, 102, 0, var(--tw-bg-opacity));
  width: 22px;
  height: 22px;
  text-align: center;
  font-size: 0.7em;
  --tw-text-opacity: 1;
  font-weight: normal;
  color: rgba(255, 255, 255, var(--tw-text-opacity));
  line-height: 1.9em;
}

.dashboard {
  display: flex;
}

.dashboard-status {
  display: flex;
  align-items: center;
  gap: 5px;
  line-height: 2em;
  padding: 0.5em 0.3em;
  cursor: pointer;
}

.dashboard-status:hover {
  color: #7f95a8;
}

.active_status{
  color: #FF6600;
}

.dashboard-content {
  width: 75%;
  margin: auto;
  margin-top: 2em;
  max-height: 50vH;
  overflow-x: hidden;
  overflow-y: scroll;
}

.dashboard-content table {
  width: 100%;
}

.order {
  text-align: left;
  font-size: 1.2em;
  box-shadow: 0px 1px #EEE;
}

.order-td:hover {
  background-color: #eaf5ff;
}

thead {
  position: sticky;
  top: 0;
  background-color: #FFF;
}

.dashboard-content th {
  color: rgb(0, 44, 81);
  font-weight: normal;
}

.dashboard-content tr.order td {
  /*padding: 0.5em 0.5em 0.2em 0em;*/
  padding: 0.2em 0;
  vertical-align: middle;
  font-size: 0.8em;
}

.dashboard-content tr.order a {
  text-decoration: none;
}

.dashboard-content tr.order td:first-child {
  width: 20px;
}

.dashboard-content tr.order td:nth-child(2) {
  font-weight: 600;
}

.dashboard-content tr.order td:nth-child(6) {
  width: 150px;
}

.dashboard-content th:nth-child(7), tr.order td:nth-child(7) {
  padding-left: 15px;
}

.dashboard-content .product img {
  height: 1rem;
}

.has-no-order {
  border-color: #CCC !important;
  color: #CCC !important;
}