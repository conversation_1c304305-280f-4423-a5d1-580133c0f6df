<script setup>
/*
 * Disclaimer: TrackballControls will probably get added to @tresjs/cientos in the near future,
 * this component can then be removed and replaced with that
 */
import { useLoop, useTresContext } from '@tresjs/core'
import { useEventListener } from '@vueuse/core'
import { TrackballControls } from 'three-stdlib'
import { onUnmounted, ref, toRefs, watch } from 'vue'
import { extendControls } from '@/utils/three/utils/threeHelpers'

const props = defineProps({
  makeDefault: {
    type: Boolean,
    default: false
  }
})
const emit = defineEmits(['change', 'start', 'end'])

const { makeDefault } = toRefs(props)
const { renderer, camera, controls, extend, invalidate } = useTresContext()

// extending tres JS catalogue with trackballcontrols
extend({ TrackballControls })

// Reference to the controls instance from the tres JS catalogue (see TresTrackballControls in template)
const controlsRef = ref()
watch(controlsRef, (value) => {
  addEventListeners()
  extendControls(value) // extending controls with smooth transitions (similar to camera controls)
  controls.value = (value && makeDefault.value) ? value : null
})

// Adding event listeners to the controls instance to emit vue events when the controls are being used
function addEventListeners() {
  useEventListener(controlsRef.value, 'change', () => {
    emit('change', controlsRef.value)
    invalidate()
  })
  useEventListener(controlsRef.value, 'start', () => emit('start', controlsRef.value))
  useEventListener(controlsRef.value, 'end', () => emit('end', controlsRef.value))
}

// Updating the controls instance before rendering the scene
const { onBeforeRender } = useLoop()
onBeforeRender(() => controlsRef.value?.update())

// Exposing the controls instance so other components can access it through "useTresContext"
defineExpose({ instance: controlsRef })

// Polling for the dom element to be ready before creating the controls instance
const isReady = ref(false)
const pollingForDomElement = setInterval(() => {
  const domElement = renderer.value?.domElement
  if (domElement && domElement.offsetWidth > 0 && domElement.offsetHeight > 0) {
    isReady.value = true
    clearInterval(pollingForDomElement)
  }
}, 10)

// Disposing the controls instance when the component is unmounted
onUnmounted(() => {
  if (pollingForDomElement) clearInterval(pollingForDomElement)
  controlsRef.value?.dispose()
})

// Options/arguments for the controls instance, changed slightly for touch devices
const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0 || navigator.msMaxTouchPoints > 0
const options = {
  rotateSpeed: isTouchDevice ? 1 : 8,
  zoomSpeed: isTouchDevice ? 1.2 : 2,
  staticMoving: true,
  cursorZoom: !isTouchDevice,
  maxDistance: 100,
}
</script>

<template>
  <TresTrackballControls
      ref="controlsRef"
      v-if="isReady"
      :args="[camera, renderer.domElement]"
      v-bind="options"
  />
</template>