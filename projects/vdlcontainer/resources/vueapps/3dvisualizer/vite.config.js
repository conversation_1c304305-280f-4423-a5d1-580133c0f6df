import {fileURLToPath, URL} from 'node:url'
import { templateCompilerOptions } from '@tresjs/core'
// import {resolve} from 'path'
import {defineConfig} from 'vite'
import vueDevTools from 'vite-plugin-vue-devtools'
import vue from '@vitejs/plugin-vue'
import vuetify from 'vite-plugin-vuetify'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue({
      // tres js compileroptions
      ...templateCompilerOptions
    }),
    vuetify({ autoImport: true }),
    /*, vueDevTools()*/
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
      '@gsdvuefw': fileURLToPath(new URL('./../../../../../gsdfw/resources/vueapps/gsdvuefw', import.meta.url)),
      '@vueapps': fileURLToPath(new URL('./node_modules/', import.meta.url)),
    }
  },
  define: {
    public_dir: JSON.stringify('/projects/vdlcontainer/templates/3dvisualizer'),
    // frontend_dir: JSON.stringify('/projects/vdlcontainer/templates/3dvisualizer')
  },
  server: {
    port: 5174,
    fs: {
      // Allow serving files from more levels up to the project root
      allow: ['./../../../../../'],
    },
  },
  // required for correct path of images
  //base: '/projects/vdlcontainer/templates/3dvisualizer',
  base: '',
  build: {
    // output folder for build files
    outDir: __dirname + '/../../../../../projects/vdlcontainer/templates/3dvisualizer',
    // assetsDir is relative to outDir
    assetsDir: '/assets',
    // minify based on environment
    minify: ((process.env.NODE_ENV === 'development') ? false : true),
    // minify: false,
    rollupOptions: {
      output: {
        // create .min files only in production mode
        assetFileNames: (assetInfo) => {
          // only create .min files for index.css, not for other assets
          if(assetInfo.name !== 'index.css') return 'assets/[name].[ext]';
          return (process.env.NODE_ENV === 'development') ? 'assets/[name].[ext]' : 'assets/[name].min.[ext]';
        },
        chunkFileNames: () => {
          return (process.env.NODE_ENV === 'development') ? 'assets/[name].js' : 'assets/[name].min.js';
        },
        entryFileNames: () => {
          return (process.env.NODE_ENV === 'development') ? 'assets/[name].js' : 'assets/[name].min.js';
        },
      },
    },
  },
})
