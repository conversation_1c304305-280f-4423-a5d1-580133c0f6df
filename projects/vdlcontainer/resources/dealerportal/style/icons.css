.gsd-svg-icon svg {
  color: #A1A5B7;
  height: 1.15rem;
  width: 1.15rem;
  display: inline-block;
}

.icon-color-orange .gsd-svg-icon svg {
  color: #ff7811;
}

.icon-color-green .gsd-svg-icon svg {
  color: #A8BA2A;
}

.icon-color-off .gsd-svg-icon svg {
  color: #bbbbbb;
}

.icon-size-1 .gsd-svg-icon svg {
  height: 1rem;
  width: 1rem;
}
.icon-size-2 .gsd-svg-icon svg {
  height: 0.9rem;
  width: 0.9rem;
}

.gsd-svg-icon-a .gsd-svg-icon svg {
  display: block;
}

.gsd-svg-icon.gsd-login svg {
  height: 1.5rem;
  width: 1.5rem;
}

.gsd-svg-icon.gsd-mail svg {
  height: 1.4rem;
  width: 1.4rem;
}

.gsd-svg-icon.gsd-phone svg {
  height: 1.05rem;
  width: 1.05rem;
}

.gsd-svg-icon-a {
  font-weight: 500;
  line-height: 1.5;
  color: #181C32;
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  user-select: none;
  border: 1px solid transparent;
  font-size: 1.1rem;
  border-radius: 0.475rem;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  background-color: #F5F8FD;
  height: calc(1.5em + 0.2rem + 2px);
  width: calc(1.5em + 0.2rem + 2px);
}

.gsd-svg-icon-a:hover {
  background-color: #EFF4F9;
}
.gsd-svg-icon-a:hover .gsd-svg-icon svg {
  color: var(--gsd-primary-color);
}
.icon-color-off.gsd-svg-icon-a:hover .gsd-svg-icon svg {
  color: #bbbbbb;
}

.gsd-svg-icon-width-2 {
  width: 77px
}
.gsd-svg-icon-width-3 {
  width: 112px
}

