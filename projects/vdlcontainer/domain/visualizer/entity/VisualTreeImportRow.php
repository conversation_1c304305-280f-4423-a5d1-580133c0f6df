<?php

  namespace domain\visualizer\entity;

  class VisualTreeImportRow extends VisualImportRow {
    public string $full_description;
    public function __construct(array $excel_row) {
      $this->group_nr = trim($excel_row[3] ?? '');
      $this->full_description = trim($excel_row[4] ?? '');

      preg_match(
        '/\d{3}\s*-\s*Onderdeel:\s*(?<part_nr>\d{6})\/\d{3}\s*Wijz:\s*\d{2}\s*-\s*(?<description>.+)$/',
        $this->full_description,
        $matches
      );
      $this->part_nr = $matches['part_nr'] ?? '';
      $this->description = $matches['description'] ?? '';
    }

    public function validate(&$errors): bool {
      if (empty($this->group_nr)) {
        $errors[] = "Groepnummer is verplicht";
      }
      if (empty($this->full_description)) {
        $errors[] = "Beschrijving is verplicht";
      } else {
        if (empty($this->part_nr)) {
          $errors[] = "Onderdeelnummer kon niet afgeleid worden uit beschrijving";
        }
        if (empty($this->description)) {
          $errors[] = "Onderdeelnaam kon niet afgeleid worden uit beschrijving";
        }
      }
      return count($errors) === 0;
    }
  }