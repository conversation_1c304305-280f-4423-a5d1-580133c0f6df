import { ref } from "vue"
import axios from "axios"
import { debounce } from "@/utils/helpers"

/**
 * A composable function to manage product filtering and retrieval.
 *
 * @returns {Object} - The reactive state and methods for managing products.
 * @property {Ref<Array>} products - A reactive array of the most recent fetched products.
 * @property {Function} fetchProducts - Fetches products from the server based on the filter.
 * @property {Function} updateFilter - Updates the filter string and fetches products using debouncing.
 */
export function useProduct() {
  const products = ref([])
  async function fetchProducts(query = '') {
    try {
      const { data } = await axios.get('?action=getProducts', {
        params: { filter: query, limit: 10 },
      })
      products.value = data
    } catch (err) {
      console.error("Failed to fetch products:", err)
      products.value = []
    }
  }
  // Debounce to avoid spamming the server with requests
  const debouncedFetchProducts = debounce(fetchProducts, 300)

  function updateFilter(newFilter) {
    debouncedFetchProducts(newFilter) // Trigger API call with new filter
  }

  return {
    products,
    fetchProducts,
    updateFilter
  }
}