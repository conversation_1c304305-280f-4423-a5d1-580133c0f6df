/** ONLY base styles like body/a/h1 **/

html, body, .content {
  font-size: 15px;
}

body {
  font-family: 'Lato', sans-serif;
}

h1 {
  @apply text-2xl text-primary mb-1;
}

h2 {
  @apply text-xl text-primary pb-1 mb-5;
}

h3 {
  @apply font-medium mb-2 text-lg text-gray-700;
}

a {
  @apply cursor-pointer no-underline;
}


[v-cloak] {
  display: none !important;
}

/** this wil make sure a web component and it's slots are not rendered until it is defined, prevents flashing of slot content  **/
/** see: https://stackoverflow.com/questions/60029748/how-do-i-prevent-a-slotted-html-element-from-rendering-until-is-it-inside-the-sh **/
*:not(:defined) {
  /*display: none*/
}