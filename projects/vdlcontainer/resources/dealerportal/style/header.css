header {
  @apply w-full md:px-2 flex justify-around items-center bg-white;
}

.logo-vdl {
  @apply hidden;
  @apply flex-grow lg:inline-flex justify-center flex-1 ;
}

.logo-dealer {
  @apply hidden;
  @apply flex-grow lg:inline-flex justify-center flex-1;
}

/*********************
 * Language dropdown
 *********************/

.language-menu {
  position:   relative;
  width:      46px;
  height:     19px;
  overflow:   hidden;
  transition: all 0.2s;
}

.language-menu:before {
  font-family: 'Material Icons';
  content:     "\e5cf";
  position:    absolute;
}


.language-menu:hover {
  overflow:  visible;
  transform: scale(1.2);
}

.language-menu ul {
  position:     absolute;
  height:       75px;
  padding-left: 20px;
  z-index:      1000;
}

.language-menu ul li {
  display:       block;
  margin-bottom: 6px;
  width:         26px;
  height:        19px;
}

.language-menu ul li a {
  width:  26px;
  height: 19px;
}

#message_development {
  background-color: #f44336;
  text-align: center;
  color: white;
  font-weight: bold;
  padding: 5px;
}
