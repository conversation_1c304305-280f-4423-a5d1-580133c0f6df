@CHARSET "UTF-8";

.orderStatus{
  display: flex;
  margin-bottom: 1em;
}
.orderStatus div{
  padding: 10px 5px;
}
.orderStatus div:first-child {
  width: 66px;
  line-height: 2.5em;
}

.orderStatus div:nth-child(2){
  width: 23%;
}

.orderStatus textarea {
  width: 100%;
  height: 2.5em;
}

.selectOrderStatus{
  padding: 0 5px;
}

.select2.select2-container{
  vertical-align: bottom;
  margin-right: 10px;
}

.select2-container--default .select2-selection--multiple {
  border: 1px solid var(--gray-200);
  height: 100%;
  border-radius: 6px;
  vertical-align: bottom !important;
}

.input, select, textarea, .content-block .dataTables_wrapper .dataTables_length select{
  padding: 8px;
}
.productselect, .transportSelect.select2 {
  display: block;
  width: 350px;
}
.transportSelect.select2{
  /*width: 210px*/
}
.product_no input{
  min-width: 100px;
}

.list .select2 .selection {
  width: inherit;
}

.select2-selection--multiple::-webkit-scrollbar {
  width: 10px;
}

/* Track */
.select2-selection--multiple::-webkit-scrollbar-track {
  background: #f1f1f1;
}

/* Handle */
.select2-selection--multiple::-webkit-scrollbar-thumb {
  background: var(--gsd-primary-color);
}

/* Handle on hover */
.select2-selection--multiple::-webkit-scrollbar-thumb:hover {
  background: var(--primary-active-color);
  cursor: pointer;
}

.list-filter-form .select2.select2-container{
  height: auto !important;
}

.horizontal-menu-bar nav > ul li a:hover {
  color: var(--sidebar-active-color);
}

.horizontal-menu-bar nav > ul a.active {
  color: var(--sidebar-active-color);
  background-color: var(--sidebar-submenu-background-color);
}

.horizontal-menu-bar nav > ul .has-sub-items ul li a:hover {
  color: var(--sidebar-active-color);
}

.select2-container {
  display: block;
}

.select2.select2-container {
  vertical-align: middle !important;
}

.select2-container--default{
}

.vdl-btn{
 padding: var(--vdl-button-padding) calc(var(--vdl-button-padding)*1.5) !important;
}


.vdl-btn-status{
  display: inline-block;
  padding: var(--vdl-button-padding) calc(var(--vdl-button-padding) * 2);
  border-radius: 0.3rem;
  margin-right: 5px;
  justify-content: center;
  display: inline-flex;
  text-align: center;
  font-size: 1.4rem;
  width: min-content;
  white-space: nowrap;
  width: 100%;
}

.input-box select, .input-box input, .input-box textarea{
  width: 380px;
}

.TransportSelect, .product_yes .description, select.kortingSelect.select2, select.productselect.select2 {
  width: 350px ;
}

.input-box select.revision_nr{
  width: 60px;
  margin-right: 20px;
}

.input-box input.referentie{
  margin-left: 15px;
  width: 100px;
}

.vdl-inform-btn, .input-box span.gsd-help{
  position: relative;
  left: 10px;
}

.productrow td.td-piece-price,.productrow td.td-total-price{
  width: 125px;
}

input.code{
  width: 70px;
}

.flex-row{
  display: flex;
  flex-direction: row;
  height: auto;
  min-height: 3.6em;
}

input.piece_price{
  width: 100px;
}

.producttype{
  width: 56px;
}

.default_table tr.dataTableRow.productrow td:nth-child(1){
  width: 50px;
  max-width: 80px;
}

.w-min-con{
  width: min-content;
}

.w-max-con{
  width: max-content;
}

.pointer{
  cursor: pointer;
}

.max-h-80vH-overflow-scroll, .gsd-modal-content{
  max-height: 80vH;
  overflow: scroll;
}

.flex-row-space-between{
  display: flex;
  justify-content: space-between;
}

.guarantee .input-row{
  gap: 10rem;
}

.guarantee .input-box .input-label{
  width: 40%;
}
.guarantee .input-box input[type=text],.guarantee  .input-box select,.guarantee  textarea, .guarantee .vdl_internal_remark{
  width: 60%
}

.guarantee  .width-50 {
  width:        48%;
  margin-right: 15px;
}

.guarantee .asterisk, .guarantee .qtipa{
  width: 1em;
}

.descriptiondateinput{
  margin-right: 1em;
}

.pi_group1 td .product_no.hour input.description{
  width: 475px;
}

.default_table tr.dataTableRow td{
  padding: 4px;
}
input, select, textarea, .content-block .dataTables_wrapper .dataTables_length select {
  /*padding: 4px;*/
}
body.body_login{
  -webkit-background-size: cover;
  background-size:         cover;
  background-attachment:   fixed;
  background-repeat:       no-repeat;
  background-position:     center center;
  background-image: url('../images/login.jpg');
}

body.body_login .main-grid main > div {
  padding: 3rem;
  background-color: var(--gray-100);
}

body.body_login .main-grid main > div #login_header{
  background: var(--gray-100) url(../images/vdl-logo.png) no-repeat center center !important;
}
body.body_login .main-grid main > div #loginscrn, body.body_login .main-grid main > div #loginscrn > div{
  background-color: var(--gray-100);
}
body.body_login .main-grid main > div #loginscrn input[type="text"], body.body_login .main-grid main > div #loginscrn input[type="password"]{
  background-color: var(--gray-400);
}

.delivery-adres input, .delivery-adres select{
  width: 50rem;
}

.w-sm.garantie-product-code{
  width: 120px;
}

.communication-order-status{
  width: 150px;
  border-radius: 3px;
  margin-right: 3px;
  padding: 3px 15px;
  border: 1px solid black;
  font-size: 12px;
}
input[type=submit][name=cancel] {
  border: 1px solid #AAA;
}
input[type="submit"][name="cancel"]:not([disabled]):hover {
  color: #fff !important;
  text-decoration: none;
  background-color: #CCC;
  border:none;
}
@media screen and (min-width: 1201px) and (max-width: 1400px) {
  .default_table tr.dataTableRow.productrow td:nth-child(1){
    width: 4rem;
    max-width: 4rem;
  }
 .producttype{
   width: 4rem;
   max-width: 4rem;
 }
  input.size{
    width: 6rem;
    max-width: 6rem;
  }
  .rem{
    width: 10px;
  }
  .add{
    width: 10px;
  }
  input.price{
    min-width: 8rem;
    max-width: 8rem;
  }
  select.incoterm{
    min-width: 6rem;
    max-width: 6rem;
  }
  .w-sm.garantie-product-code{
    width: 100px;
  }
}

.stock_view{
  display: flex;
}

.stock_level,.stock_level_max{
  flex: 1;
}
.stock_sepparator{
  width: 10px;
}
.stock_level{
 color:var(--vdl-grey-medium);
}

.stock_level_max, .stock_sepparator{
  color:var(--vdl-grey-light);
}


