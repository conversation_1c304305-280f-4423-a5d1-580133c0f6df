<?php

AppModel::loadModelClass('VisualPartContentModel');

class VisualPartContent extends VisualPartContentModel {

  public static function saveFromImport($visualPart, $description, $languages): void {
    foreach ($languages as $language) {
      if (!$visualPartContent = VisualPartContent::find_by(['visual_part_id' => $visualPart->id, 'locale' => $language])) {
        $visualPartContent = new VisualPartContent();
      }
      $visualPartContent->visual_part_id = $visualPart->id;
      $visualPartContent->locale = $language;
      $visualPartContent->name = $description;
      $visualPartContent->save();
    }
  }
}