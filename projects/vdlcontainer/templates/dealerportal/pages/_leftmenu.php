<?php
  $active_categories = [];
  if(isset($category)) {
    $active_categories = [$category->id];
    $has_parent_id     = true;
    while ($has_parent_id) {
      if(empty($categories[end($active_categories)]->parent_id)) {
        $has_parent_id = false;
      }
      else {
        $active_categories[] = $categories[end($active_categories)]->parent_id;
      }
    }
  }
?>
<ul id="leftmenu">
  <?php
    foreach ($category_tree_by_id as $category_root_id => $category_root_children): ?>
      <li>
        <a href="<?php echo $categories[$category_root_id]->getShopUrl() ?>"
           class="<?php if(in_array($category_root_id, $active_categories)): ?>active<?php endif; ?>">
          <i
            class="fa fa-chevron-right"></i> <?php echo escapeSafe($categories[$category_root_id]->getName($_SESSION['lang'])) ?>
        </a>
        <?php
          if(in_array($category_root_id, $active_categories)):
            ?>
            <ul class="category_subcats level1">
              <?php foreach ($category_root_children as $category_lvl1_id => $category_lvl1_children): ?>
                <li>
                  <a href="<?php echo $categories[$category_lvl1_id]->getShopUrl() ?>"
                     class="<?php if(in_array($category_lvl1_id, $active_categories)): ?>active<?php endif; ?>"
                     title="<?php echo escapeForInput($categories[$category_lvl1_id]->getSeoTitle($_SESSION['lang'])) ?>">
                    <i
                      class="fa fa-chevron-right"></i> <?php echo escapeSafe($categories[$category_lvl1_id]->getName($_SESSION['lang'])) ?>
                  </a>
                  <?php
                    if(in_array($category_lvl1_id, $active_categories)):
                      ?>
                      <ul class="category_subcats level2">
                        <?php foreach ($category_lvl1_children as $category_lvl2_id => $category_lvl2_children): ?>
                          <li>
                            <a href="<?php echo $categories[$category_lvl2_id]->getShopUrl() ?>"
                               class="<?php if(in_array($category_lvl2_id, $active_categories)): ?>active<?php endif; ?>"
                               title="<?php echo escapeForInput($categories[$category_lvl2_id]->getSeoTitle($_SESSION['lang'])) ?>">
                              <i
                                class="fa fa-chevron-right"></i> <?php echo escapeSafe($categories[$category_lvl2_id]->getName($_SESSION['lang'])) ?>
                            </a>
                            <?php
                              if(in_array($category_lvl2_id, $active_categories)):
                                ?>
                                <ul class="category_subcats level3">
                                  <?php foreach ($category_lvl2_children as $category_lvl3_id => $category_lvl3_children): ?>
                                    <li>
                                      <a href="<?php echo $categories[$category_lvl3_id]->getShopUrl() ?>"
                                         class="<?php if(in_array($category_lvl3_id, $active_categories)): ?>active<?php endif; ?>"
                                         title="<?php echo escapeForInput($categories[$category_lvl3_id]->getSeoTitle($_SESSION['lang'])) ?>">
                                        <i
                                          class="fa fa-chevron-right"></i> <?php echo escapeSafe($categories[$category_lvl3_id]->getName($_SESSION['lang'])) ?>
                                      </a>
                                    </li>
                                  <?php endforeach; ?>
                                </ul>
                              <?php endif ?>
                          </li>
                        <?php endforeach; ?>
                      </ul>
                    <?php endif ?>
                </li>
              <?php endforeach; ?>
            </ul>
          <?php endif ?>
      </li>
    <?php endforeach ?>
</ul>