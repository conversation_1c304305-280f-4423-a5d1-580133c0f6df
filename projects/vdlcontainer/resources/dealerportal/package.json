{"name": "vdl-frontend", "version": "0.2", "private": true, "scripts": {"build-prod": "cd ../../../../gsdfw/tools && npm run build-prod -- --config ../../projects/vdlcontainer/resources/dealerportal/webpack.config.js", "build-dev": "cd ../../../../gsdfw/tools && npm run build-dev -- --config ../../projects/vdlcontainer/resources/dealerportal/webpack.config.js", "watch": "cd ../../../../gsdfw/tools && npm run watch -- --config ../../projects/vdlcontainer/resources/dealerportal/webpack.config.js"}, "dependencies": {"jsonwebtoken": "^9.0.2"}}