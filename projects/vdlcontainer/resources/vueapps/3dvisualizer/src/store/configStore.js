import { defineStore } from "pinia"
import { ref, computed } from "vue"

export const useConfigStore = defineStore('config', () => {
  // Role of the viewer (admin or user)
  const role = ref('')
  const isAdmin = computed(() => role.value === 'admin')

  // Current selected language for the viewer
  const lang = ref('')

  // List of available languages for the viewer
  const siteLangs = ref([])

  // URL to the basket endpoint (used for adding parts to the basket or viewing the basket)
  const basketUrl = ref('')

  function initialize(config) {
    role.value = config.role || ''
    basketUrl.value = config.basketUrl || ''
    lang.value = config.lang || 'en'
    siteLangs.value = config.siteLangs || [lang.value]
  }

  return { role, isAdmin, lang, siteLangs, basketUrl, initialize }
})