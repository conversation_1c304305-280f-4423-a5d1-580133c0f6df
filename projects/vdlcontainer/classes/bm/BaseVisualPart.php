<?php
class BaseVisualPart extends AppModel {

  const DB_NAME = '';
  const TABLE_NAME = 'visual_part';
  const OM_CLASS_NAME = 'VisualPart';
  const columns = ['id', 'part_nr', 'group_nr', 'has_children', 'product_id'];
  const field_structure = [
    'id'                          => ['type' => 'mediumint', 'length' => '8', 'null' => false],
    'part_nr'                     => ['type' => 'mediumint', 'length' => '8', 'null' => false],
    'group_nr'                    => ['type' => 'mediumint', 'length' => '8', 'null' => true],
    'has_children'                => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'product_id'                  => ['type' => 'mediumint', 'length' => '8', 'null' => true],
  ];

  protected static array $primary_key = ['id'];
  protected string $auto_increment = 'id';

  public $id, $part_nr, $group_nr, $has_children, $product_id;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  /**
  * Set default object values
  */
  public function setDefaults() {
  }

  /**
   * @param array|null $conditions (required)
   * @param string $raw_sql (optional)
   * @return VisualPart[]
   */
  public static function find_all_like(?array $conditions, string $raw_sql = ''): array {
    return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array|null $conditions (required)
   * @param string $raw_sql (optional)
   * @return VisualPart[]
   */
  public static function find_all_by(?array $conditions, string $raw_sql = ''): array {
    return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $raw_sql (optional)
   * @return VisualPart[]
   */
  public static function find_all(string $raw_sql = ''): array {
    return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array|null $conditions (required)
   * @param string $raw_sql (optional)
   * @return VisualPart|false
   */
  public static function find_by(?array $conditions, string $raw_sql = ''): VisualPart|false {
    return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param int|string|null $id (required)
   * @param string $raw_sql (optional)
   * @return VisualPart|false
   */
  public static function find_by_id($id, string $raw_sql = ''): VisualPart|false {
    return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array|null $conditions (required)
   * @param string $raw_sql (optional)
   * @return int
   */
  public static function count_all_by(?array $conditions, string $raw_sql = '') {
    return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array|null $conditions (required)
   * @param string $raw_sql (optional)
   * @return bool
   */
  public static function delete_by(?array $conditions, string $raw_sql = ''): bool {
    return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

}