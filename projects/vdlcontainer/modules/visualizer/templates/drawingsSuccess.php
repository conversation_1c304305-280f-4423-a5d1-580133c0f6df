<section class="title-bar">
  <h1><?php echo __("Visualizer")?></h1>
  <ul id="tabnav" class="nav nav-tabs">
    <?php echo Navigation::writeNavigationNodes(Navigation::getItem('M_VISUALIZER')->getChildren()); ?>
  </ul>
</section>

<div class="list-filter-form">
  <a href="<?php echo PageMap::getUrl('M_VISUALIZER_VIEWER') ?>" class="gsd-btn gsd-btn-primary"><?php echo __('Toevoegen nieuwe tekening') ?></a>
</div>

<?php
  /** @var VisualDrawing[] $drawings */
?>

<table id="list-table" class="default-data-table dataTable">
  <thead>
  <tr role="row">
    <th><?php echo __('Tekening'); ?></th>
    <th><?php echo __('Gekoppelde onderdelen'); ?></th>
    <th><?php echo __('Niet-gekoppelde onderdelen'); ?></th>
    <th class="gsd-svg-icon-width-2"><?php echo __('Acties') ?></th>
  </tr>
  </thead>
  <tbody>
  <?php foreach ($drawings as $drawing): ?>
    <tr role="row">
      <td>
        <a href="<?php echo PageMap::getUrl('M_VISUALIZER_VIEWER') ?>#/<?php echo $drawing->id ?>">
          <?php echo $drawing->name ?>
        </a>
      </td>
      <td>
        <?php echo $drawing->amount_of_parts ?>
      </td>
      <td>
        <?php echo $drawing->unlinked_parts ?>
      </td>
      <td>
        <?php echo BtnHelper::getEdit(PageMap::getUrl('M_VISUALIZER_VIEWER') . '#/' . $drawing->id) ?>
        <?php echo BtnHelper::getRemove('?action=deleteDrawing&item=' . $drawing->id) ?>
      </td>
    </tr>
  <?php endforeach; ?>
  </tbody>
</table>

<style>
  table * {
    text-align: left;
  }
  tbody td:first-child {
    font-weight: bold;
  }
</style>