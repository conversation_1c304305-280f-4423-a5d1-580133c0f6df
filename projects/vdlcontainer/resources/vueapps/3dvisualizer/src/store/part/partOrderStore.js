import { defineStore } from 'pinia'
import { ref } from "vue"
import { useConfigStore } from "@/store/configStore"
import axios from "axios"

export const usePartOrderStore = defineStore('order', () => {
  const configStore = useConfigStore()
  const partToOrder = ref(null)

  function setPartToOrder(part) {
    partToOrder.value = part ?? null
  }

  /*
    * Sends request to server to add a part to the basket.
    *
    * @param {number} amount - The amount of the part to add to the basket.
    * @returns {boolean} - Whether the part was successfully added to the basket.
   */
  async function addPartToBasket(amount) {
    try {
      if (!configStore.basketUrl) throw new Error('Basket URL not set')
      const productId = partToOrder.value.product_id
      const dataString = `add=basket&size[${productId}]=${amount || 1}`
      await axios.post(configStore.basketUrl, dataString)
      return true
    } catch (error) {
      console.error(error)
      return false
    }
  }

  return {
    partTo<PERSON>rde<PERSON>,
    setPartToOrder,
    addPartToBasket
  }
})