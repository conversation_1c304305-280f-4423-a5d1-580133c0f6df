ul#navmenu-h > li > a {
  @apply text-lg lg:text-xl uppercase mx-6;
  @apply py-2 md:py-4 md:pb-6;
}

ul#navmenu-h li:hover li a, ul#navmenu-h li.iehover li a {
  @apply text-lg;
}

/*********************
 * MAIN MENU
 *********************/


/* Reset */
#navmenu-h-container ul, #navmenu-h-container li {
  margin: 0;
  padding: 0;
}

#navmenu-h-container {
  float: left;
}

ul#navmenu-h {
  margin: 0;
  padding: 0;
  list-style: none;
  position: relative;
  z-index: 1000;
  width: 100%;
}

ul#navmenu-h ul {
  width: 230px; /* Sub Menu Width */
  margin: 0;
  list-style: none;
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  padding: 10px 0;
}

ul#navmenu-h ul ul, ul#navmenu-h ul ul ul {
  top: 0;
  left: 100%;
}

ul#navmenu-h li {
  float: left;
  display: inline;
  position: relative;
}

ul#navmenu-h ul li {
  width: 100%;
  display: block;
}

/* Root Menu */
ul#navmenu-h a {
  border-top: 0;
  float: left;
  display: block;
  color: #4E4E4E;
  text-decoration: none;
  height: 1%;
  background: none;
}

ul#navmenu-h > li.sub > a::after {
  content: "\f0d7";
  font-family: 'FontAwesome';
  margin-left: 5px;
  color: #d4d4d4;
}

ul#navmenu-h .sub.iehover > a {
  background-color: #f8f8f8;
}

/* Root Menu Hover Persistence */
ul#navmenu-h a:hover {
  background: #F0F0F0;
}

/* 2nd Menu */
ul#navmenu-h li:hover li a, ul#navmenu-h li.iehover li a {
  float: none;
  padding: 8px 20px;
  border-right: 0;
  background: #f8f8f8;
}

/* 2nd Menu Hover Persistence */
ul#navmenu-h li:hover li a:hover, ul#navmenu-h li:hover li:hover a, ul#navmenu-h li.iehover li a:hover, ul#navmenu-h li.iehover li.iehover a {
  background: #F0F0F0;
}

/* 3rd Menu */
ul#navmenu-h li:hover li:hover li a, ul#navmenu-h li.iehover li.iehover li a {
  background: #999;
}

/* 3rd Menu Hover Persistence */
ul#navmenu-h li:hover li:hover li a:hover, ul#navmenu-h li:hover li:hover li:hover a, ul#navmenu-h li.iehover li.iehover li a:hover, ul#navmenu-h li.iehover li.iehover li.iehover a {
  background: #666;
}

/* 4th Menu */
ul#navmenu-h li:hover li:hover li:hover li a, ul#navmenu-h li.iehover li.iehover li.iehover li a {
  background: #666;
}

/* 4th Menu Hover */
ul#navmenu-h li:hover li:hover li:hover li a:hover, ul#navmenu-h li.iehover li.iehover li.iehover li a:hover {
  background: #333;
}

/* Hover Function - Do Not Move */
ul#navmenu-h li:hover ul ul, ul#navmenu-h li:hover ul ul ul, ul#navmenu-h li.iehover ul ul, ul#navmenu-h li.iehover ul ul ul {
  display: none;
}

ul#navmenu-h li:hover ul, ul#navmenu-h ul li:hover ul, ul#navmenu-h ul ul li:hover ul, ul#navmenu-h li.iehover ul, ul#navmenu-h ul li.iehover ul, ul#navmenu-h ul ul li.iehover ul {
  display: block;
}

#navmenu-h a.a_active {
  background: #F0F0F0;
  color: #002c51;
}

#navmenu-h-container {
  border: 0;
}
ul#navmenu-h a  {
  color: #333333;
  line-height: 20px;
}
ul#navmenu-h li:hover li a,ul#navmenu-h li.iehover li a {
  color: #002c51;

}
#navmenu-h a.a_active {
  background: white;
  box-shadow: inset 0 -5px 0 #002c51;
}
ul#navmenu-h .sub.iehover > a {
  background: white;
  box-shadow: inset 0 -5px 0 #002c51;
}
ul#navmenu-h li:hover,
ul#navmenu-h a:hover {
  background: white;
}
ul#navmenu-h li:hover > a {
  box-shadow: inset 0 -5px 0 #002c51;
}

ul#navmenu-h ul {
  box-shadow: 0 2px 1px rgba(0,0,0,0.2);
  background: white;
  border-top: 1px solid #002c51;
}

ul#navmenu-h li:hover li a, ul#navmenu-h li.iehover li a {
  color: #333333;
  background: white;
  padding: 10px 16px;
}

ul#navmenu-h li:hover li a:hover, ul#navmenu-h li.iehover li a:hover {
  background: #f5f5f5;
  color: #002c51;
  box-shadow: none;
  padding: 10px 16px;
}
#navmenu-h ul a.a_active {
  background: white;
  box-shadow: none;
}

/* Remove arrow down in main navigation - to fit more nav items in the bar */
ul#navmenu-h > li.sub > a::after {
  display: none;
}