import { defineStore } from "pinia"
import { ref, watch } from "vue"
import { usePartThreeStore } from "@/store/part/partThreeStore"
import { usePartEditStore } from "@/store/part/partEditStore"
import threeContext from "@/utils/three/threeContext"
import { colorObject } from "@/utils/three/utils/threeHelpers"

export const useInteractionStore = defineStore('interaction', () => {
  const partThreeStore = usePartThreeStore()
  const partEditStore = usePartEditStore()

  // Reset the interaction state when the current focused part changes or a part was submitted
  watch(() => partThreeStore.currentPart, () => {
    resetState()
    invisibleParts.value.clear()
  })
  watch(() => partEditStore.lastSubmittedPartNr, resetState)

  const hoveredPartName = ref()
  watch(hoveredPartName, (newName, oldName) => {
    const newObjects = threeContext.getObjectsByName(newName)
    const oldObjects = threeContext.getObjectsByName(oldName)
    const isHover = true

    // Color the new part and revert the color of the old part
    colorObject(newObjects, false, isHover)
    colorObject(oldObjects, true, isHover)
  })

  const clickedPartName = ref()
  watch(clickedPartName, (newName, oldName) => {
    const newObjects = threeContext.getObjectsByName(newName)
    const oldObjects = threeContext.getObjectsByName(oldName)

    // Color the new part and revert the color of the old part
    colorObject(newObjects)
    colorObject(oldObjects, true)
  })

  function resetState() {
    hoveredPartName.value = null
    clickedPartName.value = null
  }

  // Restore the camera state to the original state, useful to get the 3D object back in view
  function resetCameraState() {
    threeContext.restoreCameraState()
  }

  const invisibleParts = ref(new Set());
  function togglePartVisible(partName = clickedPartName.value) {
    if (!partName) return
    const objects = threeContext.getObjectsByName(partName)
    for (const object of objects) {
      object.visible = !object.visible;
      if (!object.visible) {
        invisibleParts.value.add(object.name);
      } else {
        invisibleParts.value.delete(object.name);
      }
    }
    if (clickedPartName.value === partName && !objects[0].visible) {
      clickedPartName.value = null;
    }
  }
  function setAllPartsVisible() {
    invisibleParts.value.forEach(partName => {
      const objects = threeContext.getObjectsByName(partName)
      for (const object of objects) {
        object.visible = true;
      }
    });
    invisibleParts.value.clear();
  }
  return { hoveredPartName, clickedPartName, resetState, resetCameraState, invisibleParts, togglePartVisible, setAllPartsVisible }
})