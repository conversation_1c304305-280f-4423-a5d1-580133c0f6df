/*<PERSON>lden*/
.product_image_table {
  border-collapse: collapse;
}
.product_image_table .product_image_bottomborder {
  border-bottom: 5px solid var(--main-background-color);
}

.product_image_table .product_image_bottomborder .flex-div {
  display: flex;
  flex-direction: row;
}

.product_image_table .product_image_bottomborder .div-languagerow {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  gap: 10px
}

.product_image_table .product_image_bottomborder .div-languagerow img {
  width: 22px;
  height: 15px;
}


/*Gerelateerde */
.product_related_tablehead {
  display: grid;
  grid-template-columns: 1fr 1fr
}

.related-products {
  display: grid;
  grid-template-columns: 1fr 1fr;
}

.related-products-left {
  grid-column: 1 / span 1;
  padding: 10px;
  display: flex;
  flex-direction: column;
}

.related-products-right {
  grid-column: 2 / span 1;
  padding: 10px;
  display: flex;
  flex-direction: column;
}

.related-products-left .related-product-row, .related-products-right .related-product-row {
  display: grid;
  grid-template-columns: 12fr 2fr 1fr;
  flex-direction: row;
  align-items: center;
  margin-bottom: 10px;
  gap: 10px;
  width: 100%;
}

.mb-4 .related-product-row .material-icons {
  color: red;
}

.related-products-left .input_amount_add, .related-products-right .input_amount_add {
  padding: 11px;
  max-width: 100px;
}

.select2-container {
}

.product_descr input, .product_descr textarea{
  min-width: 30rem;
  width: 50rem ;
}