<script setup>
import { useRouterStore } from "@/store/routerStore"
import { useInteractionStore } from "@/store/interactionStore"

const routerStore = useRouterStore()
const interactionStore = useInteractionStore()
</script>

<template>
  <v-card class="d-flex align-center justify-center pa-3" rounded="xl" color="primary">
    <v-btn
        icon
        variant="text"
        color="white"
        @click="routerStore.popPartFromRoute()"
    >
      <v-icon>mdi-arrow-left</v-icon>
    </v-btn>

    <v-btn
        icon
        variant="text"
        color="white"
        @click="interactionStore.resetCameraState"
    >
      <v-icon>mdi-crosshairs-gps</v-icon>
    </v-btn>
  </v-card>
</template>