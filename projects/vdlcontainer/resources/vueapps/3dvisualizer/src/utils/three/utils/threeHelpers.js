import { Vector3, MeshStandardMaterial, Box3, Matrix4, InstancedMesh } from 'three'
import gsap from "gsap"

// instantiating reusable three objects
const positionVector = new Vector3()
const sizeVector = new Vector3()
const box = new Box3()
const matrix = new Matrix4()
const clickMaterial = new MeshStandardMaterial({ color: 0xffffff })
const hoverMaterial = new MeshStandardMaterial({ color: 0xcccccc })

/**
 * Gets the absolute world position of an Object3D in the Three.js scene.
 * - Note: Use `useNewVector=true` if you want a new Vector3 instance to avoid overwriting an existing position.
 *
 * @param {THREE.Object3D} object - The object to get the position of.
 * @param {boolean} [useNewVector=false] - If true, creates a new Vector3 to store the position.
 * @returns {THREE.Vector3} - The world position of the object.
 */
export function getAbsolutePositionOfObject(object, useNewVector = false) {
  const targetVector = useNewVector ? new Vector3() : positionVector

  if (object.isInstancedMesh) setInstancePosition(object, targetVector)
  else object.getWorldPosition(targetVector)

  return targetVector
}

function setInstancePosition(object, vector) {
  // Handle InstancedMesh
  const instanceMatrix = new Matrix4()
  object.getMatrixAt(0, instanceMatrix)

  // Apply instance transformation to a vector at (0, 0, 0)
  vector.set(0, 0, 0).applyMatrix4(instanceMatrix)

  // Convert to world position
  object.updateMatrixWorld(true)  // Ensure world matrix is updated
  vector.applyMatrix4(object.matrixWorld)
}

/**
 * Sets the target of controls to the absolute position of an object.
 * Useful for centering or focusing on an object in the scene.
 *
 * @param {THREE.Object3D} object - The object to set as the target.
 * @param {Controls} controls - The controls to update with the target's position.
 */
export function setTarget(object, controls, smoothTransition = true) {
  const position = getAbsolutePositionOfObject(object) // Get object's world position
  const { x, y, z } = position
  if (controls.setTarget) controls.setTarget(x, y, z, smoothTransition) // Smoothly set the target position
  else controls.target = position
}

/**
 * Sets the camera distance based on the largest dimension of an Object3D.
 * This function is useful for framing the entire object within the view.
 *
 * @param {THREE.Object3D} object - The object to base the distance on.
 * @param {Controls} controls - The controls to update with the desired distance.
 */
export function setDistance(object, controls, smoothTransition = true) {
  const sizeVector = getSizeVectorFromObject(object)
  const maxDimension = Math.max(sizeVector.x, sizeVector.y, sizeVector.z)
  const distanceFactor = 1.8 // Change this factor if needed
  // distance should never be set to less than 0.5 to prevent the object from "disappearing"
  const desiredDistance = Math.max((maxDimension * distanceFactor), 0.5)
  controls.dollyTo?.(desiredDistance, smoothTransition)
}

/**
 * Gets the size of an Object3D in the scene.
 * @param object
 * @returns {Vector3}
 */
function getSizeVectorFromObject(object) {
  if (object instanceof InstancedMesh) {
    // Compute the bounding box of a single instance
    object.getMatrixAt(0, matrix)
    object.geometry.computeBoundingBox()
    const box = object.geometry.boundingBox.clone()
    box.applyMatrix4(matrix)
  }
  else box.setFromObject(object)

  box.getSize(sizeVector)
  return sizeVector
}

/**
 * Sets the visibility of all direct siblings of an Object3D.
 * If the object has no parent or its parent is the Scene itself, no action is taken.
 *
 * @param {THREE.Object3D} object - The reference object whose siblings will be affected.
 * @param {boolean} visible - Visibility state to apply to the siblings.
 */
export function setVisibilityOfSiblings(object, visible) {
  const parent = object?.parent

  // If parent is the Scene, avoid altering important scene-level objects like lights or cameras
  if (!parent || parent.type === "Scene") return

  parent.children.forEach(child => {
    const isSiblingWithVisibleProperty = (child.id !== object.id) && 'visible' in child
    if (isSiblingWithVisibleProperty) child.visible = visible
  })
}

/**
 * Returns an object to its original state, with all children visible.
 *
 * @param object
 * @param visible
 */
export function returnObjectToUntouchedState(object) {
  object?.traverse(child => {
    child.visible = true
    if (child.defaultMaterial) child.material = child.defaultMaterial
  })
}

export function getRootObjectFromChild(child) {
  if (!child?.parent || child.parent.type === "Scene") return child
  return getRootObjectFromChild(child.parent)
}

/**
 * Sets the visibility of all children of an Object3D.
 * Only affects direct children of the specified object.
 *
 * @param {THREE.Object3D} object - The reference object whose children will be affected.
 * @param {boolean} visible - Visibility state to apply to the children.
 */
export function setVisibilityOfChildren(object, visible) {
  object?.children.forEach(child => {
    child.visible = visible
  })
}

/**
 * Recursively hides the siblings of the given object and its parents until the scene level.
 *
 * @param {THREE.Object3D} object - The object whose parent's siblings will be hidden.
 * This function will traverse upwards in the hierarchy and call `setVisibilityOfSiblings`
 * for each parent (except for the scene itself).
 */
export function hideAncestorsSiblings(object) {
  if (object?.parent && object.parent.type !== "Scene") {
    setVisibilityOfSiblings(object, false)
    hideAncestorsSiblings(object.parent)
  }
}

/**
 * Applies a specified color material to all mesh objects within one or more Object3Ds, or reverts them to their original color.
 *
 * @param {THREE.Object3D | THREE.Object3D[]} objects - The root 3D object(s) whose meshes will be colored or reverted.
 * @param {boolean} [revert=false] - If `true`, reverts meshes to their original material. If `false`, applies the specified `material`.
 * @param isHover
 */
export function colorObject(objects, revert = false, isHover = false) {
  const list = Array.isArray(objects) ? objects : [objects]

  for (const object of list) {
    object?.traverse(child => {
      if (!child.isMesh || !child.material) return
      if (revert) {
        if (!child.defaultMaterial) return
        child.material = child.defaultMaterial
      } else {
        child.defaultMaterial ??= child.material
        child.material = isHover ? hoverMaterial : clickMaterial
      }
    })
  }
}

/**
 * Performs a Breadth-First Search (BFS) to locate an object by id, prioritizing top-level objects before nested ones.
 * This approach ensures that objects higher in the hierarchy are found first, unlike depth-first search.
 *
 * @param {THREE.Object3D} objectToSearchIn - The root object to search within.
 * @param {number} id - The id of the object to find.
 * @returns {THREE.Object3D | null} - The found object, or `null` if no matching object is found.
 */
export function getObjectByIdBFS(objectToSearchIn, id) {
  const queue = objectToSearchIn ? [objectToSearchIn] : []
  while (queue.length > 0) {
    const current = queue.shift()
    if (current.id === id) return current
    // Add children to the end of the queue for BFS traversal
    if (current.children) queue.push(...current.children)
  }
  // Return null if no object with the specified name is found
  return null
}

/**
 * Checks the visibility of an Object3D and all its parent objects.
 * If any parent object has visibility set to `false`, this function returns `false`,
 * as that would override the visibility of its children. Otherwise, returns `true`.
 *
 * @param {THREE.Object3D} object - The 3D object to check visibility for.
 * @returns {boolean} - `true` if the object and all parents are visible otherwise, `false`.
 */
export function isVisible(object) {
  if (!object || !object.visible) return false
  if (object.parent) return isVisible(object.parent)
  return true // if no parent, it's visible
}

/**
 * Recursively retrieves the root object of a 3D model.
 * Assumes the root is reached when an object has more than one child or no children.
 * Useful for models where the primary object is nested within a single-child hierarchy.
 *
 * @param {THREE.Object3D} object - The 3D object to analyze.
 * @returns {THREE.Object3D} - The root object of the 3D model.
 */
export function getRootObject(object) {
  if (object?.children.length === 1) return getRootObject(object.children[0])
  return object
}

/**
 * Extends the given controls object with additional methods for smooth or immediate target and dolly transitions.
 *
 * @param {Object} controls - The controls object to extend. Typically, this is an instance of OrbitControls, TrackballControls or similar.
 * @param {Object} camera - The camera used by the controls
 */
export function extendControls(controls) {
  extendSetTarget(controls)
  extendDollyTo(controls)
}

/**
 * Smoothly or immediately sets the target of the controls.
 *
 * @param {Object} controls - The controls object to extend.
 */
function extendSetTarget(controls) {
  controls.setTarget = (x, y, z, isSmooth = true) => {
    const target = new Vector3(x, y, z)
    if (isSmooth) {
      gsap.to(controls.target, {
        x: target.x,
        y: target.y,
        z: target.z,
        duration: 0.5, // Adjust duration as needed
        onUpdate: () => controls.update(),
      })
    } else {
      controls.target.copy(target)
      controls.update()
    }
  }
}

/**
 * Smoothly or immediately changes the camera's distance to the target.
 *
 * @param {Object} controls - The controls object to extend.
 */
function extendDollyTo(controls) {
  const camera = controls.camera ?? controls.object
  controls.dollyTo = (distance, isSmooth = true) => {
    const direction = new Vector3()
    camera.getWorldDirection(direction) // Get the camera's forward direction

    const newPosition = controls.target.clone().add(direction.multiplyScalar(-distance))

    if (isSmooth) {
      gsap.to(camera.position, {
        x: newPosition.x,
        y: newPosition.y,
        z: newPosition.z,
        duration: 0.5, // Adjust duration as needed
        onUpdate: () => controls.update(),
      })
    } else {
      camera.position.copy(newPosition)
      controls.update()
    }
  }
}
