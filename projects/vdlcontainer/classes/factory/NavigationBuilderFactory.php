<?php

  /**
   * Created by PhpStorm.
   * Date: 1-3-2016
   * Time: 12:35
   */
  class NavigationBuilderFactory extends NavigationBuilder {

    public static function buildBackend() {
      parent::buildBackend();

      $nav = Navigation::getInstance();

      $nav->removePMItem('M_ORDER_REPEAT', 'M_ORDER_OV');
      $nav->addPMItem('M_STATS_PRODUCTSEXP', 'M_STATS');

      $nav->addPMItem('M_ORDER_EXPORT_VBS', 'M_ORDER_OV');

      $nav->addPMItem('M_CATALOG_EXPORT_VBS', 'M_CATALOG');
      $nav->addPMItem('M_CATALOG_RIGHTS_GROUP', 'M_CATALOG', true, 'M_CATALOG_OV');
      $nav->addPMItem('M_SERVICE_NAV', 'M_TOP');
      $nav->addPMItem('M_SERVICE', 'M_SERVICE_NAV');
      $nav->addPMItem('M_ORDER_REPEAT', 'M_SERVICE_NAV');
      $nav->addPMItem('M_DOWNLOADS_PRODUCTINFO', 'M_WEBSITE');
      //      $nav->addPMItem('M_PLANNING','M_SERVICE_NAV',true,'M_SERVICE');

      // checking rights, prevents adding a empty menu item
      $has_guarantee_rights = false;
      foreach ([
                 'M_GUARANTEE_CLAIMS',
                 'M_GUARANTEE_STATS',
                 'M_GUARANTEE_STATS_PRODUCTS',
                 'M_GUARANTEE_STATS_PRODUCTS_EXPANDED',
                 'M_GUARANTEE_STATS_COSTS_BY_CUSTOMER',
                 'M_GUARANTEE_STATS_GROUPED',
               ] as $privilege) {
        if (Privilege::hasRight($privilege)) $has_guarantee_rights = true;
      }
      if ($has_guarantee_rights) {
        $nav->addPMItem('M_GUARANTEE', 'M_TOP', true, 'M_STATS');
        $nav->addPMItem('M_GUARANTEE_CLAIMS', 'M_GUARANTEE');
        $nav->addPMItem('M_GUARANTEE_STATS', 'M_GUARANTEE');
        $nav->addPMItem('M_GUARANTEE_STATS_PRODUCTS', 'M_GUARANTEE_STATS');
        $nav->addPMItem('M_GUARANTEE_STATS_PRODUCTS_EXPANDED', 'M_GUARANTEE_STATS');
        $nav->addPMItem('M_GUARANTEE_STATS_COSTS_BY_CUSTOMER', 'M_GUARANTEE_STATS');
        $nav->addPMItem('M_GUARANTEE_STATS_GROUPED', 'M_GUARANTEE_STATS');
        $nav->setShowMainMenuChildren('M_GUARANTEE_STATS', false); //kinderen niet tonen in hoofnavigatie
      }

      // put warehouse under different main menu item
      $item = Navigation::getItem('M_STOCK');
      if ($item) {
        $nav->removePMItem('M_STOCK', 'M_TOP');
        $nav->removePMItem('M_WAREHOUSE', 'M_STOCK');
      }
      $nav->addPMItem('M_WAREHOUSE', 'M_OTHER');

      // checking rights, prevents adding a empty menu item
      $has_machine_rights = false;
      foreach (['M_MACHINES', 'M_MACHINES_IMPORT', 'M_MACHINES_IMPORT_LOG'] as $privilege) {
        if (Privilege::hasRight($privilege)) $has_machine_rights = true;
      }
      if ($has_machine_rights) {
        $nav->addPMItem('M_MACHINES_NAV', 'M_TOP', true, 'M_GUARANTEE');
        $nav->addPMItem('M_MACHINES', 'M_MACHINES_NAV');
        $nav->addPMItem('M_MACHINES_IMPORT', 'M_MACHINES_NAV');
        $nav->addPMItem('M_MACHINES_IMPORT_LOG', 'M_MACHINES_NAV');
      }

      // webshop modules/pages
      $nav->addPMItem('M_WEBSHOP_MACHINE_SEARCH', 'M_TOP', true, 'M_HOME');
      $nav->addPMItem('M_WEBSHOP_MACHINE_VIEW', 'M_WEBSHOP_MACHINE_SEARCH');
      $nav->addPMItem('M_WEBSHOP_VISUALIZER', 'M_WEBSHOP_MACHINE_SEARCH');
      $nav->addPMItem('M_WEBSHOP_ORDER', 'M_PRODUCTS');
      $nav->addPMItem('M_WEBSHOP_DOWNLOADS_NAV', 'M_TOP', true, 'M_WEBSHOP_GUARANTEE');
      $nav->addPMItem('M_WEBSHOP_DOWNLOADS', 'M_WEBSHOP_DOWNLOADS_NAV');
      $nav->addPMItem('M_WEBSHOP_DOWNLOADS_PRODUCTINFO', 'M_WEBSHOP_DOWNLOADS_NAV');
      $nav->addPMItem('M_WEBSHOP_HELPDESK', 'M_TOP', true, 'M_WEBSHOP_DOWNLOADS_NAV');

      $user = !empty($_SESSION['userObject']); //webshop garantie module - in test phase alleen voor xxx klanten

      $nav->addPMItem('M_WEBSHOP_CONTACT', 'M_WEBSHOP_HELPDESK');
      $nav->addPMItem('M_WEBSHOP_FAQ', 'M_WEBSHOP_HELPDESK');
      $nav->addPMItem('M_BASKET', 'M_PRODUCTS');
      $nav->addPMItem('M_SAVED_BASKETS', 'M_PRODUCTS');

      if ($user && Config::isdefined("GUARANTEE_ORGANISATIONS") && in_array($_SESSION['userObject']->organisation->id, Config::get("GUARANTEE_ORGANISATIONS"))) {
        $nav->addPMItem('M_WEBSHOP_GUARANTEE', 'M_TOP', true, 'M_PRODUCTS');
        $nav->addPMItem('M_WEBSHOP_CUSTOMER_REQUEST', 'M_WEBSHOP_GUARANTEE');
      }
      if ($navitem = $nav->getItem('M_WEBSHOP_MACHINE_VIEW')) {
        $navitem->setShowMainMenu(false); // don't show in main navigation
      }
      $nav->addPMItem('M_VISUALIZER', 'M_TOP', true, 'M_SERVICE');
      $nav->addPMItem('M_VISUALIZER_DRAWINGS', 'M_VISUALIZER');
      $nav->addPMItem('M_VISUALIZER_VIEWER', 'M_VISUALIZER');
      $nav->addPMItem('M_VISUALIZER_IMPORT', 'M_VISUALIZER');
      $nav->addPMItem('M_VISUALIZER_IMPORT_LOG', 'M_VISUALIZER');

      $nav->addPMItem('M_VDL_GPT', 'M_TOP');
      $nav->addPMItem('M_VDL_GPT_CHAT', 'M_VDL_GPT');
      $nav->addPMItem('M_VDL_GPT_FILES', 'M_VDL_GPT');
      $nav->addPMItem('M_VDL_GPT_HISTORY', 'M_VDL_GPT');

    }

    public static function writeNav($root_page_id = 'M_TOP') {
      $nav = Navigation::getInstance();

      $hide_children = [
        'M_SHEETS',
        'M_SETTINGS',
        'M_ORGANISATION_OV',
        'M_ORGANISATION_LEV',
        'M_PAGES',
        'M_SELECTSITE',
        'M_PROSPECTS',
      ];

      $out = '<ul id="navmenu-h">';
      $items = Navigation::getItemChildren($root_page_id);

      $key = 0;
      foreach ($items as $item) {
        if ($item->hasRight() && $item->isShow() && $item->isShowMainMenu()) {
          $subitems = Navigation::getItemChildren($item->getPageId());
          if ($subitems && (($item->getPageId() == 'M_ORGANISATIONS' && count($subitems) <= 1) || in_array($item->getPageId(), $hide_children))) {
            $subitems = [];
          }

          $main_as_sub = clone $item;
          $main_as_sub->children = [];
          //@todo: dit is een dynamische property, welke deprecated is
//          $main_as_sub->isClone = true;
          $set_main_sub = false;

          switch ($main_as_sub) {
            case $main_as_sub->getPageId() == "M_WEBSHOP_GUARANTEE":
              $main_as_sub->name = __("overzicht");
              $set_main_sub = true;
              break;
            case $main_as_sub->getPageId() == "M_PRODUCTS":
              $set_main_sub = true;
              break;
            case $main_as_sub->getPageId() == "M_WEBSHOP_MACHINE_SEARCH":
              $main_as_sub->name = __("webshop_machine_search");
              $set_main_sub = true;
              break;
            default:
              break;
          }

          $hassubitems = false;
          foreach ($subitems as $si) {
            if ($si->hasRight() && $si->isShow() && $si->isShowMainMenu()) {
              $hassubitems = true;
            }
          }
          if ($set_main_sub) {
            $subitems = array_merge([$main_as_sub], $subitems);
            $hassubitems = true;
          }

          $link = "";
          if (!$item->isDisabled()) {
            $link = PageMap::getUrl($item->getPageId());
          }
          $out .= '<li ';
          if ($hassubitems) {
            $out .= 'class="sub" ';
          }
          $out .= '>';
          $out .= '<a href="' . $link . '" ';
          $out .= '<a href="' . $link . '" ';
          if ($item->getTarget() != '') {
            $out .= 'target="' . $item->getTarget() . '"';
          }
          if ($item->isActive()) {
            $out .= ' class="a_active"';
          }
          $out .= '>';
          $out .= $item->getName();
          $out .= '</a>';

          if ($hassubitems) {
            $out .= '<ul>';
            foreach ($subitems as $subitem) {
              if ($subitem->hasRight() && $subitem->isShow() && $subitem->isShowMainMenu()) {
                $subsubitems = Navigation::getItemChildren($subitem->getPageId());
                // do not show sub items for these nav categories
//                if ($subsubitems && in_array($subitem->getPageId(), $hide_children) || !empty($subitem->isClone)) {
                if ($subsubitems && in_array($subitem->getPageId(), $hide_children)) {
                  $subsubitems = [];
                }

                $subsubitems = array_filter($subsubitems, function ($subsubitem) {
                  // filter out items which wont be shown
                  return $subsubitem->hasRight() && $subsubitem->isShow() && $subsubitem->isShowMainMenu();
                });

                $link = "";
                if (!$subitem->isDisabled()) {
                  $link = PageMap::getUrl($subitem->getPageId());
                }
                $out .= '<li>';
                $out .= '<a href="' . $link . '" ';
                if ($subitem->getTarget() != '') {
                  $out .= 'target="' . $subitem->getTarget() . '"';
                }
                if ($subitem->isActive()) {
                  $out .= ' class="a_active" ';
                }
                $out .= '>';
                $out .= $subitem->getName();
                $out .= '</a>';

                if (count($subsubitems) > 0  && !in_array($main_as_sub->getPageId(),["M_WEBSHOP_GUARANTEE","M_PRODUCTS"])) {
                  $out .= '<ul>';
                  foreach ($subsubitems as $subsubitem) {
                    if ($subsubitem->hasRight() && $subsubitem->isShow() && $subsubitem->isShowMainMenu()) {
                      if (!$subsubitem->isDisabled()) {
                        $link = PageMap::getUrl($subsubitem->getPageId());
                      }
                      $out .= '<li>';
                      $out .= '<a href="' . $link . '" ';
                      if ($subsubitem->getTarget() != '') {
                        $out .= 'target="' . $subsubitem->getTarget() . '"';
                      }
                      if ($subsubitem->isActive()) {
                        $out .= ' style="color: #681821;" ';
                      }

                      $out .= '>';
                      $out .= htmlentities($subsubitem->getName());
                      $out .= '</a>';

                      $items3 = Navigation::getItemChildren($subsubitem->getPageId());
                      if (count($items3) > 0) {
                        $out .= '<ul>';
                        foreach ($items3 as $item3) {
                          if ($item3->hasRight() && $item3->isShow() && $item3->isShowMainMenu()) {
                            if (!$item3->isDisabled()) {
                              $link = PageMap::getUrl($item3->getPageId());
                            }
                            $out .= '<li>';
                            $out .= '<a href="' . $link . '" ';
                            if ($item3->getTarget() != '') {
                              $out .= 'target="' . $item3->getTarget() . '"';
                            }
                            if ($item3->isActive()) {
                              $out .= ' style="color: #681821;" ';
                            }

                            $out .= '>';
                            $out .= htmlentities($item3->getName());
                            $out .= '</a>';
                            $out .= '</li>';

                          }
                        }
                        $out .= '</ul>';
                      }
                      $out .= '</li>';
                    }
                  }
                  $out .= '</ul>';
                }
                $out .= '</li>';
              }
            }
            $out .= '</ul>';
          }
          $out .= '</li>';

          $key++;
        }
      }
      $out .= '</ul>';
      return $out;
    }

  }