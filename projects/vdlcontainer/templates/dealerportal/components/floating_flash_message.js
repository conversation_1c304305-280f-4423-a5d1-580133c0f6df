let floating_flash_message_template = `
<style>
    :host {
      position:      fixed;
      display:       none;
      top:           15px;
      left: 50%;
      transform: translate(-50%, 0);
      padding:       10px 25px;
      z-index:       1100;
      font-size:     18px;
      box-shadow:    0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
      border-radius: 8px;
      color:         white;
      opacity:       0;
    }

    :host(.success) {
      background: #38a169;
    }
    :host(.show) {
      display:    block;
    }
    :host(.short) {
      animation:  fadeInOut 3s;
    }
    :host(.long) {
      animation:  fadeInOut 6s;
    }
    :host(:hover) {
      /* pause the fadeout animation while cursor is on the message */
      animation-play-state: paused;
    }

    :host(.error) {
      background: #e53e3e;
    }
    
    a {
      color: white;
      text-decoration: underline;
    }

    @keyframes fadeInOut {
      0% {
        opacity: 0;
      }
      16% {
        opacity: 1;
      }
      84% {
        opacity: 1;
      }
      100% {
        opacity: 0;
      }
    }
  </style>
  <div class="message"></div>`;


class FloatingFlashMessage extends HTMLElement {
  constructor() {
    super();
    this.shadow = this.attachShadow({mode: "open"});
  }

  static get observedAttributes() {
    return ['message'];
  }

  // watch attribute changes
  attributeChangedCallback(attr, oldValue, newValue) {
    if (attr == 'message') {
      this.message                                    = newValue;
      this.shadow.querySelector('.message').innerHTML = this.message;
    }
  }


  connectedCallback() {
    this.setAttribute('role', 'listbox')
    this.shadow.innerHTML = floating_flash_message_template;
  }
}

window.customElements.define("floating-flash-message", FloatingFlashMessage);