
<header class="" style="border-bottom: 1px solid #002c51;">

  <div class="logo-vdl">
    <a href="/" class="mx-auto">
      <img src="<?php echo $site->getTemplateUrl() ?>images/VDL-logo.svg" width="150" />
    </a>
  </div>

  <div class="container px-2 md:px-4 inline-flex flex-col">
    <div class="flex flex-shrink-0 py-3 items-center justify-end">

      <?php if(isset($_SESSION['was_admin']) && $_SESSION['was_admin'] != ""): ?>
        <a href="#return2admin" id="return2admin" title="TERUG NAAR UW ACCOUNT"
           class="text-primary mr-4 text-sm font-semibold hidden xl:inline-block">
          TERUG NAAR ACCOUNT
          <?php echo IconHelper::getLogin() ?>
        </a>
      <?php endif; ?>

      <div class="inline-flex items-center text-primary justify-around w-full md:w-auto">
        <div class="mx-3 text-black hidden md:inline-block">
          <?php echo $_SESSION['userObject']->getNaam(); ?> -
          <?php echo $_SESSION['userObject']->organisation->getBedrijfsnaam() ?>
        </div>

        <?php if(Privilege::hasRight('M_BASKET')): ?>
          <a class="relative md:mx-2" href="<?php echo PageMap::getUrl('M_BASKET') ?>">
            <img src="<?php echo $site->getTemplateUrl() ?>images/icons/cart.svg" class="w-8 md:w-6" />
            <?php if(isset($_SESSION['basket']) && isset($_SESSION['basket']['products'])): ?>
              <div
                class="absolute top-0 right-0 -mt-2 -mr-2 flex px-1 justify-center items-center bg-highlight rounded-full text-white text-xs text-center">
                <?php echo array_sum(array_column($_SESSION['basket']['products'], 'size')) ?>
              </div>
            <?php endif; ?>
          </a>

          <popout-box class="md:mx-2" style="height: 21.17px;">
            <div slot="trigger">
                <div href="<?php echo PageMap::getUrl('M_SAVED_BASKETS') ?>" class="hooover">
                <img src="<?php echo $site->getTemplateUrl() ?>images/icons/bewaarde-winkelwagens.png" class="w-8 md:w-6" />
                <?php if(ArrayHelper::hasData($_SESSION['saved_baskets'])): ?>
                  <div
                    class="absolute top-0 right-0 -mt-2 -mr-2 flex px-1 justify-center items-center bg-highlight rounded-full text-white text-xs text-center">
                    <?php echo count($_SESSION['saved_baskets']) ?>
                  </div>
                <?php endif; ?>
              </div>
            </div>
            <div slot="popout-content">
              <?php if(ArrayHelper::hasData($_SESSION['saved_baskets'])): ?>
                <div class="list-menu">
                  <?php foreach($_SESSION['saved_baskets'] as $saved_basket): ?>
                    <a href="<?php echo PageMap::getUrl('M_SAVED_BASKETS') ?>" role="menuitem" tabindex="-1">
                      <?php echo $saved_basket->name ?>
                    </a>
                  <?php endforeach; ?>
                </div>
              <?php else: ?>
                <div class="p-3 text-lg"><?php echo __('U heeft nog geen bewaarde winkelwagens.') ?></div>
              <?php endif; ?>
            </div>
          </popout-box>
        <?php endif; ?>

        <a class="md:mx-3" href="<?php echo PageMap::getUrl('M_SETTINGS_PERS') ?>">
          <?php echo PageMap::getName('M_SETTINGS_PERS') ?>
        </a>

        <a class="md:mx-3" href="<?php echo PageMap::getUrl('M_LOGOFF') ?>">
          <?php echo PageMap::getName('M_LOGOFF') ?>
        </a>

        <div class="language-menu">
          <ul>
            <li>
              <img src="<?php echo $site->getTemplateUrl() ?>images/<?php echo $_SESSION['lang'] ?>.svg"
                   class="w-6" />
            </li>
            <?php foreach ($site->site_host->getPossibleLangs() as $language):
              if($_SESSION['lang'] == $language) continue; // skip active language, defined above
              ?>
              <li>
                <a href="/<?php echo $language ?>/home">
                  <img src="<?php echo $site->getTemplateUrl() ?>images/<?php echo $language ?>.svg" class="w-6" />
                </a>
              </li>
            <?php endforeach; ?>
          </ul>
        </div>
      </div>
    </div>
    <div id="headmenu">
      <div>
        <?php $pageId = $pageId=="M_EXTERNAL"?"M_HOME":$pageId ?>
        <?php if(Navigation::getInstance()->getWritenav($pageId) == true): ?>
          <div id="navmenu-h-container">
<!--            --><?php //echo Navigation::getBackendNavigationHtml() ?>
            <?php echo NavigationBuilderFactory::writeNav();?>
          </div>
        <?php endif; ?>
      </div>
    </div>

  </div>

  <div class="logo-dealer">
    <?php if(!empty($_SESSION['userObject']->organisation->logo_thumb) && file_exists(DIR_UPLOADS . 'logo/' . $_SESSION['userObject']->organisation->logo_orgin)): ?>
      <img src="<?php echo URL_UPLOADS . 'logo/' . $_SESSION['userObject']->organisation->logo_orgin ?>" class="w-52" />
    <?php endif; ?>
  </div>

</header>


<script type="text/javascript">

  $(document).ready(function () {
    $("#navmenu-h li").hover(
      function () {
        $(this).addClass("iehover");
      },
      function () {
        $(this).removeClass("iehover");
      });
  });

  <?php if(isset($_SESSION['userObject'])): ?>
  $(document).ready(function () {
    $("#return2admin").click(function (event) {
      event.preventDefault();
      return2admin();
    });
  });

  function return2admin() {
    $.getJSON("<?php echo reconstructQueryAdd(['pageId' => 'M_AJAX'])?>inner_action=return2admin").done(
      function (data) {
        if (data != 'false') {
          window.location = data;
        }
      }
    );
  }
  <?php endif; ?>
</script>