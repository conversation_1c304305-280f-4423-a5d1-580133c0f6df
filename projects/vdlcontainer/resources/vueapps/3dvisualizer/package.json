{"name": "3d-visualizer", "version": "0.0.1", "private": true, "scripts": {"dev": "vite", "build": "vite build", "build-dev": "vite build --mode development", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "dependencies": {"@mdi/font": "^7.4.47", "@tresjs/cientos": "^4.3.1", "@tresjs/core": "^4.3.6", "@vueuse/core": "^13.5.0", "axios": "^1.6.2", "gsap": "^3.12.5", "mitt": "^3.0.1", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.4.1", "three": "^0.178.0", "tweakpane": "^4.0.4", "vite-plugin-vuetify": "^2.1.1", "vue": "^3.2.45", "vue-i18n": "^11.1.9", "vue-router": "^4.1.6", "vuetify": "^3.9.4"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.0", "eslint": "^9.30.1", "eslint-plugin-vue": "^10.3.0", "vite": "^7.0.1", "vite-plugin-vue-devtools": "^7.7.7"}}