@screen md {

  .order-nr {
    @apply w-32;
  }

  .order-date {
    @apply w-36;
  }

  .order-status-column {
    @apply w-40;
  }
}


/*********************
 * ORDER STATUS COLORS
 *********************/

.order-status {
  border-right-width: 12px;
  border-right-style: solid;
}

.order-status.tendersend {
  color: #00A85D;
  border-color: #00A85D;
}

.order-status.open,
.order-status.ordered {
  color: #0091BD;
  border-color: #0091BD;
}

.order-status.check {
  color: #5F5F5F;
  border-color: #5F5F5F;
}

.order-status.topay {
  color: #B31317;
  border-color: #B31317;
}

.order-status.tosend,
.order-status.packing_slip_printed {
  color:        #baba43;
  border-color: #baba43;
}

.order-status.cancelled {
  color: #808080;
  border-color: #808080;
}

.order-status.paid {
  color: #4250B9;
  border-color: #4250B9;
}

.order-status.send {
  color: #187A6D;
  border-color: #187A6D;
}

.order-status.paybefore {
  color: #E39101;
  border-color: #E39101;
}

.order-status.backorder {
  color: #A1002A;
  border-color: #A1002A;
}

.order-status.retour {
  color: #5A009C;
  border-color: #5A009C;
}

.order-status.transportwithsystem {
  color: #CC33CCFF;
  border-color: #5A009C;
}