<?php

  namespace domain\visualizer;

  use DbHelper;
  use domain\visualizer\service\VisualizerService;
  use RequestHelper;
  use ResponseHelper;

  trait VisualizerSharedActions {
    public function executeGetDrawingById(): void {
      VisualizerService::getDrawingById();
    }

    public function executeGetProductDetails(): void {
      $productId = $_GET['product_id'] ?? null;
      $machineNr = $_GET['machine_nr'] ?? null;
      VisualizerService::getProductDetails($productId, $machineNr);
    }

    public function executeGetParts(): void {
      $input = RequestHelper::getInputFileContents();
      $whereClause = DbHelper::getSqlIn('part_nr', $input->partNumbers);
      $parts = VisualizerService::getParts($whereClause);
      ResponseHelper::exitAsJson($parts);
    }
  }