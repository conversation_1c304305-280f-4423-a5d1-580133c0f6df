let link_confirm_modal_template = `
  <a id="a-link">
    <slot name="link-text"></slot>
  </a> 
  <basic-modal>
    <span slot="title"><slot name="title"></slot></span>
    <slot></slot>
    
    <div class="text-center mt-5">
      <button class="btn btn-secondary mx-1" id="close-modal">
        <slot name="cancel-btn"></slot>
      </button>
      <button class="btn mx-1" id="confirm-link">
        <slot name="confirm-btn"></slot>
      </button>
    </div>
  </basic-modal>
  <style>
    a#a-link {
      display: inline-block;
    }
  </style>
  `;


class LinkConfirmModal extends HTMLElement {
  constructor() {
    super();
    this.shadow       = this.attachShadow({mode: "open"});
    this.main_css_url = main_css_url;
    this.clickLink    = this.clickLink.bind(this);
    this.close    = this.close.bind(this);
    this.confirm    = this.confirm.bind(this);
  }

  static get observedAttributes() {
    return ['href', 'title'];
  }

  // watch attribute changes
  attributeChangedCallback(attrName, oldValue, newValue) {
    if (oldValue !== newValue) {
      this[attrName] = this.hasAttribute(attrName);
    }
  }

  connectedCallback() {
    this.shadowRoot.innerHTML = link_confirm_modal_template;
    // add global styling to shadow root
    let link_main_css         = document.createElement('link');
    link_main_css.rel         = 'stylesheet';
    link_main_css.href        = main_css_url;
    this.shadowRoot.appendChild(link_main_css);

    this.shadowRoot.getElementById('a-link').addEventListener('click', this.clickLink);
    this.shadowRoot.getElementById('close-modal').addEventListener('click', this.close);
    this.shadowRoot.getElementById('confirm-link').addEventListener('click', this.confirm);
  }

  disconnectedCallback() {
    this.shadowRoot.getElementById('a-link').removeEventListener('click', this.clickLink);
    this.shadowRoot.getElementById('close-modal').removeEventListener('click', this.close);
    this.shadowRoot.getElementById('confirm-link').removeEventListener('click', this.confirm);
  }

  get href() {
    return this.getAttribute('href');
  }

  set href(link) {}

  get title() {
    return this.getAttribute('title');
  }

  set title(title) {}

  clickLink() {
    let link_confirm_modal  = this.shadowRoot.querySelector('basic-modal');
    link_confirm_modal.open = true;
  }

  confirm() {
    window.location = this.href;
  }

  close() {
    let link_confirm_modal  = this.shadowRoot.querySelector('basic-modal');
    link_confirm_modal.open = false;
  }
}

window.customElements.define("link-confirm-modal", LinkConfirmModal);