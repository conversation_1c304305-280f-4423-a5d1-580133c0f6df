let popout_box_template = `
  <div class="relative">
      <a id="trigger-popout" class="inline-block">
        <slot name="trigger"></slot>
      </a>
      <div id="popout-content" role="menu" aria-orientation="vertical" aria-labelledby="menu-button" tabindex="-1">
        <slot name="popout-content"></slot>
      </div>
  </div>
  `;


class PopoutBox extends HTMLElement {
  constructor() {
    super();
    this.shadow       = this.attachShadow({mode: "open"});
    this.main_css_url = main_css_url;
    this.clickTrigger = this.clickTrigger.bind(this);
  }

  connectedCallback() {
    this.shadowRoot.innerHTML = popout_box_template;
    // add global styling to shadow root
    let link_main_css         = document.createElement('link');
    link_main_css.rel         = 'stylesheet';
    link_main_css.href        = main_css_url;
    this.shadowRoot.appendChild(link_main_css);

    this.shadowRoot.getElementById('trigger-popout').addEventListener('click', this.clickTrigger);
  }

  disconnectedCallback() {
    this.shadowRoot.getElementById('trigger-popout').removeEventListener('click', this.clickTrigger);
  }

  clickTrigger() {
    this.shadowRoot.getElementById('popout-content').classList.toggle('open');
  }

}

window.customElements.define("popout-box", PopoutBox);