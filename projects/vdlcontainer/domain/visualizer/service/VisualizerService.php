<?php

  namespace domain\visualizer\service;
  use DataFile;
  use DBConn;
  use <PERSON>b<PERSON>elper;
  use Exception;
  use Machine;
  use MachineProduct;
  use Product;
  use PageMap;
  use ProductContent;
  use ResponseHelper;
  use SiteHost;
  use stdClass;
  use User;
  use VisualDrawing;
  use VisualDrawingContent;
  use VisualDrawingVisualPart;
  use VisualPart;
  use VisualPartContent;

  class VisualizerService {

    public static function saveGlbFile($drawing_id, string $temp_file_path): bool {
      if (!file_exists($temp_file_path) || !is_file($temp_file_path)) return false;

      $file_content = file_get_contents($temp_file_path);
      if (!$file_content) return false;

      $compressed_content = gzencode($file_content, 9);
      if (!$compressed_content) return false;

      $dataFile = self::getDataFile();
      $result = $dataFile->writeFile($drawing_id . '.glb.gz', $compressed_content);

      if ($result && file_exists($temp_file_path)) {
        unlink($temp_file_path);
      }

      return $result;
    }
    
    public static function deleteGlbFiles($drawing_id): bool {
      $dataFile = self::getDataFile();
      $success = true;

      $file = $drawing_id . '.glb.gz';

      $models_dir = DIR_UPLOADS . 'models/';

      // Remove file if exists
      if (file_exists($models_dir . $file)) {
        $success &= $dataFile->removeFile($file);
      }

      return $success;
    }
    
    public static function createDrawingWithContent($original_filename): VisualDrawing {
      $visual_drawing = new VisualDrawing();
      $visual_drawing->save();

      self::createDrawingContent($visual_drawing->id, $original_filename);

      return $visual_drawing;
    }
    
    public static function createDrawingContent($drawing_id, $original_filename): void {
      $site_languages = array_values(SiteHost::getSitelanguages($_SESSION['site']));
      $drawing_name = pathinfo($original_filename, PATHINFO_FILENAME);

      foreach ($site_languages as $language) {
        $drawing_content = new VisualDrawingContent();
        $drawing_content->visual_drawing_id = $drawing_id;
        $drawing_content->locale = $language;
        $drawing_content->name = $drawing_name;
        $drawing_content->save();
      }
    }
    
    public static function deleteDrawingCompletely($drawing_id): bool {
      $visual_drawing = VisualDrawing::find_by_id($drawing_id);
      if (!$visual_drawing) return false;

      // Delete files first
      self::deleteGlbFiles($drawing_id);

      // Delete drawing record (this will cascade to content via foreign keys)
      $visual_drawing->destroy();

      return true;
    }

    public static function getDrawingById(): void {
      $response = new stdClass();
      try {
        $drawing = VisualDrawing::find_by_id($_GET['id']);
        if (!$drawing) throw new Exception('Drawing not found.');

        $file = VisualizerService::getDataFile()->getFileContent($_GET['id'] . '.glb.gz');
        if (!$file) throw new Exception('Drawing file not found.');

        $contents = VisualDrawingContent::find_all_by(['visual_drawing_id' => $drawing->id]);
        $response->contents = $contents;

        $response->buffer = base64_encode($file);
        $response->drawing = $drawing;

        // Include linked machine data
        $machine = Machine::find_by(['visual_drawing_id' => $drawing->id]);
        $response->machine = $machine;

        $response->success = 1;
      } catch (Exception $e) {
        $response->message = 'An error occurred: ' . $e->getMessage();
      } finally {
        ResponseHelper::exitAsJson($response);
      }
    }

    public static function getProductDetails($productId, $machineNr): void {
      $response = new stdClass();
      try {
        $product = Product::find_by_id($productId);
        if (!$product) throw new Exception('Product not found.');

        $product->price_bruto = getLocalePrice($product->getBasketPricePart());
        $product->price_netto = getLocalePrice($product->getWebshopPriceNetto());
        $product->discount = $product->getWebshopDiscount();
        $product->stockHtml = $product->getWebshopStock();
        $product->requestLink = PageMap::getUrl('M_WEBSHOP_CONTACT', [
          'price_request' => $product->id,
        ]);
        $machineProduct = MachineProduct::find_by([
          'product_id' => $product->id,
          'machine_order_nr' => $machineNr,
        ]);
        $product->product_amount = $machineProduct?->amount ?? 0;
        $response->product = $product;
        $response->success = 1;
      } catch (Exception $e) {
        $response->message = 'An error occurred: ' . $e->getMessage();
      } finally {
        ResponseHelper::exitAsJson($response);
      }
    }

    private static function getPartsOfDrawing($drawing): array {
      $vdvp = VisualDrawingVisualPart::getTablename();
      $vp = VisualPart::getTablename();
      $id = DbHelper::escape($drawing->id);

      $whereClause = "$vp.id IN (SELECT visual_part_id FROM $vdvp WHERE visual_drawing_id = $id)";
      return self::getParts($whereClause);
    }

    public static function getParts($whereClause): array {
      $vp = VisualPart::getTablename();
      $vpc = VisualPartContent::getTablename();
      $p = Product::getTablename();
      $pc = ProductContent::getTablename();
      $lang = DbHelper::escape($_SESSION['lang']);

      $query = <<<SQL
        SELECT $p.*,
               $pc.name as product_name,
               $vp.id as part_id, $vp.part_nr, $vp.has_children, $vp.product_id,
               $vpc.name as part_name
        FROM $vp
        LEFT JOIN $vpc ON $vp.id = $vpc.visual_part_id AND $vpc.locale = '$lang'
        LEFT JOIN $p ON $p.id = $vp.product_id
        LEFT JOIN $pc ON $p.id = $pc.product_id AND $pc.locale = '$lang'
        WHERE $whereClause
      SQL;
      return self::processPartsResult($query);
    }

    private static function processPartsResult($query): array {
      $result = DBConn::db_link()->query($query);
      $parts = [];

      while ($row = $result->fetch_row()) {
        $columnCounter = 0;
        $product = new Product();
        $product->hydrateNext($row, $columnCounter);
        $product->name = $row[$columnCounter++];
        $product->thumbnail = $product->getMainUrlThumbForShop($_SESSION['site']);

        $part = (object) [
          'id' => $row[$columnCounter++],
          'part_nr' => $row[$columnCounter++],
          'has_children' => (bool) $row[$columnCounter++],
          'product_id' => $row[$columnCounter++],
          'name' => $row[$columnCounter++] ?? '',
          'product' => $product->id ? $product : null,
        ];
        $parts[] = $part;
      }
      return $parts;
    }

    private static function getDataFile(): DataFile {
      $dataFile = new DataFile();
      $dataFile->setDir(DIR_UPLOADS . 'models/');
      return $dataFile;
    }

    public static function getConfigFromSession(): stdClass {
      $config = new stdClass();

      $config->role = 'user';
      $config->lang = $_SESSION['lang'];

      $group = $_SESSION['userObject']->usergroup;
      if ($group == User::USERGROUP_ADMIN || $group == User::USERGROUP_SUPERADMIN) $config->role = 'admin';

      try {
        $config->basketUrl = PageMap::getUrl('M_BASKET');
      } catch (Exception) {
        $config->basketUrl = '';
      }
      $config->siteLangs = array_values(SiteHost::getSitelanguages($_SESSION['site']));
      return $config;
    }
  }