<?php
  include("gsdfw/includes/headercode_backend.inc.php");
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="<?php echo $_SESSION['lang'] ?>" xml:lang="<?php echo $_SESSION['lang'] ?>">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
	<meta http-equiv="expires" content="-1" />
	<meta http-equiv="pragma" content="no-cache" />
  <title><?php echo $seo_title ?></title>
  <meta name="description" content="<?php echo strip_tags(escapeForInput($seo_description)) ?>" />

  <link rel="shortcut icon" href="<?php echo $site->getTemplateUrl() ?>images/favicon.ico" type="image/x-icon" />

  <link href="//fonts.googleapis.com/css?family=Open+Sans:300,400,600,700" rel="stylesheet" type="text/css" />
  <link href="//fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet" />

  <script type="text/javascript" src="//cdn.jsdelivr.net/es6-promise/latest/es6-promise.auto.min.js"></script>

  <?php echo TemplateHelper::includeJavascript($site->getTemplateUrl() . 'dist/deploy', true); ?>
  <?php echo TemplateHelper::includeStylesheet($site->getTemplateUrl() . 'dist/main', true); ?>
  <?php echo TemplateHelper::includeStylesheet($site->getTemplateUrl() . 'style/vdl_backend'); ?>

  <?php echo TemplateHelper::includeJavascript('/gsdfw/includes/jsscripts/general', true); ?>

  <?php if(file_exists($site->getTemplateDir().'js/project.js')): ?>
    <?php echo TemplateHelper::includeJavascript($site->getTemplateUrl() . 'js/project'); ?>
  <?php endif; ?>

  <?php Context::printJavascripts() ?>
  <?php Context::printStylesheets() ?>

  <script type="text/javascript">

      $(document).ready(function() {

        $("#navmenu-h li").hover(
            function() { $(this).addClass("iehover"); },
            function() { $(this).removeClass("iehover");
        });


				$("#project_icon").click(function() {
					$("#project_messages,#project_messages_but").show();
				});

				var mouse_is_inside=false;
				$('.iconmessagebut,.iconmessage').hover(function(){
					mouse_is_inside=true;
				}, function(){
					mouse_is_inside=false;
				});

				$("body").mouseup(function(){
					if(!mouse_is_inside) $('.iconmessagebut,.iconmessage').hide();
				});

    	});

			<?php if(isset($_SESSION['userObject'])): ?>
				$(document).ready(function() {
					$("#return2admin").click(function(event){
						event.preventDefault();
						return2admin();
					});
				});
				function return2admin() {
          $.getJSON("<?php echo reconstructQueryAdd(['pageId' => 'M_AJAX'])?>inner_action=return2admin").done(
            function (data) {
							if (data != 'false') {
								window.location = data;
							}
						}
					);
				}
			<?php endif; ?>
  </script>
  <?php if(!DEVELOPMENT): ?>
    <!-- Global site tag (gtag.js) - Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-HX5WKCBSC2"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-HX5WKCBSC2');

      (function(h,o,t,j,a,r){
        h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
        h._hjSettings={hjid:3212956,hjsv:6};
        a=o.getElementsByTagName('head')[0];
        r=o.createElement('script');r.async=1;
        r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
        a.appendChild(r);
      })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');

    </script>
  <?php endif; ?>
</head>
<body class="<?php echo strtolower($current_action->getPageId()) ?>">

  <?php TemplateHelper::writeStagingMessage() ?>

  <?php if($current_action->template_wrapper_only==true): ?>

    <?php MessageFlashCoordinator::showMessages() ?>
    <?php include($current_action->getTemplatePath()); ?>

  <?php else: ?>

    <div id="head">
      <div class="main-container">

        <a href="/" id="logolink">
          <img src="<?php echo $site->getTemplateUrl() ?>images/vdl-logo.png" width="100" style="height: auto;" />
          <div class="company-name">
            VDL Container Systems
          </div>

        </a>

        <?php if(!empty($_SESSION['userObject']->organisation->logo_thumb)): ?>
          <div class="user-logo">
            <img src="<?php echo URL_UPLOADS.'logo/'.$_SESSION['userObject']->organisation->logo_orgin ?>" />
          </div>
        <?php endif; ?>

        <div style="float: right;">
           <div id="topsearch"  style="<?php if(Privilege::hasRight('GLOBAL_ADMIN')):?>padding-right: 10px;<?php endif; ?>">
             <div id="languages" style="<?php if(Privilege::hasRight('GLOBAL_ADMIN')):?>padding-right: 0;<?php endif; ?>">
              <?php
              $vals = array();
              foreach($site->site_host->getPossibleLangs() as $llang):
                $vals[] = '<a href="/'.$llang.'/" class="'.($_SESSION['lang']==$llang?'active':'').'"><img src="'.$site->getTemplateUrl().'/images/'.$llang.'.svg" /></a>';
              endforeach;
              echo implode($vals);
              ?>
            </div>
             <?php if(isset($_SESSION['loggedIn']) && $_SESSION['loggedIn'] && isset($_SESSION['userObject']) && $_SESSION['userObject'] != ""): ?>
            <?php if(Privilege::hasRight('M_PRODUCTS')): ?>
            <form method="post" action="<?php echo PageMap::getUrl('M_PRODUCTS') ?>">
              <input type="text" value="<?php if(isset($searchval)) echo $searchval; ?>" name="topsearch" placeholder="<?php echo __("Zoek product"); ?>..."/>
              <input type="submit" name="topsearchgo" value="<?php echo __("Zoek");?>" />
            </form>
            <?php endif; ?>
            <?php endif; ?>
           </div>
           <?php
            if(isset($_SESSION['was_admin']) && $_SESSION['was_admin'] != ""): ?>
              <div style="float:right; padding: 7px 0 0 0;">
                <a href="#return2admin" id="return2admin" title="TERUG NAAR UW ACCOUNT">
                  TERUG NAAR ACCOUNT
                  <?php echo IconHelper::getLogin() ?>
                </a>
             </div>
            <?php endif; ?>
          <br>
           <div id="topmenu">
             <?php if(isset($_SESSION['userObject'])): ?>

               <div style="">
                 <span class="name"><?php echo $_SESSION['userObject']->getNaam(); ?> - <?php echo __(User::getInternalUsergroupDesc($_SESSION['userObject']->usergroup)) ?></span>&nbsp;
                 |
                 <a href="<?php echo PageMap::getUrl('M_SETTINGS_PERS') ?>">
                   <?php echo PageMap::getName('M_SETTINGS_PERS') ?>
                 </a> |
                 <?php if(isset($_SESSION['basket']) && isset($_SESSION['basket']['products']) && Privilege::hasRight('M_BASKET')): ?>
                 <a href="<?php echo PageMap::getUrl('M_BASKET') ?>" style=""><?php echo PageMap::getName('M_BASKET') ?> (<?php echo count($_SESSION['basket']['products']) ?>)</a> |
                 <?php elseif(Privilege::hasRight('M_BASKET')): ?>
                 <a href="<?php echo PageMap::getUrl('M_BASKET') ?>"><?php echo PageMap::getName('M_BASKET') ?></a> |
                 <?php endif; ?>
                 <a href="<?php echo PageMap::getUrl('M_LOGOFF') ?>"><?php echo PageMap::getName('M_LOGOFF') ?></a>
               </div>
             <?php endif; ?>
           </div>
        </div>

      </div>
    </div>

    <?php if(isset($_SESSION['userObject'])): ?>

    <div id="headmenu">
      <div class="main-container">
      <?php if(Navigation::getInstance()->getWritenav($pageId)==true): ?>
        <div id="navmenu-h-container"><?php echo Navigation::getBackendNavigationHtml() ?></div>
      <?php endif; ?>
      </div>
    </div>
    <?php endif; ?>

    <div class="main-container">
      <?php if(Navigation::getInstance()->getWritenav($pageId)==true): ?>

        <?php echo BreadCrumbs::writeBreadcrumbs('',false,'', false, true) ?>

        <div class="content <?php if(isset($showleftmenu) && $showleftmenu) echo 'has_leftmenu'; ?>">

          <?php
            if(isset($showleftmenu)):
              TemplateHelper::includeBackendIndexPartial("_leftmenu.php", $current_action);
            endif;
          ?>

          <div class="div_content">

            <?php MessageFlashCoordinator::showMessages() ?>
            <?php include($current_action->getTemplatePath()); ?>
          </div>

        </div>

        <div id="footer">
          <div class="footermenu">
            VDL Container Systems bv | Industrieweg 21 | 5527 AJ Hapert | Nederland
            <?php if(Navigation::getInstance()->getWritenav($pageId)==true):  ?> |
              <a href="<?php echo $site->getTemplateUrl() ?>files/algemene-voorwaarden.pdf?v=2" target="_blank"><?php echo __("Algemene voorwaarden"); ?></a> |
              <a href="//www.vdlcontainersystems.com/nl/privacy-policy" target="_blank"><?php echo __("Privacy policy"); ?></a>
            <?php endif; ?>
          </div>
        </div>
        <div id="subfooter">
          vdldealer.com <?php echo __("is een product van");?> <a href="//www.vdlcontainersystems.com" target="_blank">VDL Container Systems bv</a> - <?php echo __("Op het gebruik van");?> vdldealer.com <?php echo __("zijn de algemene voorwaarden van toepassing");?> - Powered by <a href="https://www.gsd.nl" title="GSD - maatwerk software" target="_blank">GSD</a>
        </div>

      <?php else: ?>
        <?php MessageFlashCoordinator::showMessages(); ?>
        <?php include($current_action->getTemplatePath()); ?>
      <?php endif; ?>

    </div>
  <?php endif; ?>
</body>
</html>