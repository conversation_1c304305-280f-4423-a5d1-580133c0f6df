const node_modules_root = "../../../../gsdfw/tools/node_modules/";
const path = require("path");
const MiniCssExtractPlugin = require(node_modules_root + "mini-css-extract-plugin");
const HtmlWebpackPlugin = require(node_modules_root + "html-webpack-plugin");
const WebpackMd5Hash = require(node_modules_root + "webpack-md5-hash");
const {CleanWebpackPlugin} = require(node_modules_root + "clean-webpack-plugin");
const webpack = require(node_modules_root + "webpack");
const CssMinimizerPlugin = require(node_modules_root + "css-minimizer-webpack-plugin");
const TerserJSPlugin = require(node_modules_root + "terser-webpack-plugin"); // for minimizing JS

const DEVMODE = process.env.NODE_ENV !== 'production';
const PROJECT = 'vdlcontainer';
const DIR_GSDFW = '../../gsdfw/';
const TEMPLATE = 'dealerportal';
const DIR_RESOURCE = '../../projects/'+PROJECT+'/resources/'+TEMPLATE+'/';


module.exports = {
  optimization: {
    minimizer: [
      new TerserJSPlugin({}),
      new CssMinimizerPlugin({})
    ]
  },
  entry: [
    DIR_GSDFW + 'resources/default/backend/backend.js',
    DIR_GSDFW + 'includes/font-awesome/css/font-awesome.min.css',
    DIR_GSDFW + 'tools/node_modules/simple-line-icons/dist/styles/simple-line-icons.css',
    // DIR_GSDFW + 'includes/jsscripts/jqueryui/js/jquery-ui-1.10.4.custom.min.js',
    // DIR_GSDFW + 'includes/jsscripts/jqueryui/js/jquery.ui.datepicker-nl.js',
    // DIR_GSDFW + 'includes/jsscripts/jqueryui/css/custom-theme/jquery-ui-1.10.4.custom.min.css',
    // DIR_GSDFW + 'tools/node_modules/tablednd/dist/jquery.tablednd.min.js',
    // DIR_GSDFW + 'tools/node_modules/devbridge-autocomplete/dist/jquery.autocomplete.min.js',
    // DIR_GSDFW + 'tools/node_modules/flatpickr/dist/flatpickr.min.js',
    // DIR_GSDFW + 'tools/node_modules/flatpickr/dist/l10n/nl.js',
    // DIR_GSDFW + 'tools/node_modules/flatpickr/dist/flatpickr.min.css',
    // DIR_GSDFW + 'tools/node_modules/flatpickr/dist/themes/dark.css',
    // '../../resources/'+PROJECT+'/backend/style/main.css',
    DIR_RESOURCE + 'style/main.css',
  ],
  output: {
    filename: DEVMODE ? "deploy.js" : "deploy.min.js",
    path: path.resolve(__dirname, "../../../../projects/" + PROJECT + "/templates/" + TEMPLATE + "/dist/")
  },
  mode: process.env.NODE_ENV,
  module: {
    rules: [
      {
        test: /\.css$/,
        use: [
          {
            loader: MiniCssExtractPlugin.loader,
            options: {
              // you can specify a publicPath here
              // by default it use publicPath in webpackOptions.output
              publicPath: '../',
            },
          },
          {
            loader: 'css-loader',
            options: {
              importLoaders: 1,
            }
          },
          {
            loader: 'postcss-loader',
            options: {
              postcssOptions: {
                plugins: [
                  require(node_modules_root + 'postcss-import'),
                  require(node_modules_root + 'tailwindcss')('../../projects/' + PROJECT + '/resources/' + TEMPLATE + '/tailwind.config.js'),
                  require(node_modules_root + 'autoprefixer'),
                ],
              },
            },
          },
        ]
      },
      {
        test: /\.(gif|png|jpe?g|svg)$/i,
        type: 'asset/resource',
        generator: {
          publicPath: '/projects/' + PROJECT + '/templates/' + TEMPLATE + '/dist/',
        },
        use: [
          {
            loader: "image-webpack-loader",
            options: {
              bypassOnDebug: true
            }
          }
        ]
      },
      {
        test: /\.(woff(2)?|ttf|eot|otf)(\?v=\d+\.\d+\.\d+)?$/,
        type: 'asset/resource',
        generator: {
          publicPath: "/projects/" + PROJECT + "/templates/" + TEMPLATE + "/dist/fonts/",
          outputPath: "fonts/",
          filename: "[name][ext]"
        },
      },
      // {
      //   test: require.resolve(DIR_GSDFW + 'tools/jsscripts/general.js'),
      //   use: 'exports-loader?file,parse=helpers.parse'
      // }
    ]
  },
  plugins: [
    new CleanWebpackPlugin({
      cleanOnceBeforeBuildPatterns: ["**/*", "!**/main.min.css", "!**/main.css", "!**/deploy.min.js", "!**/deploy.js"],
    }),
    new MiniCssExtractPlugin({
      // Options similar to the same options in webpackOptions.output
      // both options are optional
      filename: DEVMODE ? "[name].css" : "[name].min.css",
      chunkFilename: DEVMODE ? "[id].css" : "[id].min.css",
    }),
  ],
};