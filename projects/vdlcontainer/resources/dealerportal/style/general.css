/*********************
 * PAGE BACKGROUNDS
 *********************/

body {
  -webkit-background-size: cover;
  background-size:         cover;
  background-attachment:   fixed;
  background-repeat:       no-repeat;
  background-position:     center center;
}

body.m_home {
  background-image: url('../images/pagebackgrounds/home.jpg?v=3');
}

body.m_webshop_machine_search {
  background-image: url('../images/pagebackgrounds/machine.jpg');
}

body.m_webshop_machine_view {
  background-image: url('../images/pagebackgrounds/machine2.jpg');
}

body.m_webshop_visualizer {
  background-image: url('../images/pagebackgrounds/machine2.jpg');
}

body.m_products {
  background-image: url('../images/pagebackgrounds/webshop.jpg');
}

body.m_webshop_order {
  background-image: url('../images/pagebackgrounds/offertes.jpg');
}

body.m_webshop_downloads {
  background-image: url('../images/pagebackgrounds/download.jpg');
}

body.m_webshop_contact,
body.m_webshop_faq {
  background-image: url('../images/pagebackgrounds/helpdesk.jpg');
}

body.m_basket {
  background-image: url('../images/pagebackgrounds/winkelwagen.jpg');
}

body.m_settings_pers,
body.m_settings_address {
  background-image: url('../images/pagebackgrounds/mijngegevens.jpg');
}



/*********************
 * BUTTONS
 *********************/

.btn {
  @apply relative inline-block cursor-pointer border-2 border-primary rounded text-primary py-2 px-3 transition duration-300 bg-white;
}

.btn:active {
  top: 2px;
}

.btn:hover {
  @apply bg-primary text-white no-underline;
}

.btn.btn-sm {
  @apply py-1 px-2;
}

.btn.btn-grey {
  @apply border-gray-600 text-gray-600;
}

.btn.btn-grey:hover {
  @apply bg-gray-600 text-white no-underline;
}

.btn.btn-primary {
  @apply bg-primary border-primary text-white;
}

.btn.btn-primary:hover {
  @apply bg-white border-primary text-primary;
}

.btn.btn-secondary {
  @apply border-gray-700 text-gray-700;
}

.btn.btn-secondary:hover {
  @apply bg-gray-700 text-white no-underline;
}

/*********************
 * Breadcrumb
 *********************/

.breadcrumb {
  @apply flex items-center mb-5 text-sm text-primary;
}

.breadcrumb ol {
  @apply flex flex-wrap;
}

.breadcrumb ol li {
  @apply ml-2;
}

.breadcrumb ol li:not(:first-child):before {
  @apply pr-2;
  content: '|';
}

.breadcrumb a:hover {
  color: black;
}

/*********************
 * DEFAULT PAGE STYLING
 *********************/
h1.page-title {
  @apply text-center mt-8;
}

div.content-with-sidebar {
  @apply flex mt-8 flex-wrap flex-col;
  @apply lg:flex-row;
}

div.content-container {
  @apply w-full lg:w-3/4;
}

/*********************
 * MESSAGES
 *********************/

.alert {
  @apply px-4 py-3 rounded relative mb-4;
}

.alert .fa {
  @apply hidden;
}

.alert.alert-danger {
  @apply bg-red-100 border border-red-300 text-red-700;
}

.alert.alert-success {
  @apply text-white;
  background: #38a169;
}

/*********************
 * POPOUT BOX
 *********************/

#popout-content {
  @apply origin-top-left absolute left-0 mt-2 w-72 shadow-lg bg-white text-lg border border-gray-100 hidden;
  transition: 300ms opacity;
  opacity:    0;
  z-index:    10000;
}

#popout-content.open {
  @apply block;
  opacity: 100;
}

/*********************
 * LIST MENU
 *********************/

.list-menu {
  @apply p-1;
}

.list-menu a {
  @apply block border-b border-gray-400 py-2 px-3;
}

.list-menu a:last-child {
  @apply border-b-0;
}

.list-menu a:hover {
  @apply bg-primary text-white;
}